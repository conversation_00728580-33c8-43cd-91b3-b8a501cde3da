import { screen, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { buildTxSwapEthereumUSDCUSDT } from '@zeal/domains/Currency/domains/SwapQuote/api/fixtures/buildTxSwapEthereumUSDCUSDT'
import { buildTxSwapPolygonUSDCMATIC } from '@zeal/domains/Currency/domains/SwapQuote/api/fixtures/buildTxSwapPolygonUSDCMATIC'
import {
    ********************************,
    swapQuoteOneInchPolygonUSDCMATIC,
    swapQuotePolygonMaticUSDCNoRoutes,
} from '@zeal/domains/Currency/domains/SwapQuote/api/fixtures/swapQuote'
import { Earn } from '@zeal/domains/Earn'
import { nonConfiguredEarn as mockNonConfiguredEarn } from '@zeal/domains/Earn/api/fixtures/fetchEarn'
import { testPassword } from '@zeal/domains/KeyStore/api/fixtures/testPassword'
import { emptyPortfolio } from '@zeal/domains/Portfolio/api/fixtures/emptyPortfolio'
import { swapPortfolio } from '@zeal/domains/Portfolio/api/fixtures/swapPortfolio'
import { ethBlockNumberAfterTransaction } from '@zeal/domains/RPCRequest/api/fixtures/ethBlockNumber'
import { ethGetTransactionByHashWithBlockNumber } from '@zeal/domains/RPCRequest/api/fixtures/ethGetTransactionByHash'
import { ethGetTransactionReceipt } from '@zeal/domains/RPCRequest/api/fixtures/ethGetTransactionReceipt'
import { onlyPKAccount } from '@zeal/domains/Storage/api/fixtures/localStorage'
import { LS_KEY } from '@zeal/domains/Storage/constants'

import { cleanEnv, mockEnv, TestEnvironment } from 'src/tests/env'
import { runLottieListeners } from 'src/tests/mocks/lottie'
import { renderPage } from 'src/tests/utils/renderers'

import { advanceLoadables } from './utils/advanceLoadables'

// TODO @manovikov-zeal :: find a way how to mock eth_call properly
jest.mock('@zeal/domains/Earn/api/fetchEarn', () => ({
    fetchEarn: async (): Promise<Earn> => mockNonConfiguredEarn,
}))

let env: TestEnvironment
jest.retryTimes(5)

let originalOpen: Window['open']

beforeEach(() => {
    env = mockEnv()
    originalOpen = window.open
})

afterEach(() => {
    cleanEnv(env)
    window.open = originalOpen
    jest.restoreAllMocks()
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user I should be able to open swap flow even is my portfolio is empty, so I can explore the feature
    As a user I should see the account label for which I want to swap, so I won't do swap on wrong account by mistake
    As a user I should not be able to continue swap if I'm not selected destination token, so I'll be motivated to select one
    As a user I should see an error if Zeal was not able to load swap providers, so I can retry
    As a user I should be able to select source token, so I can choose which token I want to swap from
    As a user I should be see a message that token I'm searching for is not found, so I know that this token is not supported yet
    As a user I should be able to search source and destination tokens, so I can quickly find the token I'm looking for
    As a user I should be able to set slippage from available options or provide custom, so I make sure my swap parameters are under control
    As a user I should be able to select destination token, so I can choose which token I want to swap to`, async () => {
    jest.useFakeTimers()
    env.chromeMocks.storages.session = {}
    env.chromeMocks.storages.local[LS_KEY] = JSON.stringify(onlyPKAccount)

    env.api['/wallet/portfolio/:address/'].get = jest.fn(() => [
        200,
        emptyPortfolio,
    ])

    renderPage(
        '/page_entrypoint.html?type=swap&fromAddress=******************************************'
    )

    await advanceLoadables()
    await advanceLoadables()
    await advanceLoadables()

    await userEvent.type(
        await screen.findByPlaceholderText('Enter password'),
        `${testPassword}{enter}`
    )

    // TODO @resetko-zeal why enter from typing password doesn't work and we need to wrap everything in advance loadables?
    await userEvent.click(
        await screen.findByRole('button', { name: 'Continue' })
    )

    await advanceLoadables()
    await advanceLoadables()
    await advanceLoadables()

    expect(await screen.findByText('0x26d0...8932')).toBeInTheDocument()
    expect(await screen.findByText('Private Key 1')).toBeInTheDocument()
    expect(await screen.findByText('Swap')).toBeInTheDocument()

    expect(
        await screen.findByRole('button', { name: 'Continue' })
    ).toBeDisabled()

    await userEvent.click(
        (await screen.findAllByRole('button', { name: 'Optimism' }))[0]
    )
    await userEvent.click(await screen.findByLabelText('Ethereum'))

    // From token
    await userEvent.click(await screen.findByRole('button', { name: 'ETH' }))
    let tokens = await screen.findByLabelText('Tokens')
    await userEvent.type(
        await within(tokens).findByPlaceholderText('Search'),
        'USDC'
    )
    await userEvent.click(await within(tokens).findByLabelText('USD Coin'))
    expect(tokens).not.toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'USDC' })
    ).toBeInTheDocument()

    // To token
    await userEvent.click(await screen.findByRole('button', { name: 'USDT' }))
    tokens = await screen.findByLabelText('Tokens')
    await userEvent.type(
        await within(tokens).findByPlaceholderText('Search'),
        'shitcoinboom'
    )
    expect(
        await within(tokens).findByText('We found no tokens')
    ).toBeInTheDocument()
    await userEvent.clear(await within(tokens).findByPlaceholderText('Search'))
    await userEvent.click(await within(tokens).findByLabelText('Tether USD'))
    expect(tokens).not.toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'USDT' })
    ).toBeInTheDocument()

    await userEvent.type(await screen.findByLabelText('Amount to swap'), '10')

    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        ********************************,
    ])
    env.rpcMocks.eth_call = jest.fn(() => [
        200,
        {
            jsonrpc: '2.0',
            result: '0x0000000000000000000000000000000000000000000000000000000000000000',
            id: **********,
        },
    ])
    env.socketApiMock.post['/build-tx'] = jest.fn(() => [
        200,
        buildTxSwapEthereumUSDCUSDT,
    ])

    expect(
        await screen.findByText('We had issues loading providers')
    ).toBeInTheDocument()
    await userEvent.click(await screen.findByRole('button', { name: 'Retry' }))

    expect(
        await screen.findByRole('button', { name: 'Not enough balance' })
    ).toBeDisabled()

    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.objectContaining({
            params: expect.objectContaining({
                userAddress: '******************************************',
                defaultSwapSlippage: '0.50',
                fromAmount: '10000000',
                fromChainId: '0x1',
                toChainId: '0x1',
                fromTokenAddress: '******************************************',
                toTokenAddress: '******************************************',
            }),
        })
    )

    await advanceLoadables()

    // Route
    expect(await screen.findByLabelText('Destination amount')).toHaveValue(
        '9.95'
    )
    expect(await screen.findByLabelText('1Inch')).toHaveAccessibleDescription(
        'Fees $0.04'
    )
    await userEvent.click(await screen.findByLabelText('1Inch'))
    const providerModal = await screen.findByLabelText('Swap provider')
    const oneInch = await within(providerModal).findByLabelText('1Inch')

    expect(await within(oneInch).findByText('9.95 USDT')).toBeInTheDocument()
    expect(await within(oneInch).findByText('Fees $0.04')).toBeInTheDocument()
    expect(
        await within(oneInch).findByLabelText('Best return route')
    ).toBeInTheDocument()
    await userEvent.click(
        await within(providerModal).findByRole('button', {
            name: 'Slippage 0.5%',
        })
    )
    await userEvent.click(
        await within(
            await screen.findByRole('dialog', { name: 'Slippage settings' })
        ).findByRole('radio', { name: '0.5%' })
    )

    // Slippage
    await userEvent.click(
        await screen.findByRole('button', { name: 'Slippage 0.5%' })
    )
    let slippageModal = await screen.findByRole('dialog', {
        name: 'Slippage settings',
    })
    expect(
        await within(slippageModal).findByRole('radio', { name: '0.1%' })
    ).toBeInTheDocument()
    expect(
        await within(slippageModal).findByRole('radio', { name: '0.5%' })
    ).toBeInTheDocument()
    await userEvent.click(
        await within(slippageModal).findByRole('radio', { name: '1%' })
    )
    expect(
        await screen.findByRole('button', { name: 'Not enough balance' })
    ).toBeDisabled()
    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.objectContaining({
            params: expect.objectContaining({ defaultSwapSlippage: '1.00' }),
        })
    )
    expect(
        JSON.parse((await chrome.storage.local.get(LS_KEY))[LS_KEY])
    ).toEqual(expect.objectContaining({ swapSlippagePercent: 1 }))
    await userEvent.click(
        await screen.findByRole('button', { name: 'Slippage 1%' })
    )
    slippageModal = await screen.findByRole('dialog', {
        name: 'Slippage settings',
    })
    expect(
        await within(slippageModal).findByRole('button', { name: 'Save' })
    ).toBeDisabled()
    await userEvent.type(
        await within(slippageModal).findByPlaceholderText('Custom'),
        '0.38{enter}'
    )

    expect(slippageModal).not.toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'Not enough balance' })
    ).toBeDisabled()
    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.objectContaining({
            params: expect.objectContaining({ defaultSwapSlippage: '0.38' }),
        })
    )
    expect(
        JSON.parse((await chrome.storage.local.get(LS_KEY))[LS_KEY])
    ).toEqual(expect.objectContaining({ swapSlippagePercent: 0.38 }))

    jest.useRealTimers()
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user I should be able to open swap with source token specified, so I can swap quicker
    As a user I should be able to switch network on swap, so I can swap on different networks
    As a user I should be able to submit swap, so my swap will be sent to RPC`, async () => {
    jest.useFakeTimers()
    env.chromeMocks.storages.session = {}
    env.chromeMocks.storages.local[LS_KEY] = JSON.stringify(onlyPKAccount)

    env.api['/wallet/portfolio/:address/'].get = jest.fn(() => [
        200,
        swapPortfolio,
    ])

    env.rpcMocks.eth_getTransactionByHash = jest.fn(() => [
        200,
        ethGetTransactionByHashWithBlockNumber,
    ])
    env.rpcMocks.eth_getTransactionReceipt = jest.fn(() => [
        200,
        ethGetTransactionReceipt,
    ])
    env.rpcMocks.eth_blockNumber = jest.fn(() => [
        200,
        ethBlockNumberAfterTransaction,
    ])
    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        swapQuotePolygonMaticUSDCNoRoutes,
    ])
    env.rpcMocks.eth_call = jest.fn(() => [
        200,
        {
            jsonrpc: '2.0',
            result: '0x0000000000000000000000000000000000000000000000000000000000000000',
            id: **********,
        },
    ])
    env.socketApiMock.post['/build-tx'] = jest.fn(() => [
        200,
        buildTxSwapPolygonUSDCMATIC,
    ])

    renderPage(
        '/page_entrypoint.html?type=swap&fromAddress=******************************************&fromCurrencyId=Polygon%7C0x2791bca1f2de4661ed88a30c99a7a9449aa84174'
    )

    await advanceLoadables()
    await advanceLoadables()
    await advanceLoadables()

    await userEvent.type(
        await screen.findByPlaceholderText('Enter password'),
        `${testPassword}{enter}`
    )

    // TODO @resetko-zeal why enter from typing password doesn't work and we need to wrap everything in advance loadables?
    await userEvent.click(
        await screen.findByRole('button', { name: 'Continue' })
    )

    await advanceLoadables()
    await advanceLoadables()
    await advanceLoadables()

    expect(await screen.findByText('0x26d0...8932')).toBeInTheDocument()
    expect(await screen.findByText('Private Key 1')).toBeInTheDocument()
    expect(await screen.findByText('Swap')).toBeInTheDocument()

    expect(
        await screen.findByRole('button', { name: 'Continue' })
    ).toBeDisabled()

    expect(
        await screen.findByRole('button', { name: 'USDC.e' })
    ).toBeInTheDocument()
    await userEvent.click(await screen.findByRole('button', { name: 'USDC' }))
    let tokens = await screen.findByLabelText('Tokens')
    await userEvent.click(await within(tokens).findByLabelText('POL'))
    expect(tokens).not.toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'POL' })
    ).toBeInTheDocument()

    // Switch to Arbitrum
    const [fromNetwork] = await screen.findAllByRole('button', {
        name: 'Polygon',
    })
    await userEvent.click(fromNetwork)
    await userEvent.click(
        await screen.findByRole('button', { name: 'Arbitrum' })
    )
    expect(
        await screen.findByRole('button', { name: 'ETH' })
    ).toBeInTheDocument()

    // Back to Polygon
    await userEvent.click(await screen.findByRole('button', { name: 'USDC' }))
    tokens = await screen.findByLabelText('Tokens')
    await userEvent.click(
        await within(tokens).findByTestId('tokens-network-filter-button')
    )
    await userEvent.click(
        await screen.findByRole('button', { name: 'Polygon' })
    )
    expect(tokens).not.toBeInTheDocument()

    // Set tokens
    await userEvent.click(await screen.findByRole('button', { name: 'USDC' }))
    tokens = await screen.findByLabelText('Tokens')
    await userEvent.click(
        await within(tokens).findByRole('button', { name: 'POL' })
    )
    expect(tokens).not.toBeInTheDocument()

    // Try no routes
    await userEvent.type(await screen.findByLabelText('Amount to swap'), '1')
    expect(
        await screen.findByRole('button', { name: 'No routes found' })
    ).toBeDisabled()

    // Above available balance and full balance
    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        swapQuoteOneInchPolygonUSDCMATIC,
    ])
    await userEvent.type(await screen.findByLabelText('Amount to swap'), '160')
    expect(
        await screen.findByRole('button', { name: 'Not enough balance' })
    ).toBeDisabled()
    await userEvent.click(
        await screen.findByRole('button', { name: 'Max: 157.22' })
    )
    expect(await screen.findByLabelText('Amount to swap')).toHaveValue(
        '157.223311'
    )
    expect(await screen.findByText('$157.22')).toBeInTheDocument()
    expect(await screen.findByLabelText('1Inch')).toHaveAccessibleDescription(
        'Fees $0.36'
    )
    expect(await screen.findByLabelText('Destination amount')).toHaveValue(
        '609.24'
    )

    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.stringContaining(
            [
                'fromChainId=0x89',
                'toChainId=0x89',
                'fromTokenAddress=0x2791bca1f2de4661ed88a30c99a7a9449aa84174',
                'toTokenAddress=0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee',
                'fromAmount=157223311',
                'userAddress=******************************************',
                'recipient=******************************************',
                'sort=output',
                'defaultSwapSlippage=0.50',
                'defaultBridgeSlippage=0.50',
                'isContractCall=false',
            ].join('&')
        ),
        expect.anything()
    )

    const submitButton = await screen.findByRole('button', { name: 'Continue' })
    expect(submitButton).not.toBeDisabled()

    await userEvent.click(submitButton)
    expect(await screen.findByText('Doing safety checks…')).toBeInTheDocument()
    await advanceLoadables()
    await runLottieListeners()

    // Approval transaction
    await userEvent.click(await screen.findByRole('button', { name: 'Submit' }))

    await advanceLoadables()

    await userEvent.click(await screen.findByRole('button', { name: 'Close' }))
    expect(await screen.findByText('Doing safety checks…')).toBeInTheDocument()
    await advanceLoadables()
    await runLottieListeners()

    // Swap transaction
    await userEvent.click(await screen.findByRole('button', { name: 'Submit' }))

    await advanceLoadables()
    await runLottieListeners()

    await userEvent.click(await screen.findByRole('button', { name: 'Close' }))

    await advanceLoadables()
    await runLottieListeners()

    expect(
        await screen.findByRole('button', { name: 'Portfolio' })
    ).toBeInTheDocument()

    jest.useRealTimers()
})
