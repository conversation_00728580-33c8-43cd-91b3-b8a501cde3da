import { screen, within } from '@testing-library/react'

import { Earn } from '@zeal/domains/Earn'
import { nonConfiguredEarn as mockNonConfiguredEarn } from '@zeal/domains/Earn/api/fixtures/fetchEarn'

import { cleanEnv, mockEnv, TestEnvironment } from 'src/tests/env'
import { renderPage } from 'src/tests/utils/renderers'
jest.retryTimes(3)

// TODO @manovikov-zeal :: find a way how to mock eth_call properly
jest.mock('@zeal/domains/Earn/api/fetchEarn', () => ({
    fetchEarn: async (): Promise<Earn> => mockNonConfiguredEarn,
}))

let env: TestEnvironment

beforeEach(() => {
    env = mockEnv()
})

afterEach(() => {
    cleanEnv(env)
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user I should be able to see my tokens and their balances when I load extension, so I can do actions with them accordingly
    As a user I should be able to see price changes of my tokens for last 24h, so I can plan my actions using this information`, async () => {
    await renderPage('/index.html?type=extension&mode=popup')

    const assetsWidget = await screen.findByLabelText('Assets', {
        exact: false,
    })

    const [ethereumEth] = await within(assetsWidget).findAllByLabelText('ETH')

    expect(await within(ethereumEth).findByText('0.01971')).toBeInTheDocument()
    expect(await within(ethereumEth).findByText('$54.86')).toBeInTheDocument()
    expect(await within(ethereumEth).findByText('$2,783')).toBeInTheDocument()
})
