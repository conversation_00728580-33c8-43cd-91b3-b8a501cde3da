import { screen, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { serialize } from '@zeal/toolkit/Storage'

import {
    bridgeQuoteArbitrumUSDCEBaseUSDbC,
    bridgeQuotePolygonUSDCArbitrumUSDT,
    bridgeQuotePolygonUSDCArbitrumUSDTRefuel,
} from '@zeal/domains/Currency/domains/Bridge/api/fixtures/bridgeQuote'
import {
    bridgeStatusPolygonUSDCArbitrumUSDTRefuelCompleted,
    bridgeStatusPolygonUSDCArbitrumUSDTRefuelPendingAll,
    bridgeStatusPolygonUSDCArbitrumUSDTRefuelPendingRefuel,
} from '@zeal/domains/Currency/domains/Bridge/api/fixtures/bridgeStatus'
import {
    buildTxEthereumUSDCPolygonUSDT as buildTxArbitrumUSDCeBaseUSDbC,
    buildTxPolygonUSDCArbitrumUSDT,
} from '@zeal/domains/Currency/domains/Bridge/api/fixtures/buildTx'
import { swapQuotePolygonMaticUSDCNoRoutes } from '@zeal/domains/Currency/domains/SwapQuote/api/fixtures/swapQuote'
import { Earn } from '@zeal/domains/Earn'
import { nonConfiguredEarn as mockNonConfiguredEarn } from '@zeal/domains/Earn/api/fixtures/fetchEarn'
import { testPassword } from '@zeal/domains/KeyStore/api/fixtures/testPassword'
import { emptyPortfolio } from '@zeal/domains/Portfolio/api/fixtures/emptyPortfolio'
import { basePortfolioWithUSDCe } from '@zeal/domains/Portfolio/api/fixtures/portfolio'
import { ethBlockNumberAfterTransaction } from '@zeal/domains/RPCRequest/api/fixtures/ethBlockNumber'
import { ethGetTransactionByHashWithBlockNumber } from '@zeal/domains/RPCRequest/api/fixtures/ethGetTransactionByHash'
import { ethGetTransactionReceipt } from '@zeal/domains/RPCRequest/api/fixtures/ethGetTransactionReceipt'
import {
    emptyPortfolioMap,
    onlyPKAccount,
    pendingBridge,
    portfolioMap,
} from '@zeal/domains/Storage/api/fixtures/localStorage'
import { LS_KEY, PORTFOLIO_MAP_KEY } from '@zeal/domains/Storage/constants'

import { cleanEnv, mockEnv, TestEnvironment } from 'src/tests/env'
import { runLottieListeners } from 'src/tests/mocks/lottie'
import { renderPage } from 'src/tests/utils/renderers'

import { advanceLoadables } from './utils/advanceLoadables'
import { advanceTimers } from './utils/advanceTimers'

jest.retryTimes(3)

// TODO @manovikov-zeal :: find a way how to mock eth_call properly
jest.mock('@zeal/domains/Earn/api/fetchEarn', () => ({
    fetchEarn: async (): Promise<Earn> => mockNonConfiguredEarn,
}))

let env: TestEnvironment

let originalOpen: Window['open']

beforeEach(() => {
    env = mockEnv()
    originalOpen = window.open
})

afterEach(() => {
    cleanEnv(env)
    window.open = originalOpen
    jest.restoreAllMocks()
})

// TODO @resetko-zeal this is perfect example of simpliest test which does not work durrounded with fake timers, reloadable data breaks no matter what
// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user I should be able to see bridge which is in progress even after closing the bridge flow, so I can continue monitoring it`, async () => {
    env.chromeMocks.storages.session = {}
    env.chromeMocks.storages.local[LS_KEY] = JSON.stringify(pendingBridge)

    await renderPage('/index.html?type=extension&mode=popup')

    await userEvent.type(
        await screen.findByPlaceholderText('Enter password'),
        `${testPassword}{enter}`
    )

    expect(await screen.findByText('Bridge')).toBeInTheDocument()
    expect(await screen.findByText('USDC to USDT')).toBeInTheDocument()
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user I should be able to open bridge flow even is my portfolio is empty, so I can explore the feature
    As a user I should see the account label from which I want to bridge, so I won't do it on wrong account by mistake
    As a user I should see an error if Zeal was not able to load bridge providers, so I can retry
    As a user I should be able to select source token, so I can choose which token I want to bridge from
    As a user I should be see a message that token I'm searching for is not found, so I know that this token is not supported yet
    As a user I should be able to search source and destination tokens, so I can quickly find the token I'm looking for
    As a user I should be able to set slippage from available options or provide custom, so I make sure my swap parameters during bridge are under control
    As a user I should be able to select destination token, so I can choose which token I want to swap to`, async () => {
    env.chromeMocks.storages.session = {}
    env.chromeMocks.storages.local[LS_KEY] = JSON.stringify(onlyPKAccount)
    env.chromeMocks.storages.local[PORTFOLIO_MAP_KEY] =
        JSON.stringify(emptyPortfolioMap)
    env.api['/wallet/portfolio/:address/'].get = jest.fn(() => [
        200,
        emptyPortfolio,
    ])

    renderPage(
        '/page_entrypoint.html?type=bridge&fromAddress=******************************************'
    )

    await userEvent.type(
        await screen.findByPlaceholderText('Enter password'),
        `${testPassword}{enter}`
    )

    expect(await screen.findByText('0x26d0...8932')).toBeInTheDocument()
    expect(await screen.findByText('Private Key 1')).toBeInTheDocument()
    expect(await screen.findByText('Bridge')).toBeInTheDocument()

    expect(
        await screen.findByRole('button', { name: 'Continue' })
    ).toBeDisabled()

    await userEvent.click(
        await screen.findByRole('button', { name: 'Optimism' })
    )

    await userEvent.type(
        await screen.findByPlaceholderText('Search'),
        'Arbitrum'
    )

    await userEvent.click(await screen.findByLabelText('Arbitrum'))

    // From token
    expect(
        await screen.findByRole('button', { name: 'Arbitrum' })
    ).toBeInTheDocument()
    await userEvent.click(
        await within(await screen.findByLabelText('From')).findByRole(
            'button',
            { name: 'ETH' }
        )
    )
    let tokens = await screen.findByLabelText('Tokens')
    await userEvent.type(
        await within(tokens).findByPlaceholderText('Search'),
        'USDC'
    )
    await userEvent.click(
        await within(tokens).findByLabelText('USDC.e (Bridged)')
    )
    expect(tokens).not.toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'USDC.e' })
    ).toBeInTheDocument()

    // To token
    expect(
        await screen.findByRole('button', { name: 'Gnosis' })
    ).toBeInTheDocument()

    await userEvent.click(await screen.findByRole('button', { name: 'Gnosis' }))

    await userEvent.type(
        await within(tokens).findByPlaceholderText('Search'),
        'Base'
    )

    await userEvent.click(await screen.findByLabelText('Base'))

    await userEvent.click(
        await within(await screen.findByLabelText('To')).findByRole('button', {
            name: 'ETH',
        })
    )
    tokens = await screen.findByLabelText('Tokens')
    await userEvent.type(
        await within(tokens).findByPlaceholderText('Search'),
        'shicoin'
    )
    expect(
        await within(tokens).findByText('We found no tokens')
    ).toBeInTheDocument()
    await userEvent.clear(await within(tokens).findByPlaceholderText('Search'))
    await userEvent.type(
        await within(tokens).findByPlaceholderText('Search'),
        'USD'
    )
    await userEvent.click(await within(tokens).findByLabelText('USD Base Coin'))
    expect(tokens).not.toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'USDbC' })
    ).toBeInTheDocument()

    await userEvent.type(await screen.findByLabelText('Amount to bridge'), '10')

    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        bridgeQuoteArbitrumUSDCEBaseUSDbC,
    ])
    env.socketApiMock.post['/build-tx'] = jest.fn(() => [
        200,
        buildTxArbitrumUSDCeBaseUSDbC,
    ])
    env.rpcMocks.eth_call = jest.fn(() => [
        200,
        {
            jsonrpc: '2.0',
            result: '0x0000000000000000000000000000000000000000000000000000000000000000',
            id: **********,
        },
    ])

    expect(
        await screen.findByText('We had issues loading providers')
    ).toBeInTheDocument()
    await userEvent.click(await screen.findByRole('button', { name: 'Retry' }))

    expect(
        await screen.findByRole('button', { name: 'Reduce amount' })
    ).toBeDisabled()

    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.objectContaining({
            params: expect.objectContaining({
                bridgeWithGas: false,
                defaultBridgeSlippage: '0.50',
                defaultSwapSlippage: '0.50',
                fromAmount: '10000000',
                fromChainId: '0xa4b1',
                fromTokenAddress: '******************************************',
                recipient: '******************************************',
                singleTxOnly: true,
                sort: 'output',
                toChainId: '0x2105',
                toTokenAddress: '0xd9aaec86b65d86f6a7b5b1b0c42ffa531710b6ca',
                userAddress: '******************************************',
            }),
        })
    )

    // Route
    expect(await screen.findByLabelText('Destination amount')).toHaveValue(
        '99.75'
    )
    expect(await screen.findByLabelText('Across')).toHaveAccessibleDescription(
        '1 minFees $0'
    )
    await userEvent.click(await screen.findByLabelText('Across'))
    const providerModal = await screen.findByLabelText('Bridge provider')
    const across = await within(providerModal).findByLabelText('Across')
    expect(await within(across).findByText('99.75 USDbC')).toBeInTheDocument()
    expect(await within(across).findByText('1 min')).toBeInTheDocument()
    expect(await within(across).findByText('Fees $0')).toBeInTheDocument()
    expect(
        await within(across).findByLabelText('Best return route')
    ).toBeInTheDocument()
    expect(
        await within(across).findByLabelText('Best service time route')
    ).toBeInTheDocument()
    const stargate = await within(providerModal).findByLabelText('Stargate')
    expect(await within(stargate).findByText('98.77 USDbC')).toBeInTheDocument()
    expect(await within(stargate).findByText('1 min')).toBeInTheDocument()
    expect(await within(stargate).findByText('Fees $0')).toBeInTheDocument()
    expect(
        within(stargate).queryByLabelText('Best return route')
    ).not.toBeInTheDocument()
    expect(
        within(stargate).queryByLabelText('Best service time route')
    ).not.toBeInTheDocument()

    await userEvent.click(stargate)
    expect(
        await screen.findByLabelText('Stargate')
    ).toHaveAccessibleDescription('1 minFees $0')

    // Slippage
    await userEvent.click(
        await screen.findByRole('button', { name: 'Slippage 0.5%' })
    )
    let slippageModal = await screen.findByRole('dialog', {
        name: 'Slippage settings',
    })
    expect(
        await within(slippageModal).findByRole('radio', { name: '0.1%' })
    ).toBeInTheDocument()
    expect(
        await within(slippageModal).findByRole('radio', { name: '0.5%' })
    ).toBeInTheDocument()
    await userEvent.click(
        await within(slippageModal).findByRole('radio', { name: '1%' })
    )
    expect(
        await screen.findByRole('button', { name: 'Reduce amount' })
    ).toBeDisabled()
    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.objectContaining({
            params: expect.objectContaining({
                defaultBridgeSlippage: '1.00',
                defaultSwapSlippage: '1.00',
            }),
        })
    )
    expect(
        JSON.parse((await chrome.storage.local.get(LS_KEY))[LS_KEY])
    ).toEqual(expect.objectContaining({ swapSlippagePercent: 1 }))
    await userEvent.click(
        await screen.findByRole('button', { name: 'Slippage 1%' })
    )
    slippageModal = await screen.findByRole('dialog', {
        name: 'Slippage settings',
    })
    expect(
        await within(slippageModal).findByRole('button', { name: 'Save' })
    ).toBeDisabled()
    await userEvent.type(
        await within(slippageModal).findByPlaceholderText('Custom'),
        '0.24{enter}'
    )

    expect(slippageModal).not.toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'Reduce amount' })
    ).toBeDisabled()
    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.objectContaining({
            params: expect.objectContaining({ defaultSwapSlippage: '0.24' }),
        })
    )
    expect(
        JSON.parse((await chrome.storage.local.get(LS_KEY))[LS_KEY])
    ).toEqual(expect.objectContaining({ swapSlippagePercent: 0.24 }))
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user I should be able to open bridge with source token specified, so I can swap quicker
    As a user I should be able to switch from network on bridge, so I can choose tokens on different networks
    As a user I should be able to switch to network on bridge, so I can choose tokens on different networks
    As a user I should be able to add native currency topup to my bridge, so I'll have native currency on the network I'm bridging to
    As a user I should be able to submit bridge, so my swap will be sent to RPC
    As a user I should be able to monitor my bridge, so I know when my bridge is completd`, async () => {
    window.open = jest.fn()
    env.chromeMocks.storages.session = {}
    env.chromeMocks.storages.local[LS_KEY] = JSON.stringify(onlyPKAccount)
    env.chromeMocks.storages.local[PORTFOLIO_MAP_KEY] = serialize(portfolioMap)

    env.api['/wallet/portfolio/:address/'].get = jest.fn(() => [
        200,
        basePortfolioWithUSDCe,
    ])
    env.rpcMocks.eth_getTransactionByHash = jest.fn(() => [
        200,
        ethGetTransactionByHashWithBlockNumber,
    ])
    env.rpcMocks.eth_getTransactionReceipt = jest.fn(() => [
        200,
        ethGetTransactionReceipt,
    ])
    env.rpcMocks.eth_blockNumber = jest.fn(() => [
        200,
        ethBlockNumberAfterTransaction,
    ])
    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        swapQuotePolygonMaticUSDCNoRoutes,
    ])
    env.socketApiMock.post['/build-tx'] = jest.fn(() => [
        200,
        buildTxPolygonUSDCArbitrumUSDT,
    ])
    env.rpcMocks.eth_call = jest.fn(() => [
        200,
        {
            jsonrpc: '2.0',
            result: '0x0000000000000000000000000000000000000000000000000000000000000000',
            id: **********,
        },
    ])

    renderPage(
        '/page_entrypoint.html?type=bridge&fromAddress=******************************************&fromCurrencyId=Polygon%7C0x2791bca1f2de4661ed88a30c99a7a9449aa84174'
    )

    await userEvent.type(
        await screen.findByPlaceholderText('Enter password'),
        `${testPassword}{enter}`
    )

    expect(await screen.findByText('0x26d0...8932')).toBeInTheDocument()
    expect(await screen.findByText('Private Key 1')).toBeInTheDocument()
    expect(await screen.findByText('Bridge')).toBeInTheDocument()

    jest.useFakeTimers()
    expect(
        await screen.findByRole('button', { name: 'Continue' })
    ).toBeDisabled()

    expect(
        await screen.findByRole('button', { name: 'Polygon' })
    ).toBeInTheDocument()
    expect(
        await screen.findByRole('button', { name: 'USDC.e' })
    ).toBeInTheDocument()

    // Switch target chain
    await userEvent.click(await screen.findByRole('button', { name: 'Gnosis' }))

    await userEvent.type(
        await screen.findByPlaceholderText('Search'),
        'Arbitrum'
    )

    await userEvent.click(await screen.findByLabelText('Arbitrum'))

    expect(
        await screen.findByRole('button', { name: 'Arbitrum' })
    ).toBeInTheDocument()

    // Polygon USDC -> Arbitrum USDT

    await userEvent.click(await screen.findByRole('button', { name: 'ETH' }))
    const tokens = await screen.findByLabelText('Tokens')
    await userEvent.type(
        await within(tokens).findByPlaceholderText('Search'),
        'tether'
    )
    await userEvent.click(await within(tokens).findByLabelText('Tether USD'))
    expect(tokens).not.toBeInTheDocument()

    // Try no routes
    await userEvent.type(await screen.findByLabelText('Amount to bridge'), '1')
    expect(
        await screen.findByRole('button', { name: 'Select another asset' })
    ).toBeDisabled()

    // Above available balance and full balance
    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        bridgeQuotePolygonUSDCArbitrumUSDT,
    ])
    await userEvent.type(
        await screen.findByLabelText('Amount to bridge'),
        '160'
    )
    expect(
        await screen.findByRole('button', { name: 'Reduce amount' })
    ).toBeDisabled()
    await userEvent.click(
        await screen.findByRole('button', { name: 'Max: 157.22' })
    )
    expect(await screen.findByLabelText('Amount to bridge')).toHaveValue(
        '157.223311'
    )
    expect(await screen.findByText('$157.22')).toBeInTheDocument()
    expect(await screen.findByLabelText('Across')).toHaveAccessibleDescription(
        '2 minFees $0.42'
    )
    expect(await screen.findByLabelText('Destination amount')).toHaveValue(
        '156.79'
    )

    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.stringContaining(
            [
                'singleTxOnly=true',
                'bridgeWithGas=false',
                'fromChainId=0x89',
                'toChainId=0xa4b1',
                'fromTokenAddress=0x2791bca1f2de4661ed88a30c99a7a9449aa84174',
                'toTokenAddress=0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9',
                'fromAmount=116000000',
                'userAddress=******************************************',
                'recipient=******************************************',
                'sort=output',
                'defaultSwapSlippage=0.50',
                'defaultBridgeSlippage=0.50',
            ].join('&')
        ),
        expect.anything()
    )

    // Refuel
    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        bridgeQuotePolygonUSDCArbitrumUSDTRefuel,
    ])
    await userEvent.click(
        await screen.findByRole('button', { name: 'Top up ETH' })
    )
    expect(env.socketApiMock.get['/quote']).toHaveBeenCalledWith(
        expect.stringContaining('bridgeWithGas=true'),
        expect.anything()
    )
    const refuelFrom = await screen.findByLabelText('POL')
    expect(refuelFrom).toHaveAccessibleDescription('-8.70')
    const refuelTo = await screen.findByLabelText('ETH')
    expect(refuelTo).toHaveAccessibleDescription('+0.003053')
    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        bridgeQuotePolygonUSDCArbitrumUSDT,
    ])
    await userEvent.click(
        await within(refuelFrom).findByRole('button', { name: 'Remove Topup' })
    )
    expect(refuelFrom).not.toBeInTheDocument()
    expect(refuelTo).not.toBeInTheDocument()
    env.socketApiMock.get['/quote'] = jest.fn(() => [
        200,
        bridgeQuotePolygonUSDCArbitrumUSDTRefuel,
    ])
    await userEvent.click(
        await screen.findByRole('button', { name: 'Top up ETH' })
    )
    expect(await screen.findByLabelText('POL')).toBeInTheDocument()

    const submitButton = await screen.findByRole('button', { name: 'Continue' })
    expect(submitButton).not.toBeDisabled()

    await userEvent.click(submitButton)
    expect(await screen.findByText('Doing safety checks…')).toBeInTheDocument()

    await advanceLoadables()
    await runLottieListeners()

    // Approval transaction
    await userEvent.click(await screen.findByRole('button', { name: 'Submit' }))

    await advanceLoadables()
    await runLottieListeners()

    await userEvent.click(await screen.findByRole('button', { name: 'Close' }))
    expect(await screen.findByText('Doing safety checks…')).toBeInTheDocument()

    await advanceLoadables()
    await runLottieListeners()

    // Bridge transaction
    await userEvent.click(await screen.findByRole('button', { name: 'Submit' }))

    await advanceLoadables()
    await runLottieListeners()

    await userEvent.click(await screen.findByRole('button', { name: 'Close' }))

    // Bridge status
    env.socketApiMock.get['/bridge-status'] = jest.fn(() => [
        200,
        bridgeStatusPolygonUSDCArbitrumUSDTRefuelPendingAll,
    ])
    expect(
        await screen.findByText('Bridging USDC.e to USDT')
    ).toBeInTheDocument()
    expect(await screen.findByText('1 sec / 3 min')).toBeInTheDocument()
    await advanceTimers(5000)
    expect(await screen.findByText('11 sec / 3 min')).toBeInTheDocument()

    await userEvent.click(await screen.findByRole('button', { name: '0x' }))
    expect(window.open).toHaveBeenCalledWith(
        'https://socketscan.io/tx/0x1a44ec6f4652f4635064fee89516d22495d2827e8127c8a7afe2b67affca7913',
        '_blank'
    )

    const source = await screen.findByLabelText('USDC.e (Bridged)')
    expect(await within(source).findByText('-157.22')).toBeInTheDocument()
    expect(await within(source).findByText('-$157.22')).toBeInTheDocument()
    expect(
        await within(source).findByLabelText('Completed')
    ).toBeInTheDocument()
    const sourceRefuel = await screen.findByLabelText('POL')
    expect(await within(sourceRefuel).findByText('-8.70')).toBeInTheDocument()
    expect(
        await within(sourceRefuel).findByLabelText('Completed')
    ).toBeInTheDocument()
    const target = await screen.findByLabelText('Tether USD')
    expect(await within(target).findByText('+156.85')).toBeInTheDocument()
    expect(await within(target).findByText('+$156.85')).toBeInTheDocument()
    expect(await within(target).findByLabelText('Pending')).toBeInTheDocument()
    const targetRefuel = await screen.findByLabelText('ETH')
    expect(
        await within(targetRefuel).findByText('+0.003053')
    ).toBeInTheDocument()
    expect(
        await within(targetRefuel).findByLabelText('Pending')
    ).toBeInTheDocument()

    env.socketApiMock.get['/bridge-status'] = jest.fn(() => [
        200,
        bridgeStatusPolygonUSDCArbitrumUSDTRefuelPendingRefuel,
    ])
    await advanceLoadables()
    expect(
        await within(target).findByLabelText('Completed')
    ).toBeInTheDocument()

    env.socketApiMock.get['/bridge-status'] = jest.fn(() => [
        200,
        bridgeStatusPolygonUSDCArbitrumUSDTRefuelCompleted,
    ])
    await advanceLoadables()
    await runLottieListeners()

    expect(
        await within(await screen.findByLabelText('ETH')).findByLabelText(
            'Completed'
        )
    ).toBeInTheDocument()

    await userEvent.click(
        await within(await screen.findByLabelText('Bridge')).findByRole(
            'button',
            { name: 'Close' }
        )
    )

    await advanceLoadables()

    expect(
        await screen.findByRole('button', { name: 'Portfolio' })
    ).toBeInTheDocument()

    jest.useRealTimers()
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user I should be able to open bridge with source token specified, so I can swap quicker
    As a user I should not be able to switch fromNetwork to the same network selected in toNetwork
    As a user I should not be able to switch toNetwork to the same network selected in fromNetwork`, async () => {
    window.open = jest.fn()
    env.chromeMocks.storages.session = {}
    env.chromeMocks.storages.local[LS_KEY] = JSON.stringify(onlyPKAccount)
    env.chromeMocks.storages.local[PORTFOLIO_MAP_KEY] = serialize(portfolioMap)

    renderPage(
        '/page_entrypoint.html?type=bridge&fromAddress=******************************************&fromCurrencyId=Polygon%7C0x2791bca1f2de4661ed88a30c99a7a9449aa84174'
    )

    await userEvent.type(
        await screen.findByPlaceholderText('Enter password'),
        `${testPassword}{enter}`
    )

    expect(await screen.findByText('0x26d0...8932')).toBeInTheDocument()
    expect(await screen.findByText('Private Key 1')).toBeInTheDocument()
    expect(await screen.findByText('Bridge')).toBeInTheDocument()

    jest.useFakeTimers()
    // Polygon USDC -> Polygon
    expect(
        await screen.findByRole('button', { name: 'Continue' })
    ).toBeDisabled()

    expect(
        await screen.findByRole('button', { name: 'Polygon' })
    ).toBeInTheDocument()

    expect(
        await screen.findByRole('button', { name: 'USDC.e' })
    ).toBeInTheDocument()

    await userEvent.click(await screen.findByRole('button', { name: 'Gnosis' }))

    const networkModalWithoutPolygon = await screen.findByLabelText('Networks')
    expect(networkModalWithoutPolygon).toBeInTheDocument()

    // Polygon is preselected in to network, so it should not be available in from network
    expect(
        within(networkModalWithoutPolygon).queryByRole('button', {
            name: 'Polygon',
        })
    ).not.toBeInTheDocument()

    // Select Base as to network
    await userEvent.click(
        await within(networkModalWithoutPolygon).findByRole('button', {
            name: 'Base',
        })
    )

    await userEvent.click(
        await screen.findByRole('button', { name: 'Polygon' })
    )

    const networkModalWithoutBase = await screen.findByLabelText('Networks')
    expect(networkModalWithoutBase).toBeInTheDocument()

    // Base is not available in from network since it is selected as to network
    expect(
        within(networkModalWithoutBase).queryByRole('button', { name: 'Base' })
    ).not.toBeInTheDocument()

    jest.useRealTimers()
})
