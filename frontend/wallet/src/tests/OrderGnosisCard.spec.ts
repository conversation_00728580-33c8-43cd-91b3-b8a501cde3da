import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { Earn } from '@zeal/domains/Earn'
import { nonConfiguredEarn as mockNonConfiguredEarn } from '@zeal/domains/Earn/api/fixtures/fetchEarn'
import { testPassword } from '@zeal/domains/KeyStore/api/fixtures/testPassword'
import { onlyPKAccount } from '@zeal/domains/Storage/api/fixtures/localStorage'
import { LS_KEY } from '@zeal/domains/Storage/constants'

import { cleanEnv, mockEnv, TestEnvironment } from 'src/tests/env'
import { renderPage } from 'src/tests/utils/renderers'

jest.retryTimes(3)

let env: TestEnvironment

// TODO @manovikov-zeal :: find a way how to mock eth_call properly
jest.mock('@zeal/domains/Earn/api/fetchEarn', () => ({
    fetchEarn: async (): Promise<Earn> => mockNonConfiguredEarn,
}))

beforeEach(() => {
    env = mockEnv()
})

afterEach(() => {
    cleanEnv(env)
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip(`As a user, I should be able to order new gnosis card for the first time, so that I can use it as fiat payment method`, async () => {
    env.chromeMocks.storages.session = {}
    env.chromeMocks.storages.local[LS_KEY] = JSON.stringify(onlyPKAccount)

    await renderPage('/index.html?type=extension&mode=fullscreen')

    await userEvent.type(
        await screen.findByPlaceholderText('Enter password'),
        testPassword
    )
    await userEvent.click(
        await screen.findByRole('button', { name: 'Continue' })
    )

    await userEvent.click(
        await screen.findByRole('button', {
            name: 'Card',
        })
    )

    await userEvent.click(
        await screen.findByRole('button', {
            name: 'Order card for free',
        })
    )

    expect(
        await screen.findByText('Select wallet to manage your card')
    ).toBeInTheDocument()

    env.gnosisApiMock.get['/auth/nonce'] = jest.fn(() => [
        200,
        'xTajiuEVyFhfnMiaZ',
    ])

    env.gnosisApiMock.post['/auth/challenge'] = jest.fn(() => [
        200,
        {
            token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.7zDGlx1WX_di1t8azGFJsn61wSf-n-55jX4ERrdgoXs',
        },
    ])

    jest.spyOn(Date, 'now').mockImplementation(() => *************)

    await userEvent.click(
        await screen.findByRole('button', { name: 'Private Key 1' })
    )

    await waitFor(() => {
        expect(env.gnosisApiMock.post['/auth/challenge']).toHaveBeenCalledWith(
            expect.anything(),
            expect.objectContaining({
                body: '{"message":"app.gnosispay.com wants you to sign in with your Ethereum account:\\n0x26D0d88fFe184b1BA244D08Fb2a0c695e65c8932\\n\\nSign in With Ethereum.\\n\\nURI: https://app.gnosispay.com\\nVersion: 1\\nChain ID: 100\\nNonce: \\"xTajiuEVyFhfnMiaZ\\"\\nIssued At: 2020-12-31T23:50:00.000Z","signature":"0x92a935146c9b4de92c23587d41bdd700dbb664239b178e5f5594b704952e4e5515912db7c6a147b84f268d4020034f6eb7e77ed6155fff9bfad08dc230bdd1881b"}',
            })
        )
    })

    jest.spyOn(Date, 'now').mockRestore()
})
