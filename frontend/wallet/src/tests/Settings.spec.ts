import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { Earn } from '@zeal/domains/Earn'
import { nonConfiguredEarn as mockNonConfiguredEarn } from '@zeal/domains/Earn/api/fixtures/fetchEarn'

import { cleanEnv, mockEnv, TestEnvironment } from 'src/tests/env'
import { renderPage } from 'src/tests/utils/renderers'

import { advanceLoadables } from './utils/advanceLoadables'

let env: TestEnvironment
jest.retryTimes(3)

// TODO @manovikov-zeal :: find a way how to mock eth_call properly
jest.mock('@zeal/domains/Earn/api/fetchEarn', () => ({
    fetchEarn: async (): Promise<Earn> => mockNonConfiguredEarn,
}))

beforeEach(() => {
    env = mockEnv()
})

afterEach(() => {
    cleanEnv(env)
})

// eslint-disable-next-line jest/no-disabled-tests
test.skip('As a user I should be able to see current version of extension, so I know if I need to check for updates', async () => {
    jest.useFakeTimers()
    await renderPage('/index.html?type=extension&mode=popup')

    await advanceLoadables()

    await userEvent.click(
        await screen.findByRole('button', {
            name: 'Settings',
        })
    )

    expect(screen.getByText('Version 1.2.34 env: local')).toBeInTheDocument()
    jest.useRealTimers()
})
