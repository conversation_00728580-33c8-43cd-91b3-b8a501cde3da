import { notReachable } from '@zeal/toolkit'
import * as Web3 from '@zeal/toolkit/Web3'

import { Safe4337 } from '@zeal/domains/KeyStore'
import { fetchSafe4337Instance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { fetchGasAbstractionTransactionFees } from './fetchGasAbstractionTransactionFees'

import {
    DUMMY_EOA_SIGNATURE,
    DUMMY_PASSKEY_SIGNATURE,
    EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER,
    passkeySignerGasBufferConfig,
} from '../constants'
import { createAddOwnerMetaTransaction } from '../helpers/createAddOwnerMetaTransaction'
import { MetaTransactionData } from '../MetaTransactionData'

type Params = {
    metaTransactionDatas: MetaTransactionData[]
    callGasLimitBuffer: bigint
    network: Network
    keyStore: Safe4337
    sponsored: boolean
    networkRPCMap: NetworkRPCMap
    serverPortfolio: ServerPortfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}

export const fetchGasAbstractionTransactionFeesForMetaTransactions = async ({
    network,
    keyStore,
    networkRPCMap,
    sponsored,
    serverPortfolio,
    metaTransactionDatas,
    defaultCurrencyConfig,
    networkMap,
    callGasLimitBuffer,
    signal,
}: Params) => {
    const { safeDeplymentConfig } = keyStore

    const safeInstance = await fetchSafe4337Instance({
        safeDeplymentConfig,
        network,
        networkRPCMap,
    })

    const { safeAddress, entrypoint } = safeInstance

    const localSignerAddress = keyStore.localSignerKeyStore.address

    const addOwnerMetaTransactionData = createAddOwnerMetaTransaction({
        safeAddress: safeAddress as Web3.address.Address,
        owner: localSignerAddress,
        treshold: 1n,
    })

    switch (safeInstance.type) {
        case 'deployed': {
            if (safeInstance.owners.includes(localSignerAddress)) {
                return fetchGasAbstractionTransactionFees({
                    network,
                    sponsored,
                    initCode: null,
                    metaTransactionDatas,
                    sender: safeAddress,
                    callGasLimitBuffer,
                    verificationGasLimitBuffer:
                        EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER,
                    entrypoint,
                    networkRPCMap,
                    serverPortfolio,
                    dummySignature: DUMMY_EOA_SIGNATURE,
                    signal,
                    defaultCurrencyConfig,
                    networkMap,
                })
            } else {
                return fetchGasAbstractionTransactionFees({
                    network,
                    sponsored,
                    initCode: null,
                    metaTransactionDatas: [
                        ...metaTransactionDatas,
                        addOwnerMetaTransactionData,
                    ],
                    sender: safeAddress,
                    callGasLimitBuffer,
                    verificationGasLimitBuffer:
                        passkeySignerGasBufferConfig[
                            keyStore.safeDeplymentConfig.passkeyOwner
                                .signerVersion
                        ].verificationGasLimitBuffer,
                    entrypoint,
                    networkRPCMap,
                    serverPortfolio,
                    dummySignature: DUMMY_PASSKEY_SIGNATURE,
                    signal,
                    defaultCurrencyConfig,
                    networkMap,
                })
            }
        }

        case 'not_deployed': {
            return fetchGasAbstractionTransactionFees({
                network,
                sponsored,
                initCode: safeInstance.deploymentInitCode,
                metaTransactionDatas: [
                    ...metaTransactionDatas,
                    addOwnerMetaTransactionData,
                ],
                sender: safeAddress,
                callGasLimitBuffer,
                verificationGasLimitBuffer:
                    passkeySignerGasBufferConfig[
                        keyStore.safeDeplymentConfig.passkeyOwner.signerVersion
                    ].verificationGasLimitBuffer,
                entrypoint,
                networkRPCMap,
                serverPortfolio,
                dummySignature: DUMMY_PASSKEY_SIGNATURE,
                signal,
                defaultCurrencyConfig,
                networkMap,
            })
        }

        default:
            return notReachable(safeInstance)
    }
}
