import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Web3 from '@zeal/toolkit/Web3'
import { Address } from '@zeal/toolkit/Web3/address'

import { Account } from '@zeal/domains/Account'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { Safe4337 as SafeKeystore } from '@zeal/domains/KeyStore'
import { fetchSafe4337Instance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { ActionSource2 } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    SimulatedUserOperationRequest,
    SimulatedWithAddOwnerUserOperationRequest,
    SimulatedWithDeploymentBundleUserOperationRequest,
    SimulatedWithoutDeploymentBundleUserOperationRequest,
} from '@zeal/domains/TransactionRequest'
import { FetchSimulationByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import {
    GasAbstractionTransactionFeeResponse,
    InitialUserOperation,
} from '@zeal/domains/UserOperation'
import { fetchCurrentEntrypointNonce } from '@zeal/domains/UserOperation/api/fetchCurrentEntrypointNonce'
import { fetchGasAbstractionTransactionFees } from '@zeal/domains/UserOperation/api/fetchGasAbstractionTransactionFees'
import {
    DUMMY_EOA_SIGNATURE,
    DUMMY_PASSKEY_SIGNATURE,
    EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER,
    passkeySignerGasBufferConfig,
} from '@zeal/domains/UserOperation/constants'
import { createAddOwnerMetaTransaction } from '@zeal/domains/UserOperation/helpers/createAddOwnerMetaTransaction'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'
import { metaTransactionDatasToUserOperationCallData } from '@zeal/domains/UserOperation/helpers/metaTransactionDatasToUserOperationCallData'

type Props = {
    rpcRequestsToBundle: EthSendTransaction[]
    fetchSimulationByRequest: FetchSimulationByRequest
    network: Network
    networkMap: NetworkMap
    keyStore: SafeKeystore
    account: Account
    sponsored: boolean
    networkRPCMap: NetworkRPCMap
    dApp: DAppSiteInfo | null
    portfolio: ServerPortfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    actionSource?: ActionSource2
    signal?: AbortSignal
}

export const simulateSafeTransaction = async ({
    network,
    keyStore,
    networkRPCMap,
    rpcRequestsToBundle,
    sponsored,
    fetchSimulationByRequest,
    account,
    portfolio,
    dApp,
    defaultCurrencyConfig,
    networkMap,
    actionSource,
    signal,
}: Props): Promise<{
    userOperationRequest: SimulatedUserOperationRequest
    feeForecast: GasAbstractionTransactionFeeResponse
}> => {
    const { safeDeplymentConfig } = keyStore

    const safeInstance = await fetchSafe4337Instance({
        safeDeplymentConfig,
        network,
        networkRPCMap,
    })

    const safeAddress = safeInstance.safeAddress
    const localSignerAddress = keyStore.localSignerKeyStore.address

    if (safeAddress !== keyStore.address) {
        throw new ImperativeError(`Safe address mismatch vs keystore`, {
            safeAddress: safeAddress,
            keyStoreAddress: keyStore.address,
        })
    }

    rpcRequestsToBundle.forEach((rpcRequest) => {
        const requestData = rpcRequest.params[0]

        if (!requestData.to) {
            throw new ImperativeError(
                'Cannot bundle transaction without `to` address'
            )
        }
    })

    const requestMetaTransactions = rpcRequestsToBundle.map(
        ethSendTransactionToMetaTransactionData
    )

    switch (safeInstance.type) {
        case 'deployed': {
            const entrypoint = safeInstance.entrypoint

            const initialUserOperation: InitialUserOperation = {
                type: 'initial_user_operation',
                sender: safeAddress,
                callData: metaTransactionDatasToUserOperationCallData({
                    metaTransactionDatas: requestMetaTransactions,
                }),
                initCode: null,
                nonce: await fetchCurrentEntrypointNonce({
                    network,
                    address: safeAddress as Web3.address.Address,
                    entrypoint: entrypoint as Web3.address.Address,
                    networkRPCMap,
                    signal,
                }),
                entrypoint,
            }

            if (safeInstance.owners.includes(localSignerAddress)) {
                const [simulation, feeForecast] = await Promise.all([
                    fetchSimulationByRequest({
                        dApp,
                        network,
                        signal,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        requestToSimulate: {
                            type: 'user_operation',
                            initialUserOperation,
                        },
                    }),
                    fetchGasAbstractionTransactionFees({
                        networkMap,
                        network,
                        sponsored,
                        initCode: null,
                        metaTransactionDatas: requestMetaTransactions,
                        sender: safeAddress,
                        callGasLimitBuffer: 0n,
                        verificationGasLimitBuffer:
                            EOA_SIGNATURE_VERIFICATION_GAS_LIMIT_BUFFER,
                        entrypoint,
                        networkRPCMap,
                        serverPortfolio: portfolio,
                        dummySignature: DUMMY_EOA_SIGNATURE,
                        defaultCurrencyConfig,
                        actionSource,
                        signal,
                    }),
                ])
                const userOperationRequest: SimulatedWithoutDeploymentBundleUserOperationRequest =
                    {
                        type: 'simulated_safe_without_deployment_bundle_user_operation_request',
                        account,
                        dApp,
                        network,
                        rpcRequests: rpcRequestsToBundle,
                        simulationResult: simulation,
                        metaTransactionDatas: requestMetaTransactions,
                        initCode: null,
                        entrypoint,
                    }

                return {
                    feeForecast,
                    userOperationRequest,
                }
            } else {
                const addOwnerMetaTransactionData =
                    createAddOwnerMetaTransaction({
                        safeAddress: safeAddress as Address,
                        owner: localSignerAddress,
                        treshold: 1n,
                    })

                const entrypoint = safeInstance.entrypoint

                const [simulation, feeForecast] = await Promise.all([
                    fetchSimulationByRequest({
                        dApp,
                        network,
                        defaultCurrencyConfig,
                        signal,
                        networkMap,
                        networkRPCMap,
                        requestToSimulate: {
                            type: 'user_operation',
                            initialUserOperation,
                        },
                    }),
                    fetchGasAbstractionTransactionFees({
                        network,
                        networkMap,
                        sponsored,
                        initCode: null,
                        metaTransactionDatas: [
                            ...requestMetaTransactions,
                            addOwnerMetaTransactionData,
                        ],
                        sender: safeAddress,
                        callGasLimitBuffer: 0n,
                        verificationGasLimitBuffer:
                            passkeySignerGasBufferConfig[
                                keyStore.safeDeplymentConfig.passkeyOwner
                                    .signerVersion
                            ].verificationGasLimitBuffer,
                        entrypoint,
                        networkRPCMap,
                        serverPortfolio: portfolio,
                        dummySignature: DUMMY_PASSKEY_SIGNATURE,
                        defaultCurrencyConfig,
                        actionSource,
                        signal,
                    }),
                ])

                const userOperationRequest: SimulatedWithAddOwnerUserOperationRequest =
                    {
                        type: 'simulated_safe_with_add_owner_user_operation_request',
                        account,
                        dApp,
                        network,
                        rpcRequests: rpcRequestsToBundle,
                        simulationResult: simulation,
                        metaTransactionDatas: requestMetaTransactions,
                        addOwnerMetaTransactionData,
                        initCode: null,
                        entrypoint,
                    }

                return {
                    feeForecast,
                    userOperationRequest,
                }
            }
        }

        case 'not_deployed': {
            const entrypoint = safeInstance.entrypoint

            const simulationUserOperation: InitialUserOperation = {
                type: 'initial_user_operation',
                sender: safeAddress,
                callData: metaTransactionDatasToUserOperationCallData({
                    metaTransactionDatas: requestMetaTransactions,
                }),
                initCode: safeInstance.deploymentInitCode,
                nonce: await fetchCurrentEntrypointNonce({
                    network,
                    address: safeAddress as Web3.address.Address,
                    entrypoint: entrypoint as Web3.address.Address,
                    networkRPCMap,
                    signal,
                }),
                entrypoint,
            }

            const addOwnerMetaTransactionData = createAddOwnerMetaTransaction({
                safeAddress: safeAddress as Address,
                owner: localSignerAddress,
                treshold: 1n,
            })

            const [simulation, feeForecast] = await Promise.all([
                fetchSimulationByRequest({
                    dApp,
                    network,
                    networkMap,
                    networkRPCMap,
                    defaultCurrencyConfig,
                    signal,
                    requestToSimulate: {
                        type: 'user_operation',
                        initialUserOperation: simulationUserOperation,
                    },
                }),
                fetchGasAbstractionTransactionFees({
                    network,
                    networkMap,
                    sponsored,
                    initCode: safeInstance.deploymentInitCode,
                    metaTransactionDatas: [
                        ...requestMetaTransactions,
                        addOwnerMetaTransactionData,
                    ],
                    sender: safeAddress,
                    callGasLimitBuffer: 0n,
                    verificationGasLimitBuffer:
                        passkeySignerGasBufferConfig[
                            keyStore.safeDeplymentConfig.passkeyOwner
                                .signerVersion
                        ].verificationGasLimitBuffer,
                    entrypoint,
                    networkRPCMap,
                    serverPortfolio: portfolio,
                    dummySignature: DUMMY_PASSKEY_SIGNATURE,
                    defaultCurrencyConfig,
                    actionSource,
                    signal,
                }),
            ])

            const userOperationRequest: SimulatedWithDeploymentBundleUserOperationRequest =
                {
                    type: 'simulated_safe_deployment_bundle_user_operation_request',
                    account,
                    dApp,
                    network,
                    rpcRequests: rpcRequestsToBundle,
                    simulationResult: simulation,
                    metaTransactionDatas: requestMetaTransactions,
                    addOwnerMetaTransactionData,
                    initCode: safeInstance.deploymentInitCode,
                    entrypoint,
                }

            return {
                feeForecast,
                userOperationRequest,
            }
        }
        default:
            return notReachable(safeInstance)
    }
}
