import React from 'react'
import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'

import { GnosisPayCardBlockedCantBeActivatedTitle } from '@zeal/domains/Card/components/GnosisPayCardBlockedCantBeActivated'
import { AppError } from '@zeal/domains/Error'

export const Title = ({ error }: { error: AppError }) => {
    switch (error.type) {
        case 'unblock_account_number_and_sort_code_mismatch':
            return (
                <FormattedMessage
                    id="error.unblock_account_number_and_sort_code_mismatch"
                    defaultMessage="Account number and sort code mismatch"
                />
            )

        case 'unblock_invalid_iban':
            return (
                <FormattedMessage
                    id="error.unblock_invalid_iban"
                    defaultMessage="Invalid IBAN"
                />
            )

        case 'unblock_hard_kyc_failure':
            return (
                <FormattedMessage
                    id="error.unblock_hard_kyc_failure"
                    defaultMessage="Unexpected KYC state"
                />
            )

        case 'unblock_session_expired':
            return (
                <FormattedMessage
                    id="error.unblock_session_expired.title"
                    defaultMessage="Unblock session expired"
                />
            )

        case 'unblock_user_with_such_email_already_exists':
            return (
                <FormattedMessage
                    id="error.unblock_user_with_such_email_already_exists.title"
                    defaultMessage="User with such email already exists"
                />
            )

        case 'unblock_user_with_address_already_exists':
            return (
                <FormattedMessage
                    id="error.unblock_user_with_address_already_exists.title"
                    defaultMessage="Account already set up for address"
                />
            )
        case 'decrypt_incorrect_password':
            return (
                <FormattedMessage
                    id="error.decrypt_incorrect_password.title"
                    defaultMessage="Incorrect password"
                />
            )
        case 'encrypted_object_invalid_format':
        case 'invalid_encrypted_file_format':
            return (
                <FormattedMessage
                    id="error.encrypted_object_invalid_format.title"
                    defaultMessage="Corrupted data"
                />
            )

        case 'bundler_error_aa10_sender_already_constructed':
        case 'bundler_error_aa13_init_code_failed_or_out_of_gas':
        case 'bundler_error_aa21_didnt_pay_prefund':
        case 'bundler_error_aa22_expired_or_not_due':
        case 'bundler_error_aa23_reverted_or_oog':
        case 'bundler_error_aa24_signature_error':
        case 'bundler_error_aa25_invalid_account_nonce':
        case 'bundler_error_aa31_paymaster_deposit_too_low':
        case 'bundler_error_aa33_reverted_or_out_of_gas':
        case 'bundler_error_aa34_signature_error':
        case 'bundler_error_aa40_over_verification_gas_limit':
        case 'bundler_error_aa41_too_little_verification_gas':
        case 'bundler_error_aa51_prefund_below_gas_cost':
        case 'bundler_error_aa93_invalid_paymaster_and_data':
        case 'bundler_error_aa95_out_of_gas':
        case 'bundler_error_cannot_execute_request':
        case 'bundler_error_max_priority_fee_per_gas_is_lower_than_expected':
        case 'bundler_error_unknown':
        case 'bundler_error_user_operation_reverted_during_execution_phase':
        case 'bundler_error_user_operation_reverted_during_execution_phase_address_not_whitelisted':
        case 'bundler_error_user_operation_reverted_during_execution_phase_already_minted':
        case 'bundler_error_user_operation_reverted_during_execution_phase_return_amount_not_enough':
        case 'bundler_error_user_operation_reverted_during_execution_phase_transfer_amount_exceeds_balance':
        case 'eoa_transaction_failed_error':
        case 'rpc_error_already_known':
        case 'rpc_error_block_gas_limit_exceeded':
        case 'rpc_error_block_state_unavailable':
        case 'rpc_error_cannot_execute_request':
        case 'rpc_error_cannot_query_unfinalized_data':
        case 'rpc_error_contract_signature_hash_not_approved':
        case 'rpc_error_execution_reverted':
        case 'rpc_error_execution_timeout':
        case 'rpc_error_gas_price_is_less_than_minimum':
        case 'rpc_error_gas_required_exceeds_allowance':
        case 'rpc_error_insufficient_balance_for_transfer':
        case 'rpc_error_insufficient_funds_for_gas_and_value':
        case 'rpc_error_invalid_argument':
        case 'rpc_error_invalid_rsv_values':
        case 'rpc_error_invalid_sender':
        case 'rpc_error_max_fee_per_gas_less_than_block_base_fee':
        case 'rpc_error_nounce_is_too_low':
        case 'rpc_error_priority_fee_too_low':
        case 'rpc_error_replacement_not_allowed':
        case 'rpc_error_replacement_transaction_underpriced':
        case 'rpc_error_swap_failed':
        case 'rpc_error_too_many_requests':
        case 'rpc_error_transaction_erc20_insufficient_allowance':
        case 'rpc_error_transaction_erc20_insufficient_balance':
        case 'rpc_error_transaction_erc20_transfer_error':
        case 'rpc_error_transaction_underpriced':
        case 'rpc_error_tx_pool_disabled':
        case 'rpc_error_unknown':
        case 'user_operation_failed_error':
            return (
                <FormattedMessage
                    id="error.trezor_action_cancelled.title"
                    defaultMessage="Transaction rejected"
                />
            )

        case 'trezor_device_used_elsewhere':
            return (
                <FormattedMessage
                    id="error.trezor_device_used_elsewhere.title"
                    defaultMessage="Device is being used in another session"
                />
            )

        case 'trezor_method_cancelled':
            return (
                <FormattedMessage
                    id="error.trezor_method_cancelled.title"
                    defaultMessage="Couldn't sync Trezor"
                />
            )

        case 'trezor_permissions_not_granted':
            return (
                <FormattedMessage
                    id="error.trezor_permissions_not_granted.title"
                    defaultMessage="Couldn't sync Trezor"
                />
            )

        case 'trezor_pin_cancelled':
            return (
                <FormattedMessage
                    id="error.trezor_pin_cancelled.title"
                    defaultMessage="Couldn't sync Trezor"
                />
            )

        case 'trezor_popup_closed':
            return (
                <FormattedMessage
                    id="error.trezor_popup_closed.title"
                    defaultMessage="Couldn't sync Trezor"
                />
            )

        case 'failed_to_fetch_google_auth_token':
            return (
                <FormattedMessage
                    id="error.failed_to_fetch_google_auth_token.title"
                    defaultMessage="We couldn't get access"
                />
            )
        case 'unblock_invalid_faster_payment_configuration':
            return (
                <FormattedMessage
                    id="error.unblock_invalid_faster_payment_configuration.title"
                    defaultMessage="This bank does not support Faster Payments"
                />
            )

        case 'gnosis_pay_is_not_available_in_this_country':
            return (
                <FormattedMessage
                    id="gnosisPayIsNotAvailableInThisCountry.title"
                    defaultMessage="GnosisPay is not yet available in your country"
                />
            )

        case 'gnosis_pay_card_blocked_cant_be_activated':
            return <GnosisPayCardBlockedCantBeActivatedTitle />
        case 'biometric_prompt_auth_failed':
        case 'biometric_prompt_cancelled':
        case 'gnosis_pay_address_linked_to_another_monerium_profile':
        case 'gnosis_pay_already_accepted_terms_error':
        case 'gnosis_pay_email_or_account_already_registered_error':
        case 'gnosis_pay_invalid_phone_otp_error':
        case 'gnosis_pay_invalid_signup_otp_error':
        case 'gnosis_pay_no_active_cards_found':
        case 'gnosis_pay_public_key_not_matching_error':
        case 'gnosis_pay_readonly_signer_is_already_in_use':
        case 'gnosis_pay_there_is_already_pending_card_order':
        case 'gnosis_pay_user_is_not_signed_up':
        case 'gnosis_pay_user_already_has_monerium_account':
        case 'gnosis_pay_user_cannot_create_card_re_kyc_needed':
        case 'gnosis_pay_failed_to_verify_signature':
        case 'google_api_error':
        case 'hardware_wallet_failed_to_open_device':
        case 'http_error':
        case 'imperative_error':
        case 'ios_could_not_communicate_with_helper_application':
        case 'ledger_blind_sign_not_enabled_or_running_non_eth_app':
        case 'ledger_is_locked':
        case 'ledger_not_running_any_app':
        case 'ledger_running_non_eth_app':
        case 'passkey_android_cannot_validate_incoming_request':
        case 'passkey_android_creation_interrupted':
        case 'passkey_android_failed_to_launch_selector':
        case 'passkey_android_fido_api_not_available':
        case 'passkey_android_fido_api_not_supported':
        case 'passkey_android_no_create_options_available':
        case 'passkey_android_no_credential_available':
        case 'passkey_android_provider_configuration_error':
        case 'passkey_android_resident_key_creation_not_supported':
        case 'passkey_android_timeout_error':
        case 'passkey_android_unable_to_get_sync_account':
        case 'passkey_android_unknown_error':
        case 'passkey_app_not_associated_with_domain':
        case 'passkey_google_account_missing':
        case 'passkey_not_supported_in_mobile_browser_error':
        case 'passkey_operation_cancelled':
        case 'passkey_screen_lock_missing':
        case 'passkey_signer_not_found_error':
        case 'rpc_request_parse_error':
        case 'secure_store_keychain_decryption_error':
        case 'swaps_io_not_enough_balance_error':
        case 'swaps_io_swap_below_minimum':
        case 'trezor_action_cancelled':
        case 'trezor_connection_already_initialized':
        case 'unblock_invalid_otp':
        case 'unblock_login_user_did_not_exists':
        case 'unblock_maximum_otp_attempts_exceeded':
        case 'unblock_nonce_already_in_use':
        case 'unblock_otp_expired':
        case 'unblock_unsupported_country':
        case 'unblock_user_associated_with_other_merchant':
        case 'unexpected_failure':
        case 'unknown_error':
        case 'unknown_merchant_code':
        case 'user_trx_denied_by_user':
            return (
                <FormattedMessage
                    id="error.unknown_error.title"
                    defaultMessage="System error"
                />
            )

        case 'connectivity_error':
            return (
                <FormattedMessage
                    id="error.connectivity_error.title"
                    defaultMessage="No internet connection"
                />
            )

        case 'wallet_connect_proposal_no_more_available':
        case 'wallet_connect_proposal_expired':
            return (
                <FormattedMessage
                    id="wallet_connect_proposal_expired.title"
                    defaultMessage="Connection expired"
                />
            )
        case 'wallet_connect_add_ethereum_chain_missing_or_invalid':
        case 'wagmi_add_ethereum_chain_not_supported':
        case 'wagmi_switch_chain_chain_id_not_supported':
            return (
                <FormattedMessage
                    id="wallet_connect_add_chain_missing.title"
                    defaultMessage="Network not supported"
                />
            )

        case 'monerium_error_address_re_link_required':
            return (
                <FormattedMessage
                    id="monerium_error_address_re_link_required.title"
                    defaultMessage="Wallet needs to be re-linked to Monerium"
                />
            )

        case 'monerium_error_duplicate_order':
            return (
                <FormattedMessage
                    id="monerium_error_duplicate_order.title"
                    defaultMessage="Duplicate order"
                />
            )

        case 'signal_aborted_for_unknown_reason':
            return (
                <FormattedMessage
                    id="signal_aborted_for_uknown_reason.title"
                    defaultMessage="Network request cancelled"
                />
            )

        case 'zeal_a_rewards_already_claimed_error':
            return (
                <FormattedMessage
                    id="zeal_a_rewards_already_claimed_error.title"
                    defaultMessage="Reward already claimed"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
