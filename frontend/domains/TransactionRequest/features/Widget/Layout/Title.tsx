import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'

import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import { SimulationResult } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'

type Props = { simulationResult: SimulationResult }

export const Title = ({ simulationResult }: Props) => {
    switch (simulationResult.type) {
        case 'failed':
        case 'not_supported':
            return (
                <FormattedMessage
                    id="simulationNotAvailable.title"
                    defaultMessage="Unknown Action"
                />
            )

        case 'simulated': {
            const simulatedTransaction = simulationResult.simulation.transaction

            switch (simulatedTransaction.type) {
                case 'earn_recharge_disabled':
                    return (
                        <FormattedMessage
                            id="earn.recharge_configured.disable.trx.title"
                            defaultMessage="Disable Auto-Recharge"
                        />
                    )
                case 'earn_recharge_configured':
                    return (
                        <FormattedMessage
                            id="earn.set_recharge.status.title"
                            defaultMessage="Set Auto-Recharge"
                        />
                    )
                case 'earn_recharge_updated':
                    return (
                        <FormattedMessage
                            id="earn.recharge_configured.updated.trx.title"
                            defaultMessage="Save Recharge Settings"
                        />
                    )
                case 'earn_deposit_with_swap':
                case 'earn_deposit_direct_send':
                    return (
                        <FormattedMessage
                            id="earn.earn_deposit.status.title"
                            defaultMessage="Deposit into Earn"
                        />
                    )
                case 'earn_withdraw':
                    return (
                        <FormattedMessage
                            id="earn.earn_withdraw.status.title"
                            defaultMessage="Withdraw from Earn Account"
                        />
                    )
                case 'card_cashback_deposit':
                    return (
                        <FormattedMessage
                            id="card-cashback.status.title"
                            defaultMessage="Deposit into Cashback"
                        />
                    )
                case 'deploy_earn_account':
                    return (
                        <FormattedMessage
                            id="earn.deploy.status.title"
                            defaultMessage="Create {title} Earn account"
                            values={{
                                title: (
                                    <TakerTitle
                                        takerType={
                                            simulatedTransaction.taker.type
                                        }
                                    />
                                ),
                            }}
                        />
                    )

                case 'smart_wallet_activation':
                    return (
                        <FormattedMessage
                            id="activate-smart-wallet.title"
                            defaultMessage="Activate wallet"
                        />
                    )

                case 'WithdrawalTrx':
                    return (
                        <FormattedMessage
                            id="simulatedTransaction.Withdrawal.info.title"
                            defaultMessage="Withdrawal"
                        />
                    )

                case 'CardTopUpTrx':
                case 'card_top_up_from_earn':
                    return (
                        <FormattedMessage
                            id="simulatedTransaction.CardTopUp.info.title"
                            defaultMessage="Add cash to card"
                        />
                    )

                case 'BridgeTrx':
                    return (
                        <FormattedMessage
                            id="simulatedTransaction.BridgeTrx.info.title"
                            defaultMessage="Bridge"
                        />
                    )

                case 'swaps_io_native_token_swap':
                case 'P2PTransaction':
                case 'P2PNftTransaction':
                    return (
                        <FormattedMessage
                            id="simulatedTransaction.P2PTransaction.info.title"
                            defaultMessage="Send"
                        />
                    )
                case 'ApprovalTransaction':
                    return (
                        <FormattedMessage
                            id="simulatedTransaction.approve.info.title"
                            defaultMessage="Approve"
                        />
                    )

                case 'NftCollectionApprovalTransaction':
                    return (
                        <FormattedMessage
                            id="simulatedTransaction.NftCollectionApproval.approve"
                            defaultMessage="Approve NFT collection"
                        />
                    )

                case 'SingleNftApprovalTransaction':
                    return (
                        <FormattedMessage
                            id="simulatedTransaction.SingleNftApproval.approve"
                            defaultMessage="Approve NFT"
                        />
                    )

                case 'UnknownTransaction':
                case 'FailedTransaction':
                    return <>{simulatedTransaction.method}</>

                default:
                    return notReachable(simulatedTransaction)
            }
        }

        /* istanbul ignore next */
        default:
            return notReachable(simulationResult)
    }
}
