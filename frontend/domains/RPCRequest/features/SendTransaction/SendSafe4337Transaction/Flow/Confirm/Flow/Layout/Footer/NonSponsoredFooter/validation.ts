import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { failure, Result, success } from '@zeal/toolkit/Result'

import { CryptoCurrency } from '@zeal/domains/Currency'
import { Crypto<PERSON>oney, Money } from '@zeal/domains/Money'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { SimulatedUserOperationRequest } from '@zeal/domains/TransactionRequest'
import { SimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { NonSponsoredGasAbstractionTransactionFee } from '@zeal/domains/UserOperation'

import {
    DangerSafetyChecksFailedError,
    validateDangerSafetyChecks,
} from '../validateDangerSafetyChecks'

type InsufficientGasTokenBalanceError = {
    type: 'insufficient_gas_token_balance'
    selectedFee: NonSponsoredGasAbstractionTransactionFee
    requiredGasCurrencyAmount: CryptoMoney
}

type SubmitError =
    | InsufficientGasTokenBalanceError
    | DangerSafetyChecksFailedError

export type FeeForecastError = InsufficientGasTokenBalanceError

export const getOutgoingAmounts = ({
    simulatedTransaction,
}: {
    simulatedTransaction: SimulatedTransaction
}): Money[] => {
    switch (simulatedTransaction.type) {
        case 'ApprovalTransaction': {
            const approvalAmount = simulatedTransaction.amount
            switch (approvalAmount.type) {
                case 'Limited':
                    return [
                        {
                            currencyId: approvalAmount.amount.currency.id,
                            amount: approvalAmount.amount.amount,
                        },
                    ]

                case 'Unlimited':
                    return []

                default:
                    return notReachable(approvalAmount)
            }
        }
        case 'FailedTransaction':
        case 'SingleNftApprovalTransaction':
        case 'NftCollectionApprovalTransaction':
        case 'P2PNftTransaction':
        case 'deploy_earn_account':
        case 'earn_recharge_configured':
        case 'earn_withdraw':
        case 'earn_recharge_disabled':
        case 'earn_recharge_updated':
        case 'smart_wallet_activation':
            return []

        case 'WithdrawalTrx':
            return [
                {
                    amount: simulatedTransaction.withdrawalRequest.fromAmount
                        .amount,
                    currencyId:
                        simulatedTransaction.withdrawalRequest.fromAmount
                            .currency.id,
                },
            ]

        case 'CardTopUpTrx':
            return [
                {
                    amount: simulatedTransaction.topUpRequest.amount.amount,
                    currencyId:
                        simulatedTransaction.topUpRequest.amount.currency.id,
                },
            ]

        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
            return [
                {
                    amount: simulatedTransaction.earnDepositRequest.from.amount,
                    currencyId:
                        simulatedTransaction.earnDepositRequest.from.currency
                            .id,
                },
            ]

        case 'card_top_up_from_earn':
            switch (simulatedTransaction.state) {
                case 'withdraw':
                case 'approval':
                    return []
                case 'swap':
                    return [
                        {
                            amount: simulatedTransaction.topUpRequest
                                .investmentAssetAmount.amount,
                            currencyId:
                                simulatedTransaction.topUpRequest
                                    .investmentAssetAmount.currency.id,
                        },
                    ]
                /* istanbul ignore next */
                default:
                    return notReachable(simulatedTransaction.state)
            }
        case 'swaps_io_native_token_swap':
            return [
                {
                    amount: simulatedTransaction.swapsIOQuote.from.amount,
                    currencyId:
                        simulatedTransaction.swapsIOQuote.from.currency.id,
                },
            ]

        case 'BridgeTrx':
            return [
                {
                    amount: simulatedTransaction.bridgeRoute.from.amount,
                    currencyId:
                        simulatedTransaction.bridgeRoute.from.currency.id,
                },
            ]

        case 'card_cashback_deposit':
            return [
                {
                    amount: simulatedTransaction.cashbackDepositRequest
                        .depositAmount.amount,
                    currencyId:
                        simulatedTransaction.cashbackDepositRequest
                            .depositAmount.currency.id,
                },
            ]

        case 'P2PTransaction': {
            switch (simulatedTransaction.token.direction) {
                case 'Receive':
                    return []
                case 'Send':
                    return [simulatedTransaction.token.amount]

                /* istanbul ignore next */
                default:
                    return notReachable(simulatedTransaction.token.direction)
            }
        }
        case 'UnknownTransaction': {
            const outgoingTokens = simulatedTransaction.tokens.filter(
                (token) => {
                    switch (token.direction) {
                        case 'Receive':
                            return false
                        case 'Send':
                            return true

                        /* istanbul ignore next */
                        default:
                            return notReachable(token.direction)
                    }
                }
            )

            return outgoingTokens.map((token) => token.amount)
        }
        /* istanbul ignore next */
        default:
            return notReachable(simulatedTransaction)
    }
}

const validateGasCurrencyBalance = ({
    portfolio,
    fees,
    selectedGasCurrency,
    simulatedTransaction,
}: {
    portfolio: ServerPortfolio2 | null
    selectedGasCurrency: CryptoCurrency
    fees: NonSponsoredGasAbstractionTransactionFee[]
    simulatedTransaction: SimulatedTransaction
}): Result<
    InsufficientGasTokenBalanceError,
    NonSponsoredGasAbstractionTransactionFee
> => {
    const token = getTokenByCryptoCurrency3({
        currency: selectedGasCurrency,
        serverPortfolio: portfolio,
    })

    const selectedFee = fees.find(
        (fee) => fee.feeInTokenCurrency.currency.id === selectedGasCurrency.id
    )

    if (!selectedFee) {
        throw new ImperativeError(
            `Selected fee not found in validateGasCurrencyBalance for gas currency`,
            { selectedGasCurrencyId: selectedGasCurrency }
        )
    }

    const outgoingAmounts = getOutgoingAmounts({ simulatedTransaction })

    const outgoingGasTokenAmount =
        outgoingAmounts.find(
            (item) => item.currencyId === selectedGasCurrency.id
        )?.amount || 0n

    const requiredGasCurrencyAmount =
        selectedFee.feeInTokenCurrency.amount + outgoingGasTokenAmount

    if (!token || token.balance.amount < requiredGasCurrencyAmount) {
        return failure({
            type: 'insufficient_gas_token_balance',
            selectedFee,
            requiredGasCurrencyAmount: {
                currency: selectedGasCurrency,
                amount: requiredGasCurrencyAmount,
            },
        })
    }

    return success(selectedFee)
}

export const validate = ({
    portfolio,
    selectedGasCurrency,
    fees,
    simulatedTransaction,
}: {
    portfolio: ServerPortfolio2 | null
    selectedGasCurrency: CryptoCurrency
    fees: NonSponsoredGasAbstractionTransactionFee[]
    simulatedTransaction: SimulatedTransaction
}): Result<FeeForecastError, NonSponsoredGasAbstractionTransactionFee> =>
    validateGasCurrencyBalance({
        portfolio,
        selectedGasCurrency,
        fees,
        simulatedTransaction,
    })

export const validateSubmit = ({
    portfolio,
    selectedGasCurrency,
    userOperationRequest,
    fees,
    simulatedTransaction,
}: {
    portfolio: ServerPortfolio2 | null
    selectedGasCurrency: CryptoCurrency
    userOperationRequest: SimulatedUserOperationRequest
    fees: NonSponsoredGasAbstractionTransactionFee[]
    simulatedTransaction: SimulatedTransaction
}): Result<
    SubmitError,
    {
        selectedFee: NonSponsoredGasAbstractionTransactionFee
        userOperationRequest: SimulatedUserOperationRequest
    }
> =>
    validateGasCurrencyBalance({
        portfolio,
        selectedGasCurrency,
        fees,
        simulatedTransaction,
    }).andThen((selectedFee) =>
        validateDangerSafetyChecks({ userOperationRequest, selectedFee }).map(
            () => ({
                selectedFee,
                userOperationRequest,
            })
        )
    )
