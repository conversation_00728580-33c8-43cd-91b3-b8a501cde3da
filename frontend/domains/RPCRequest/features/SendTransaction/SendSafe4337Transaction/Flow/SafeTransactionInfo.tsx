import { notReachable } from '@zeal/toolkit'

import { AccountsMap } from '@zeal/domains/Account'
import { OffRampTransactionView } from '@zeal/domains/Currency/domains/BankTransfer/components/OffRampTransactionView'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { TakerSubtextListItemNoBalance } from '@zeal/domains/Earn/components/TakerSubtextListItemNoBalance'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap } from '@zeal/domains/Network'
import { NftCollectionListItem } from '@zeal/domains/NFTCollection/components/NftCollectionListItem'
import { NftListItem } from '@zeal/domains/NFTCollection/components/NftListItem'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { Approve } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/Approve'
import { BridgeTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/BridgeTrx'
import { CardCashbackDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardCashbackDepositTrxView'
import { CardTopUpFromEarnTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpFromEarnTrxView'
import { CardTopUpTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpTrxView'
import { EarnDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnDepositTrxView'
import { EarnRechargeConfiguredTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnRechargeConfiguredTrxView'
import { EarnWithdrawalTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnWithdrawalTrxView'
import { Failed } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/Failed'
import { P2PTransactionView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/P2PTransactionView'
import { SmartWalletActivationTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SmartWalletActivationTrxView'
import { SwapsIONativeTokenSwapView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SwapsIONativeTokenSwap'
import { Unknown } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/Unknown'

export const SafeTransactionInfo = ({
    simulation,
    dApp,
    accounts,
    keyStores,
    networkMap,
    installationId,
}: {
    simulation: SimulateTransactionResponse
    dApp: DAppSiteInfo | null
    accounts: AccountsMap
    keyStores: KeyStoreMap
    networkMap: NetworkMap
    installationId: string
}) => {
    const { transaction, checks, currencies } = simulation

    switch (transaction.type) {
        case 'earn_recharge_configured':
        case 'earn_recharge_updated':
            return <EarnRechargeConfiguredTrxView transaction={transaction} />
        case 'earn_recharge_disabled':
            return null
        case 'deploy_earn_account':
            return (
                <TakerSubtextListItemNoBalance
                    taker={transaction.taker}
                    takerApyMap={transaction.takerApyMap}
                />
            )
        case 'earn_withdraw':
            return <EarnWithdrawalTrxView transaction={transaction} />
        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
            return <EarnDepositTrxView transaction={transaction} />
        case 'card_cashback_deposit':
            return <CardCashbackDepositTrxView transaction={transaction} />
        case 'card_top_up_from_earn':
            return <CardTopUpFromEarnTrxView transaction={transaction} />
        case 'BridgeTrx':
            return (
                <BridgeTrxView
                    networkMap={networkMap}
                    transaction={transaction}
                />
            )
        case 'swaps_io_native_token_swap':
            return (
                <SwapsIONativeTokenSwapView
                    transaction={transaction}
                    networkMap={networkMap}
                />
            )
        case 'WithdrawalTrx':
            return (
                <OffRampTransactionView
                    variant={{ type: 'no_status' }}
                    networkMap={networkMap}
                    withdrawalRequest={transaction.withdrawalRequest}
                />
            )
        case 'CardTopUpTrx':
            return (
                <CardTopUpTrxView transaction={transaction} checks={checks} />
            )
        case 'ApprovalTransaction':
            return (
                <Approve
                    transaction={transaction}
                    checks={checks}
                    knownCurrencies={currencies}
                />
            )
        case 'UnknownTransaction':
            return (
                <Unknown
                    networkMap={networkMap}
                    checks={checks}
                    knownCurrencies={currencies}
                    transaction={transaction}
                />
            )
        case 'FailedTransaction':
            return <Failed dApp={dApp} transaction={transaction} />
        case 'SingleNftApprovalTransaction':
            return (
                <NftListItem
                    networkMap={networkMap}
                    nft={transaction.nft}
                    checks={checks}
                    rightNode={null}
                />
            )
        case 'NftCollectionApprovalTransaction':
            return (
                <NftCollectionListItem
                    networkMap={networkMap}
                    checks={checks}
                    nftCollection={transaction.nftCollectionInfo}
                />
            )
        case 'P2PTransaction':
        case 'P2PNftTransaction':
            return (
                <P2PTransactionView
                    installationId={installationId}
                    networkMap={networkMap}
                    transaction={transaction}
                    dApp={dApp}
                    knownCurrencies={currencies}
                    checks={checks}
                    accounts={accounts}
                    keystores={keyStores}
                />
            )
        case 'smart_wallet_activation':
            return <SmartWalletActivationTrxView transaction={transaction} />
        /* istanbul ignore next */
        default:
            return notReachable(transaction)
    }
}
