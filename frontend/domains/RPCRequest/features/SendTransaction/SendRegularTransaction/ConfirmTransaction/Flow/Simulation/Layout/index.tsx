import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Content } from '@zeal/uikit/Content'
import { Group } from '@zeal/uikit/Group'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { OffRampTransactionView } from '@zeal/domains/Currency/domains/BankTransfer/components/OffRampTransactionView'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { TakerSubtextListItemNoBalance } from '@zeal/domains/Earn/components/TakerSubtextListItemNoBalance'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { NftCollectionListItem } from '@zeal/domains/NFTCollection/components/NftCollectionListItem'
import { NftListItem } from '@zeal/domains/NFTCollection/components/NftListItem'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { TransactionSafetyCheckResult } from '@zeal/domains/SafetyCheck'
import { TransactionStatusButton } from '@zeal/domains/SafetyCheck/components/TransactionStatusButton'
import { ListItem } from '@zeal/domains/SmartContract/components/ListItem'
import { NotSigned } from '@zeal/domains/TransactionRequest'
import { ActionBar } from '@zeal/domains/Transactions/components/ActionBar'
import {
    FeeForecastRequest,
    FeeForecastResponse,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { SimulationResult } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import { BridgeTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/BridgeTrx'
import { CardCashbackDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardCashbackDepositTrxView'
import { CardTopUpFromEarnTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpFromEarnTrxView'
import { CardTopUpTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpTrxView'
import { EarnDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnDepositTrxView'
import { EarnRechargeConfiguredTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnRechargeConfiguredTrxView'
import { EarnWithdrawalTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnWithdrawalTrxView'
import { EditableApprove } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EditableApprove'
import { Failed } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/Failed'
import { P2PTransactionView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/P2PTransactionView'
import { SimulatedTransactionContentHeader } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SimulatedTransactionContentHeader'
import { SmartWalletActivationTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SmartWalletActivationTrxView'
import { SwapsIONativeTokenSwapView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SwapsIONativeTokenSwap'
import { Unknown } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/Unknown'

import { FeeForecastWidget } from '../../../../FeeForecastWidget'
import { ActionButton, Msg as ActionButtonMsg } from '../../ActionButton'
import { validateSafetyCheckFailedWithFailedChecksOnly } from '../helpers/validation'

type Props = {
    installationId: string
    transactionRequest: NotSigned
    simulation: SimulateTransactionResponse
    nonce: number
    gasEstimate: Hexadecimal.Hexadecimal

    networkMap: NetworkMap

    pollableData: PollableData<FeeForecastResponse, FeeForecastRequest>
    pollingInterval: number
    pollingStartedAt: number

    accounts: AccountsMap
    keystores: KeyStoreMap

    actionSource: ActionSource2

    onMsg: (msg: Msg) => void
}

export type Msg =
    | {
          type: 'on_cancel_confirm_transaction_clicked'
      }
    | {
          type: 'safety_checks_clicked'
      }
    | {
          type: 'on_minimize_click'
      }
    | ActionButtonMsg
    | MsgOf<typeof FeeForecastWidget>
    | MsgOf<typeof EditableApprove>
    | MsgOf<typeof SimulatedTransactionContentHeader>

export const Layout = ({
    onMsg,
    simulation,
    nonce,
    gasEstimate,
    transactionRequest,
    pollingInterval,
    pollingStartedAt,
    pollableData,
    accounts,
    keystores,
    networkMap,
    actionSource,
    installationId,
}: Props) => {
    const { account } = transactionRequest

    const keystore = getKeyStore({
        keyStoreMap: keystores,
        address: account.address,
    })

    const simulationResult: SimulationResult = { type: 'simulated', simulation }

    const safetyCheckResult = validateSafetyCheckFailedWithFailedChecksOnly({
        simulationResult,
    })

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'on_minimize_click' })}
        >
            <ActionBar
                title={
                    <FormattedMessage
                        id="action_bar_title.transaction_request"
                        defaultMessage="Transaction request"
                    />
                }
                account={transactionRequest.account}
                actionSourceType={actionSource.type}
                network={findNetworkByHexChainId(
                    transactionRequest.networkHexId,
                    networkMap
                )}
                onMsg={onMsg}
            />

            <Column spacing={12} alignY="stretch">
                <Content
                    header={
                        <SimulatedTransactionContentHeader
                            dAppInfo={transactionRequest.dApp}
                            simulatedTransaction={
                                simulationResult.simulation.transaction
                            }
                        />
                    }
                    footer={
                        <Footer
                            safetyCheckResult={safetyCheckResult}
                            simulation={simulation}
                            networkMap={networkMap}
                            onMsg={onMsg}
                        />
                    }
                >
                    <TransactionInfo
                        installationId={installationId}
                        networkMap={networkMap}
                        keystores={keystores}
                        originalEthSendTransaction={
                            transactionRequest.rpcRequest
                        }
                        accounts={accounts}
                        dApp={transactionRequest.dApp}
                        simulation={simulation}
                        onMsg={onMsg}
                    />
                </Content>

                <Column spacing={12}>
                    <FeeForecastWidget
                        keystore={keystore}
                        nonce={nonce}
                        gasEstimate={gasEstimate}
                        pollingStartedAt={pollingStartedAt}
                        simulateTransactionResponse={{
                            type: 'simulated',
                            simulation,
                        }}
                        transactionRequest={transactionRequest}
                        onMsg={onMsg}
                        pollingInterval={pollingInterval}
                        pollableData={pollableData}
                    />

                    <Actions variant="default">
                        <Button
                            size="regular"
                            variant="secondary"
                            onClick={() =>
                                onMsg({
                                    type: 'on_cancel_confirm_transaction_clicked',
                                })
                            }
                        >
                            <FormattedMessage
                                id="action.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>

                        <ActionButton
                            nonce={nonce}
                            gasEstimate={gasEstimate}
                            pollableData={pollableData}
                            transactionRequest={transactionRequest}
                            simulationResult={{ type: 'simulated', simulation }}
                            onMsg={onMsg}
                            keystore={getKeyStore({
                                keyStoreMap: keystores,
                                address: transactionRequest.account.address,
                            })}
                        />
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}

const Footer = ({
    safetyCheckResult,
    simulation,
    networkMap,
    onMsg,
}: {
    safetyCheckResult: TransactionSafetyCheckResult
    simulation: SimulateTransactionResponse
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}) => {
    const { transaction, currencies, checks } = simulation

    switch (transaction.type) {
        case 'deploy_earn_account':
        case 'earn_recharge_configured':
        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
        case 'earn_withdraw':
        case 'earn_recharge_disabled':
        case 'earn_recharge_updated':
        case 'card_cashback_deposit':
        case 'smart_wallet_activation':
        case 'WithdrawalTrx':
        case 'CardTopUpTrx':
        case 'card_top_up_from_earn':
        case 'swaps_io_native_token_swap':
            return null
        case 'ApprovalTransaction':
        case 'SingleNftApprovalTransaction':
        case 'NftCollectionApprovalTransaction':
            return (
                <Column spacing={0}>
                    <Group variant="default">
                        <Column spacing={0}>
                            <Text
                                variant="paragraph"
                                weight="regular"
                                color="textSecondary"
                            >
                                <FormattedMessage
                                    id="simulation.approve.footer.for"
                                    defaultMessage="For"
                                />
                            </Text>
                            <ListItem
                                safetyChecks={checks}
                                smartContract={transaction.approveTo}
                                networkMap={networkMap}
                            />
                        </Column>
                    </Group>

                    <TransactionStatusButton
                        safetyCheckResult={safetyCheckResult}
                        knownCurrencies={currencies}
                        onClick={() => onMsg({ type: 'safety_checks_clicked' })}
                    />
                </Column>
            )
        case 'UnknownTransaction':
        case 'FailedTransaction':
        case 'P2PTransaction':
        case 'P2PNftTransaction':
        case 'BridgeTrx':
            return (
                <TransactionStatusButton
                    safetyCheckResult={safetyCheckResult}
                    knownCurrencies={currencies}
                    onClick={() => onMsg({ type: 'safety_checks_clicked' })}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(transaction)
    }
}

const TransactionInfo = ({
    simulation,
    accounts,
    keystores,
    originalEthSendTransaction,
    onMsg,
    installationId,
    networkMap,
    dApp,
}: {
    simulation: SimulateTransactionResponse
    dApp: DAppSiteInfo | null
    accounts: AccountsMap
    originalEthSendTransaction: EthSendTransaction
    keystores: KeyStoreMap
    onMsg: (msg: Msg) => void
    installationId: string
    networkMap: NetworkMap
}) => {
    const { transaction, checks, currencies: knownCurrencies } = simulation

    switch (transaction.type) {
        case 'earn_recharge_configured':
        case 'earn_recharge_updated':
            return <EarnRechargeConfiguredTrxView transaction={transaction} />
        case 'earn_recharge_disabled':
            return null

        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
            return <EarnDepositTrxView transaction={transaction} />

        case 'earn_withdraw':
            return <EarnWithdrawalTrxView transaction={transaction} />

        case 'card_cashback_deposit':
            return <CardCashbackDepositTrxView transaction={transaction} />

        case 'deploy_earn_account':
            return (
                <TakerSubtextListItemNoBalance
                    taker={transaction.taker}
                    takerApyMap={transaction.takerApyMap}
                />
            )

        case 'card_top_up_from_earn':
            return <CardTopUpFromEarnTrxView transaction={transaction} />

        case 'swaps_io_native_token_swap':
            return (
                <SwapsIONativeTokenSwapView
                    networkMap={networkMap}
                    transaction={transaction}
                />
            )

        case 'WithdrawalTrx':
            return (
                <OffRampTransactionView
                    variant={{ type: 'no_status' }}
                    networkMap={networkMap}
                    withdrawalRequest={transaction.withdrawalRequest}
                />
            )

        case 'BridgeTrx':
            return (
                <BridgeTrxView
                    networkMap={networkMap}
                    transaction={transaction}
                />
            )
        case 'P2PTransaction':
        case 'P2PNftTransaction':
            return (
                <P2PTransactionView
                    installationId={installationId}
                    networkMap={networkMap}
                    transaction={transaction}
                    dApp={dApp}
                    knownCurrencies={knownCurrencies}
                    checks={checks}
                    accounts={accounts}
                    keystores={keystores}
                />
            )

        case 'ApprovalTransaction':
            return (
                <EditableApprove
                    originalEthSendTransaction={originalEthSendTransaction}
                    checks={checks}
                    knownCurrencies={knownCurrencies}
                    transaction={transaction}
                    onMsg={onMsg}
                />
            )

        case 'UnknownTransaction':
            return (
                <Unknown
                    networkMap={networkMap}
                    checks={checks}
                    knownCurrencies={knownCurrencies}
                    transaction={transaction}
                />
            )

        case 'FailedTransaction':
            return <Failed dApp={dApp} transaction={transaction} />

        case 'SingleNftApprovalTransaction':
            return (
                <NftListItem
                    networkMap={networkMap}
                    nft={transaction.nft}
                    checks={checks}
                    rightNode={null}
                />
            )

        case 'NftCollectionApprovalTransaction':
            return (
                <NftCollectionListItem
                    networkMap={networkMap}
                    checks={checks}
                    nftCollection={transaction.nftCollectionInfo}
                />
            )
        case 'CardTopUpTrx':
            return (
                <CardTopUpTrxView transaction={transaction} checks={checks} />
            )
        case 'smart_wallet_activation':
            return <SmartWalletActivationTrxView transaction={transaction} />

        /* istanbul ignore next */
        default:
            return notReachable(transaction)
    }
}
