import { notReachable } from '@zeal/toolkit'
import { withDelay } from '@zeal/toolkit/Function'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { fetchCurrentNonce } from '@zeal/domains/RPCRequest/api/fetchCurrentNonce'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { NotSigned } from '@zeal/domains/TransactionRequest'
import { fetchGasEstimate } from '@zeal/domains/Transactions/api/fetchGasEstimate'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import {
    FetchSimulationByRequest,
    SimulationResult,
} from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'

import { Flow } from './Flow'
import {
    Msg as SkeletonMsg,
    Skeleton,
    State as SkeletonState,
} from './Skeleton'

type Props = {
    installationId: string
    actionSource: ActionSource2
    transactionRequest: NotSigned
    state: State

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap

    accounts: AccountsMap
    keystores: KeyStoreMap

    fetchSimulationByRequest: FetchSimulationByRequest

    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

const fetch = async ({
    request,
    fetchSimulationByRequest,
    networkMap,
    networkRPCMap,
    dApp,
    defaultCurrencyConfig,
}: {
    request: NotSigned
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    fetchSimulationByRequest: FetchSimulationByRequest
    dApp: DAppSiteInfo | null
}): Promise<{
    simulation: SimulationResult
    nonce: number
    gasEstimate: Hexadecimal.Hexadecimal
}> => {
    const network = findNetworkByHexChainId(request.networkHexId, networkMap)
    const [simulation, nonce, gasEstimate] = await Promise.all([
        fetchSimulationByRequest({
            network,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            dApp,
            requestToSimulate: {
                type: 'rpc_request',
                rpcRequest: request.rpcRequest,
            },
        }),
        fetchCurrentNonce({
            network,
            networkRPCMap,
            address: request.account.address,
        }),
        fetchGasEstimate({
            network,
            networkRPCMap,
            rpcRequest: request.rpcRequest,
        }),
    ])

    return { simulation, nonce, gasEstimate }
}

type State = SkeletonState

export type Msg =
    | Extract<
          MsgOf<typeof Flow>,
          {
              type:
                  | 'drag'
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_expand_request'
                  | 'on_minimize_click'
                  | 'user_confirmed_transaction_for_signing'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
          }
      >
    | SkeletonMsg

const SIMULATION_ANIMATION_TIME_MS = 1000

export const ConfirmTransaction = ({
    defaultCurrencyConfig,
    actionSource,
    transactionRequest,
    state,
    accounts,
    keystores,
    fetchSimulationByRequest,

    networkMap,
    networkRPCMap,
    feePresetMap,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(
        withDelay(fetch, SIMULATION_ANIMATION_TIME_MS),
        {
            type: 'loading',
            params: {
                request: transactionRequest,
                fetchSimulationByRequest: fetchSimulationByRequest,
                networkMap,
                networkRPCMap,
                dApp: actionSource.dAppSiteInfo || null,
                defaultCurrencyConfig,
            },
        }
    )

    switch (loadable.type) {
        case 'loading':
            return (
                <Skeleton
                    network={findNetworkByHexChainId(
                        transactionRequest.networkHexId,
                        networkMap
                    )}
                    installationId={installationId}
                    state={state}
                    actionSource={actionSource}
                    account={loadable.params.request.account}
                    onMsg={onMsg}
                />
            )

        case 'loaded':
            return (
                <Flow
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    feePresetMap={feePresetMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    accounts={accounts}
                    keystores={keystores}
                    state={state}
                    transactionRequest={loadable.params.request}
                    simulation={loadable.data.simulation}
                    nonce={loadable.data.nonce}
                    gasEstimate={loadable.data.gasEstimate}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'drag':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_expand_request':
                            case 'on_minimize_click':
                            case 'user_confirmed_transaction_for_signing':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                                onMsg(msg)
                                break

                            case 'on_retry_button_clicked':
                            case 'on_transaction_simulation_retry_clicked':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        request: loadable.params.request,
                                        dApp: loadable.params.dApp,
                                        fetchSimulationByRequest,
                                        networkMap,
                                        networkRPCMap,
                                        defaultCurrencyConfig,
                                    },
                                })
                                break
                            case 'on_edit_approval_form_submit':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        defaultCurrencyConfig,
                                        request: {
                                            ...loadable.params.request,
                                            rpcRequest:
                                                msg.updatedEthSendTransaction,
                                        },
                                        dApp: loadable.params.dApp,
                                        fetchSimulationByRequest,
                                        networkMap,
                                        networkRPCMap,
                                    },
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'error':
            return (
                <>
                    <Skeleton
                        installationId={installationId}
                        network={findNetworkByHexChainId(
                            transactionRequest.networkHexId,
                            networkMap
                        )}
                        state={state}
                        actionSource={actionSource}
                        account={loadable.params.request.account}
                        onMsg={onMsg}
                    />

                    <AppErrorPopup
                        installationId={installationId}
                        error={parseAppError(loadable.error)}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({
                                        type: 'on_cancel_confirm_transaction_clicked',
                                    })
                                    break

                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
