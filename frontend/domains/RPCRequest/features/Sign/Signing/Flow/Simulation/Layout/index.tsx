import React from 'react'

import { Column } from '@zeal/uikit/Column'
import { Content as UiKitContent } from '@zeal/uikit/Content'
import { Screen } from '@zeal/uikit/Screen'

import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { KeyStore } from '@zeal/domains/KeyStore'
import { SignActionSource } from '@zeal/domains/Main'
import { NetworkMap } from '@zeal/domains/Network'
import { SignMessageRequest } from '@zeal/domains/RPCRequest'
import { SignMessageSimulationResultSimulated } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { Content } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/components/Content'
import { Header } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/components/Header'
import { ActionBar } from '@zeal/domains/Transactions/components/ActionBar'

import { ActionButtons } from './ActionButtons'
import { Footer } from './Footer'

type Props = {
    simulatedResult: SignMessageSimulationResultSimulated
    account: Account
    keyStore: KeyStore
    isLoading: boolean
    request: SignMessageRequest
    networkMap: NetworkMap
    nowTimestampMs: number
    actionSource: SignActionSource
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof Header>
    | MsgOf<typeof Footer>
    | MsgOf<typeof ActionButtons>
    | { type: 'on_minimize_click' }

export const Layout = ({
    onMsg,
    keyStore,
    isLoading,
    request,
    networkMap,
    account,
    simulatedResult,
    nowTimestampMs,
    actionSource,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'on_minimize_click' })}
        >
            <Column spacing={12} shrink alignY="stretch">
                <ActionBar
                    title={null}
                    account={account}
                    actionSourceType={actionSource.type}
                    network={null}
                    onMsg={onMsg}
                />
                <UiKitContent
                    header={
                        <Header
                            dApp={actionSource.dAppSiteInfo}
                            onMsg={onMsg}
                            simulatedSignMessage={
                                simulatedResult.simulationResponse.message
                            }
                        />
                    }
                    footer={
                        <Footer
                            networkMap={networkMap}
                            knownCurrencies={
                                simulatedResult.simulationResponse.currencies
                            }
                            safetyChecks={
                                simulatedResult.simulationResponse.checks
                            }
                            simulatedSignMessage={
                                simulatedResult.simulationResponse.message
                            }
                            onMsg={onMsg}
                        />
                    }
                >
                    <Content
                        networkMap={networkMap}
                        nowTimestampMs={nowTimestampMs}
                        request={request}
                        result={simulatedResult}
                    />
                </UiKitContent>

                <ActionButtons
                    request={request}
                    isLoading={isLoading}
                    keyStore={keyStore}
                    simulatedResult={simulatedResult}
                    onMsg={onMsg}
                />
            </Column>
        </Screen>
    )
}
