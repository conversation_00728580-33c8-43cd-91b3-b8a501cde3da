import React from 'react'

import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { OrderBuyView } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderBuyView'
import { OrderCardTopUpView } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderCardTopUpView'
import { OrderEarnDepositBridgeView } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderEarnDepositBridgeView'
import { NetworkMap } from '@zeal/domains/Network'
import { SignMessageRequest } from '@zeal/domains/RPCRequest'
import { SignMessageSimulationResult } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { RawMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/components/RawMessage'

import { PermitAllowanceItem } from './PermitAllowanceItem'

type Props = {
    request: SignMessageRequest
    result: SignMessageSimulationResult
    networkMap: NetworkMap
    nowTimestampMs: number
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof PermitAllowanceItem>

export const Layout = ({
    networkMap,
    result,
    request,
    nowTimestampMs,
    onMsg,
}: Props) => {
    switch (result.type) {
        case 'not_supported':
        case 'failed':
            return <RawMessage request={request} />
        case 'simulated': {
            const simulatedSignMessage = result.simulationResponse.message
            switch (simulatedSignMessage.type) {
                case 'Permit2SignMessage':
                    return (
                        <Column spacing={8}>
                            {simulatedSignMessage.allowances.map(
                                (permitAllowance, index) => (
                                    <React.Fragment
                                        key={`${permitAllowance.amount.amount.currency.id}${index}`}
                                    >
                                        <PermitAllowanceItem
                                            permitAllowance={permitAllowance}
                                            safetyChecks={
                                                result.simulationResponse.checks
                                            }
                                            nowTimestampMs={nowTimestampMs}
                                            onMsg={onMsg}
                                        />

                                        {index !==
                                            simulatedSignMessage.allowances
                                                .length -
                                                1 && (
                                            <Divider variant="secondary" />
                                        )}
                                    </React.Fragment>
                                )
                            )}
                        </Column>
                    )

                case 'PermitSignMessage':
                case 'DaiPermitSignMessage':
                    return (
                        <PermitAllowanceItem
                            permitAllowance={simulatedSignMessage.allowance}
                            safetyChecks={result.simulationResponse.checks}
                            nowTimestampMs={nowTimestampMs}
                            onMsg={onMsg}
                        />
                    )

                case 'OrderCardTopupSignMessage':
                    return (
                        <OrderCardTopUpView
                            networkMap={networkMap}
                            simulatedSignMessage={simulatedSignMessage}
                        />
                    )
                case 'OrderEarnDepositBridge':
                    return (
                        <OrderEarnDepositBridgeView
                            simulatedSignMessage={simulatedSignMessage}
                        />
                    )
                case 'OrderBuySignMessage':
                    return (
                        <OrderBuyView
                            networkMap={networkMap}
                            simulatedSignMessage={simulatedSignMessage}
                        />
                    )

                case 'UnknownSignMessage':
                    return <RawMessage request={request} />

                default:
                    return notReachable(simulatedSignMessage)
            }
        }
        default:
            return notReachable(result)
    }
}
