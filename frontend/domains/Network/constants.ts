import { ImperativeError } from '@zeal/toolkit/Error'
import { values } from '@zeal/toolkit/Object'
import { staticFromString } from '@zeal/toolkit/Web3/address'

import { initCustomCurrency } from '@zeal/domains/Currency/helpers/initCustomCurrency'
import {
    Network,
    NetworkHexId,
    NetworkMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import { getChainIdNumber } from '@zeal/domains/Network/helpers/getChainIdNumber'

import { KNOWN_NETWORKS, KnownNetwork } from './chains'

export const KNOWN_NETWORKS_MAP: Record<string, KnownNetwork> =
    KNOWN_NETWORKS.reduce(
        (acc, network) => ({
            ...acc,
            [network.hexChainId]: network,
        }),
        {}
    )

// Do not export this one, it is used only for not to forget to add new networks to the array if new type is added
const PREDEFINED_NETWORKS_MAP: {
    [K in PredefinedNetwork['name']]: Omit<PredefinedNetwork, 'name'> & {
        name: K
    }
} = {
    Ethereum: {
        type: 'predefined',
        blockExplorerUrl: 'https://etherscan.io/',
        hexChainId: '0x1' as NetworkHexId,
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Ethereum|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
            name: 'ETH',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0x1' as NetworkHexId,
        },
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'Ethereum',
        trxType: 'eip1559',
        blockTimeMS: 15000,
    },
    Polygon: {
        type: 'predefined',
        blockExplorerUrl: 'https://polygonscan.com/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Polygon|******************************************',
            symbol: 'POL',
            code: 'POL',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Polygon|******************************************',
            name: 'POL',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 42,
            networkHexChainId: '0x89' as NetworkHexId,
        },
        hexChainId: '0x89' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'Polygon',
        trxType: 'eip1559',
        blockTimeMS: 2000,
    },
    PolygonZkevm: {
        type: 'predefined',
        blockExplorerUrl: 'https://zkevm.polygonscan.com/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'PolygonZkevm|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/PolygonZkevm|******************************************',
            name: 'ETH',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0x44d' as NetworkHexId,
        },
        hexChainId: '0x44d' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' }, // TODO: @Nicvaniek - disabled until we find a fix for https://discord.com/channels/961390365708009524/1217199787980357743/1222436704884101181
        name: 'PolygonZkevm',
        trxType: 'legacy',
        blockTimeMS: 2000,
    },
    Linea: {
        type: 'predefined',
        blockExplorerUrl: 'https://lineascan.build/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Linea|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Linea|******************************************',
            name: 'ETH',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0xe708' as NetworkHexId,
        },
        hexChainId: '0xe708' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'Linea',
        trxType: 'eip1559',
        blockTimeMS: 3000,
    },
    Arbitrum: {
        type: 'predefined',
        blockExplorerUrl: 'https://arbiscan.io/',
        nativeCurrency: {
            symbol: 'ETH',
            fraction: 18,
            name: 'ETH',
            marketCapRank: 2,
            type: 'CryptoCurrency',
            rateFraction: 18,
            id: 'Arbitrum|******************************************',
            address: staticFromString(
                '******************************************'
            ),
            code: 'ETH',
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Arbitrum|******************************************',
            networkHexChainId: '0xa4b1' as NetworkHexId,
        },
        hexChainId: '0xa4b1' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'Arbitrum',
        trxType: 'legacy',
        blockTimeMS: 2000,
    },
    zkSync: {
        type: 'predefined',
        blockExplorerUrl: 'https://explorer.zksync.io/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'zkSync|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/zkSync|******************************************',
            name: 'ETH',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0x144' as NetworkHexId,
        },
        hexChainId: '0x144' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'zkSync',
        trxType: 'legacy',
        blockTimeMS: 1000,
    },
    Aurora: {
        type: 'predefined',
        blockExplorerUrl: 'https://aurorascan.dev/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Aurora|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: '', // FIXME: @Kat - need aurora eth pic plz
            name: 'Aurora',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0x4e454152' as NetworkHexId,
        },
        hexChainId: '0x4e454152' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'Aurora',
        trxType: 'legacy',
        blockTimeMS: 2000,
    },
    Avalanche: {
        type: 'predefined',
        blockExplorerUrl: 'https://snowtrace.io/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Avalanche|******************************************',
            symbol: 'AVAX',
            code: 'AVAX',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Avalanche|******************************************',
            name: 'AVAX',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 17,
            networkHexChainId: '0xa86a' as NetworkHexId,
        },
        hexChainId: '0xa86a' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'Avalanche',
        trxType: 'eip1559',
        blockTimeMS: 3000,
    },
    BSC: {
        type: 'predefined',
        blockExplorerUrl: 'https://bscscan.com/',
        nativeCurrency: {
            symbol: 'BNB',
            fraction: 18,
            name: 'Binance',
            marketCapRank: 4,
            type: 'CryptoCurrency',
            rateFraction: 18,
            id: 'BSC|******************************************',
            address: staticFromString(
                '******************************************'
            ),
            code: 'BNB',
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/BSC|******************************************',
            networkHexChainId: '0x38' as NetworkHexId,
        },
        hexChainId: '0x38' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'BSC',
        trxType: 'legacy',
        blockTimeMS: 3000,
    },
    Celo: {
        type: 'predefined',
        blockExplorerUrl: 'https://celoscan.io/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Celo|******************************************',
            symbol: 'CELO',
            code: 'CELO',
            fraction: 18,
            rateFraction: 18,
            icon: '', // FIXME: @Kat - need CELO pic plz
            name: 'Celo',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0xa4ec' as NetworkHexId,
        },
        hexChainId: '0xa4ec' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'Celo',
        trxType: 'legacy',
        blockTimeMS: 5000,
    },
    Fantom: {
        type: 'predefined',
        blockExplorerUrl: 'https://ftmscan.com/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Fantom|******************************************',
            symbol: 'FTM',
            code: 'FTM',
            fraction: 18,
            rateFraction: 18,
            icon: '',
            name: 'FANTOM',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0xfa' as NetworkHexId,
        },
        hexChainId: '0xfa' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'Fantom',
        trxType: 'legacy',
        blockTimeMS: 2000,
    },
    Gnosis: {
        type: 'predefined',
        blockExplorerUrl: 'https://gnosisscan.io/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Gnosis|******************************************',
            address: staticFromString(
                '******************************************'
            ),
            symbol: 'xDAI',
            code: 'XDAI',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
            name: 'XDAI',
            marketCapRank: null,
            networkHexChainId: '0x64' as NetworkHexId,
        },
        hexChainId: '0x64' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: true,
        },
        name: 'Gnosis',
        trxType: 'eip1559',
        blockTimeMS: 5000,
    },
    Optimism: {
        type: 'predefined',
        blockExplorerUrl: 'https://optimistic.etherscan.io/',
        nativeCurrency: {
            symbol: 'ETH',
            fraction: 18,
            name: 'ETH',
            marketCapRank: 2,
            type: 'CryptoCurrency',
            rateFraction: 18,
            id: 'Optimism|******************************************',
            address: staticFromString(
                '******************************************'
            ),
            code: 'ETH',
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Optimism|******************************************',
            networkHexChainId: '0xa' as NetworkHexId,
        },
        hexChainId: '0xa' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: true,
        },
        name: 'Optimism',
        trxType: 'legacy',
        blockTimeMS: 3000,
    },
    Base: {
        type: 'predefined',
        blockExplorerUrl: 'https://basescan.org/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Base|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Base|******************************************',
            name: 'ETH',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0x2105' as NetworkHexId,
        },
        hexChainId: '0x2105' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'Base',
        trxType: 'legacy',
        blockTimeMS: 2000,
    },
    Blast: {
        type: 'predefined',
        blockExplorerUrl: 'https://blastscan.io/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Blast|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Blast|******************************************',
            name: 'BLAST',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0x13e31' as NetworkHexId,
        },
        hexChainId: '0x13e31' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        name: 'Blast',
        trxType: 'legacy',
        blockTimeMS: 2000,
    },
    OPBNB: {
        type: 'predefined',
        blockExplorerUrl: 'https://opbnbscan.com/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'OPBNB|******************************************',
            symbol: 'BNB',
            code: 'BNB',
            fraction: 18,
            rateFraction: 18,
            icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/OPBNB|******************************************',
            name: 'BNB',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 6,
            networkHexChainId: '0xcc' as NetworkHexId,
        },
        hexChainId: '0xcc' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'OPBNB',
        trxType: 'legacy',
        blockTimeMS: 2000,
    },
    Cronos: {
        type: 'predefined',
        blockExplorerUrl: 'https://cronoscan.com/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Cronos|******************************************',
            symbol: 'CRO',
            code: 'CRO',
            fraction: 18,
            rateFraction: 18,
            icon: '',
            name: 'Cronos',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0x19' as NetworkHexId,
        },
        hexChainId: '0x19' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'Cronos',
        trxType: 'legacy',
        blockTimeMS: 5000,
    },
    Mantle: {
        type: 'predefined',
        blockExplorerUrl: 'https://mantlescan.info/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Mantle|******************************************',
            symbol: 'MNT',
            code: 'MNT',
            fraction: 18,
            rateFraction: 18,
            icon: '',
            name: 'MNT',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: null,
            networkHexChainId: '0x1388' as NetworkHexId,
        },
        hexChainId: '0x1388' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'Mantle',
        trxType: 'eip1559',
        blockTimeMS: 1000,
    },
    Manta: {
        type: 'predefined',
        blockExplorerUrl: 'https://manta.socialscan.io/',
        nativeCurrency: {
            type: 'CryptoCurrency',
            id: 'Manta|******************************************',
            symbol: 'ETH',
            code: 'ETH',
            fraction: 18,
            rateFraction: 18,
            icon: '',
            name: 'ETH',
            address: staticFromString(
                '******************************************'
            ),
            marketCapRank: 2,
            networkHexChainId: '0xa9' as NetworkHexId,
        },
        hexChainId: '0xa9' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        name: 'Manta',
        trxType: 'legacy',
        blockTimeMS: 10000,
    },
}

// Do not export this one, it is used only for not to forget to add new networks to the array if new type is added
const TEST_NETWORKS_MAP: {
    [K in TestNetwork['name']]: Omit<TestNetwork, 'name'> & {
        name: K
    }
} = {
    AvalancheFuji: {
        type: 'testnet',
        blockExplorerUrl: 'https://testnet.snowtrace.io/',
        hexChainId: '0xa869' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        trxType: 'eip1559',
        name: 'AvalancheFuji',
        blockTimeMS: 3000,
        nativeCurrency: initCustomCurrency({
            address: staticFromString(
                '******************************************'
            ),
            networkHexChainId: '0xa869' as NetworkHexId,
            fraction: 18,
            id: `AvalancheFuji|******************************************`,
            symbol: 'AVAX',
            icon: null,
        }),
    },
    BscTestnet: {
        type: 'testnet',
        blockExplorerUrl: 'https://testnet.bscscan.com/',
        hexChainId: '0x61' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        trxType: 'legacy',
        name: 'BscTestnet',
        blockTimeMS: 3000,
        nativeCurrency: initCustomCurrency({
            address: staticFromString(
                '******************************************'
            ),
            networkHexChainId: '0x61' as NetworkHexId,
            fraction: 18,
            id: `BscTestnet|******************************************`,
            symbol: 'BNB',
            icon: null,
        }),
    },
    EthereumSepolia: {
        type: 'testnet',
        blockExplorerUrl: 'https://sepolia.etherscan.io/',
        hexChainId: '0xaa36a7' as NetworkHexId,
        isSimulationSupported: true,
        isZealRPCSupported: true,
        smartWalletSupport: {
            type: 'supported',
            isSponsorshipSupported: false,
        },
        trxType: 'eip1559',
        name: 'EthereumSepolia',
        blockTimeMS: 15000,
        nativeCurrency: initCustomCurrency({
            address: staticFromString(
                '******************************************'
            ),
            networkHexChainId: '0xaa36a7' as NetworkHexId,
            fraction: 18,
            id: `EthereumSepolia|******************************************`,
            symbol: 'spETH',
            icon: null,
        }),
    },
    FantomTestnet: {
        type: 'testnet',
        blockExplorerUrl: 'https://testnet.ftmscan.com/',
        hexChainId: '0xfa2' as NetworkHexId,
        isSimulationSupported: false,
        isZealRPCSupported: true,
        smartWalletSupport: { type: 'not_supported' },
        trxType: 'legacy',
        name: 'FantomTestnet',
        blockTimeMS: 2000,
        nativeCurrency: initCustomCurrency({
            address: staticFromString(
                '******************************************'
            ),
            networkHexChainId: '0xfa2' as NetworkHexId,
            fraction: 18,
            id: `FantomTestnet|******************************************`,
            symbol: 'FTM',
            icon: null,
        }),
    },
}

export const ARBITRUM = PREDEFINED_NETWORKS_MAP['Arbitrum']
export const AVALANCHE = PREDEFINED_NETWORKS_MAP['Avalanche']
export const BASE = PREDEFINED_NETWORKS_MAP['Base']
export const BLAST = PREDEFINED_NETWORKS_MAP['Blast']
export const POLYGON = PREDEFINED_NETWORKS_MAP['Polygon']
export const OPTIMISM = PREDEFINED_NETWORKS_MAP['Optimism']
export const GNOSIS = PREDEFINED_NETWORKS_MAP['Gnosis']
export const ETHEREUM = PREDEFINED_NETWORKS_MAP['Ethereum']
export const BSC = PREDEFINED_NETWORKS_MAP['BSC']
export const OPBNB = PREDEFINED_NETWORKS_MAP['OPBNB']
export const LINEA = PREDEFINED_NETWORKS_MAP['Linea']
export const ZKSYNC = PREDEFINED_NETWORKS_MAP['zkSync']
export const POLYGON_ZKEVM = PREDEFINED_NETWORKS_MAP['PolygonZkevm']
export const FANTOM = PREDEFINED_NETWORKS_MAP['Fantom']
export const CRONOS = PREDEFINED_NETWORKS_MAP['Cronos']
export const MANTLE = PREDEFINED_NETWORKS_MAP['Mantle']
export const MANTA = PREDEFINED_NETWORKS_MAP['Manta']
export const AURORA = PREDEFINED_NETWORKS_MAP['Aurora']
export const CELO = PREDEFINED_NETWORKS_MAP['Celo']

export const PREDEFINED_NETWORKS: PredefinedNetwork[] = values(
    PREDEFINED_NETWORKS_MAP
)

export const TEST_NETWORKS: TestNetwork[] = values(TEST_NETWORKS_MAP)

export const PREDEFINED_AND_TEST_NETWORKS: (PredefinedNetwork | TestNetwork)[] =
    [...PREDEFINED_NETWORKS, ...TEST_NETWORKS]

export const SMART_WALLET_REFERENCE_NETWORK = PREDEFINED_NETWORKS_MAP['Gnosis']

export const findNetworkByNumber = (
    networkId: number
): TestNetwork | PredefinedNetwork => {
    const network = PREDEFINED_AND_TEST_NETWORKS.find(
        (n) => getChainIdNumber(n) === networkId
    )
    if (!network) {
        throw new ImperativeError(`cannot find network id`, { networkId })
    }
    return network
}

export const findNetworkByHexChainId = (
    hexChanId: NetworkHexId,
    networkMap: NetworkMap
): Network => {
    const network = networkMap[hexChanId]
    if (!network) {
        throw new ImperativeError(`cannot find network for hexchainid`, {
            hexChanId,
        })
    }
    return network
}

export const DEFAULT_NETWORK = GNOSIS

// https://docs.coingecko.com/reference/asset-platforms-list
export const COIN_GECKO_NETWORK_MAP: Record<PredefinedNetwork['name'], string> =
    {
        Ethereum: 'ethereum',
        Polygon: 'polygon-pos',
        PolygonZkevm: 'polygon-zkevm',
        Linea: 'linea',
        Arbitrum: 'arbitrum-one',
        zkSync: 'zksync',
        Aurora: 'aurora',
        Avalanche: 'avalanche',
        BSC: 'binance-smart-chain',
        Celo: 'celo',
        Fantom: 'fantom',
        Gnosis: 'xdai',
        Optimism: 'optimistic-ethereum',
        Base: 'base',
        Blast: 'blast',
        OPBNB: 'opbnb',
        Cronos: 'cronos',
        Mantle: 'mantle',
        Manta: 'manta-pacific',
    }

export const DEFAULT_NETWORK_HEX_IDS_TO_SPONSOR = [GNOSIS.hexChainId]
