import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'

import { CardConfig } from '@zeal/domains/Card'
import { BReward, ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    BRewardDiagnostics,
    CardConfigDiagnostics,
    Diagnostics,
    ReferralConfigDiagnostics,
    Storage,
} from '@zeal/domains/Storage'
import { fetchStorage } from '@zeal/domains/Storage/api/fetchStorage'

const bRewardsToDiagnostics = (rewards: BReward): BRewardDiagnostics => {
    switch (rewards.type) {
        case 'not_eligible':
            return { type: 'not_eligible' }
        case 'in_progress':
            return {
                type: 'in_progress',
                firstCardActivatedTimestampMS:
                    rewards.firstCardActivatedTimestampMS,
                spentAmount: toFixedWithFraction(
                    rewards.spent.amount,
                    rewards.spent.currency.fraction
                ),
            }
        case 'ready_to_claim':
            return {
                type: 'ready_to_claim',
                firstCardActivatedTimestampMS:
                    rewards.firstCardActivatedTimestampMS,
                spentAmount: toFixedWithFraction(
                    rewards.spent.amount,
                    rewards.spent.currency.fraction
                ),
            }
        case 'expired':
            return {
                type: 'expired',
                firstCardActivatedTimestampMS:
                    rewards.firstCardActivatedTimestampMS,
                spentAmount: toFixedWithFraction(
                    rewards.spent.amount,
                    rewards.spent.currency.fraction
                ),
                dismissed: rewards.dismissed,
            }
        case 'claimed':
            return { type: 'claimed' }
        default:
            return notReachable(rewards)
    }
}

const cardConfigToDiagnostics = (
    cardConfig: CardConfig
): CardConfigDiagnostics => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            return { type: 'card_readonly_signer_address_is_not_selected' }
        case 'card_readonly_signer_address_is_selected':
            return {
                type: 'card_readonly_signer_address_is_selected',
                readonlySignerAddress: cardConfig.readonlySignerAddress,
            }
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return {
                type: 'card_readonly_signer_address_is_selected_fully_onboarded',
                readonlySignerAddress: cardConfig.readonlySignerAddress,
                cardCurrencyCode: cardConfig.currency.code,
                cardSafeAddress: cardConfig.lastSeenSafeAddress,
                isCreatedViaZeal: cardConfig.isCreatedViaZeal,
                gnosisPayUserId: cardConfig.userId,
                rewards: bRewardsToDiagnostics(cardConfig.rewards),
            }
        default:
            return notReachable(cardConfig)
    }
}

const referralConfigToDiagnostics = (
    referralConfig: ReferralConfig
): ReferralConfigDiagnostics => {
    const userA: ReferralConfigDiagnostics['userA'] = (() => {
        switch (referralConfig.userA.type) {
            case 'not_configured':
                return { type: 'not_configured' }
            case 'configured':
                return {
                    type: 'configured',
                    referralCode: referralConfig.userA.referralCode,
                    shareableLink: referralConfig.userA.shareableLink,
                    ownerAddress: referralConfig.userA.ownerAddress,
                }
            default:
                return notReachable(referralConfig.userA)
        }
    })()

    const userB: ReferralConfigDiagnostics['userB'] = (() => {
        switch (referralConfig.userB.type) {
            case 'not_configured':
                return { type: 'not_configured' }
            case 'configured':
                return {
                    type: 'configured',
                    referralCode: referralConfig.userB.referralCode,
                }
            default:
                return notReachable(referralConfig.userB)
        }
    })()

    return { userA, userB }
}

const _added_diagnostics: Record<
    keyof Storage,
    'yes_i_added_diagnostics_if_needed'
> = {
    selectedAddress: 'yes_i_added_diagnostics_if_needed',
    fetchedAt: 'yes_i_added_diagnostics_if_needed',
    accounts: 'yes_i_added_diagnostics_if_needed',
    portfolios: 'yes_i_added_diagnostics_if_needed',
    keystoreMap: 'yes_i_added_diagnostics_if_needed',
    encryptedPassword: 'yes_i_added_diagnostics_if_needed',
    customCurrencies: 'yes_i_added_diagnostics_if_needed',
    dApps: 'yes_i_added_diagnostics_if_needed',
    transactionRequests: 'yes_i_added_diagnostics_if_needed',
    submitedBridges: 'yes_i_added_diagnostics_if_needed',
    submittedOffRampTransactions: 'yes_i_added_diagnostics_if_needed',
    swapSlippagePercent: 'yes_i_added_diagnostics_if_needed',
    customNetworkMap: 'yes_i_added_diagnostics_if_needed',
    networkRPCMap: 'yes_i_added_diagnostics_if_needed',
    bankTransferInfo: 'yes_i_added_diagnostics_if_needed',
    isOnboardingStorySeen: 'yes_i_added_diagnostics_if_needed',
    isEthereumNetworkFeeWarningSeen: 'yes_i_added_diagnostics_if_needed',
    feePresetMap: 'yes_i_added_diagnostics_if_needed',
    currencyHiddenMap: 'yes_i_added_diagnostics_if_needed',
    currencyPinMap: 'yes_i_added_diagnostics_if_needed',
    gasCurrencyPresetMap: 'yes_i_added_diagnostics_if_needed',
    cardConfig: 'yes_i_added_diagnostics_if_needed',
    browserTabState: 'yes_i_added_diagnostics_if_needed',
    notificationsConfig: 'yes_i_added_diagnostics_if_needed',
    totalEarningsInDefaultCurrencyMap: 'yes_i_added_diagnostics_if_needed',
    earnHistoricalTakerUserCurrencyRateMap: 'yes_i_added_diagnostics_if_needed',
    earnTakerMetrics: 'yes_i_added_diagnostics_if_needed',
    defaultCurrencyConfig: 'yes_i_added_diagnostics_if_needed',
    counterparties: 'yes_i_added_diagnostics_if_needed',
    celebrationConfig: 'yes_i_added_diagnostics_if_needed',
    swapsIOSwapRequestsMap: 'yes_i_added_diagnostics_if_needed',
    transactionActivitiesCacheMap: 'yes_i_added_diagnostics_if_needed',
    appRating: 'yes_i_added_diagnostics_if_needed',
}

export const fetchDiagnostics = async (): Promise<Diagnostics> => {
    const storageResult = await fetchStorage()

    const referralConfig = referralConfigToDiagnostics(
        storageResult.referralConfig
    )

    if (!storageResult.storage) {
        return {
            type: 'not_onboarded',
            installationId: storageResult.installationId,
            installationCampaign: storageResult.installationCampaign,
            referralConfig,
        }
    }

    const { storage } = storageResult

    return {
        type: 'onboarded',
        installationId: storageResult.installationId,
        installationCampaign: storageResult.installationCampaign,
        referralConfig,
        cardConfig: cardConfigToDiagnostics(storage.cardConfig),
    }
}
