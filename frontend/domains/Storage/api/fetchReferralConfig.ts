import { parse as parseJ<PERSON><PERSON> } from '@zeal/toolkit/JSON'
import {
    match,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as storage from '@zeal/toolkit/Storage'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    ReferralConfig,
    UserAReferralConfig,
    UserBReferralConfig,
} from '@zeal/domains/Card/domains/Reward'

import { REFERRAL_CONFIG_KEY } from '../constants'

const parseUserAReferralConfig = (
    input: unknown
): Result<unknown, UserAReferralConfig> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                type: match(obj.type, 'not_configured' as const),
            }).map((config) => ({ type: config.type }) as UserAReferralConfig),

            shape({
                type: match(obj.type, 'configured' as const),
                ownerAddress: Web3.address.parse(obj.ownerAddress),
                referralCode: string(obj.referralCode),
                shareableLink: string(obj.shareableLink),
            }).map(
                (config) =>
                    ({
                        type: config.type,
                        ownerAddress: config.ownerAddress,
                        referralCode: config.referralCode,
                        shareableLink: config.shareableLink,
                    }) as UserAReferralConfig
            ),
        ])
    )

const parseUserBReferralConfig = (
    input: unknown
): Result<unknown, UserBReferralConfig> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                type: match(obj.type, 'not_configured' as const),
            }).map((config) => ({ type: config.type }) as UserBReferralConfig),

            shape({
                type: match(obj.type, 'configured' as const),
                referralCode: string(obj.referralCode),
            }).map(
                (config) =>
                    ({
                        type: config.type,
                        referralCode: config.referralCode,
                    }) as UserBReferralConfig
            ),
        ])
    )

const parseReferralConfig = (input: unknown): Result<unknown, ReferralConfig> =>
    oneOf(input, [
        string(input)
            .andThen(parseJSON)
            .andThen(object)
            .andThen((obj) =>
                shape({
                    userA: oneOf(obj, [
                        parseUserAReferralConfig(obj.userA),
                        success({
                            type: 'not_configured',
                        } as UserAReferralConfig),
                    ]),
                    userB: oneOf(obj, [
                        parseUserBReferralConfig(obj.userB),
                        success({
                            type: 'not_configured',
                        } as UserBReferralConfig),
                    ]),
                })
            ),
        success({
            userA: { type: 'not_configured' },
            userB: { type: 'not_configured' },
        } as ReferralConfig),
    ])

export const fetchReferralConfig = async (): Promise<ReferralConfig> =>
    parseReferralConfig(
        await storage.local.get(REFERRAL_CONFIG_KEY)
    ).getSuccessResultOrThrow('Failed to parse referral config')
