import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import { CardConfig } from '@zeal/domains/Card'
import { CashbackCelebrationConfig } from '@zeal/domains/Card/domains/Cashback'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { CountryISOCode } from '@zeal/domains/Country'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyId,
    CurrencyPinMap,
    DefaultCurrency,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    SubmittedOfframpTransaction,
    UnblockLoginSignature,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { SubmitedBridgesMap } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOSwapRequestsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import {
    EarnCelebrationConfig,
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { SumSubAccessToken } from '@zeal/domains/KYC'
import { CustomNetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DAppConnectionState } from '@zeal/domains/Storage/domains/DAppConnectionState'
import { Submited } from '@zeal/domains/TransactionRequest'
import { TransactionActivitiesCacheMap } from '@zeal/domains/Transactions'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

// !!! :: storage should intentionally be separated from domains types to allow migrations in future

export type Storage = {
    selectedAddress: Address | null
    fetchedAt: Date
    accounts: AccountsMap
    portfolios: PortfolioMap
    keystoreMap: KeyStoreMap
    // encrypted session password with user password
    encryptedPassword: string
    customCurrencies: CustomCurrencyMap
    dApps: Record<string, DAppConnectionState>
    transactionRequests: Record<Address, Submited[]>
    submitedBridges: SubmitedBridgesMap
    submittedOffRampTransactions: SubmittedOfframpTransaction[]
    swapSlippagePercent: number | null
    customNetworkMap: CustomNetworkMap
    networkRPCMap: NetworkRPCMap
    bankTransferInfo: BankTransferInfo
    isOnboardingStorySeen: boolean
    isEthereumNetworkFeeWarningSeen: boolean
    feePresetMap: FeePresetMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    browserTabState: BrowserTabState
    notificationsConfig: NotificationsConfig
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    earnTakerMetrics: EarnTakerMetrics
    defaultCurrencyConfig: DefaultCurrencyConfig
    counterparties: Counterparty[]
    celebrationConfig: CelebrationConfig
    swapsIOSwapRequestsMap: SwapsIOSwapRequestsMap
    transactionActivitiesCacheMap: TransactionActivitiesCacheMap
    appRating: AppRating
}

// TODO @resetko-zeal maybe better naming for this partial storge which we use while user is not yet set his password/pin
export type OnboardingStorage = Pick<
    Storage,
    | 'accounts'
    | 'portfolios'
    | 'defaultCurrencyConfig'
    | 'customCurrencies'
    | 'networkRPCMap'
    | 'currencyHiddenMap'
    | 'keystoreMap'
>

export type DefaultCurrencyConfig = {
    defaultCurrency: DefaultCurrency
}

export type BrowserTabState = {
    lastVisitedURL: URL | null
}

export type NotificationsConfig = {
    bankTransfers: boolean
    cardPayments: boolean
    wallets: Record<Address, boolean>
}

// TODO @max-tern :: maybe to move to BankTransfer domain?
export type BankTransferInfo =
    | BankTransferSetupNotStarted
    | BankTransferUnblockUserCreated

export type BankTransferSetupNotStarted = {
    type: 'not_started'
}

export type BankTransferUnblockUserCreated = {
    type: 'unblock_user_created'
    unblockUserId: string
    countryCode: CountryISOCode | null
    connectedWalletAddress: Address
    sumSubAccessToken: SumSubAccessToken | null
    lastSignature: UnblockLoginSignature | null
}

export type CustomCurrencyMap = Record<CurrencyId, CryptoCurrency>

// TODO @max-tern Think if this should be moved to some other domain
export type StorageState =
    | { type: 'no_storage' }
    | { type: 'locked'; storage: Storage }
    | { type: 'unlocked'; sessionPassword: string; storage: Storage }

export type CelebrationConfig = {
    cashback: CashbackCelebrationConfig | null
    earn: EarnCelebrationConfig
}

export type BRewardDiagnostics =
    | { type: 'not_eligible' }
    | {
          type: 'in_progress'
          firstCardActivatedTimestampMS: number
          spentAmount: string
      }
    | {
          type: 'ready_to_claim'
          firstCardActivatedTimestampMS: number
          spentAmount: string
      }
    | {
          type: 'expired'
          firstCardActivatedTimestampMS: number
          dismissed: boolean
          spentAmount: string
      }
    | { type: 'claimed' }

export type CardConfigDiagnostics =
    | { type: 'card_readonly_signer_address_is_not_selected' }
    | {
          type: 'card_readonly_signer_address_is_selected'
          readonlySignerAddress: Web3.address.Address
      }
    | {
          type: 'card_readonly_signer_address_is_selected_fully_onboarded'
          readonlySignerAddress: Web3.address.Address
          cardCurrencyCode: string
          cardSafeAddress: Web3.address.Address
          isCreatedViaZeal: boolean
          gnosisPayUserId: string | null
          rewards: BRewardDiagnostics
      }

export type ReferralConfigDiagnostics = {
    userA:
        | { type: 'not_configured' }
        | {
              type: 'configured'
              ownerAddress: Web3.address.Address
              referralCode: string
              shareableLink: string
          }
    userB:
        | { type: 'not_configured' }
        | { type: 'configured'; referralCode: string }
}

export type Diagnostics =
    | {
          type: 'not_onboarded'
          installationId: string
          installationCampaign: string | null
          referralConfig: ReferralConfigDiagnostics
      }
    | {
          type: 'onboarded'
          installationId: string
          installationCampaign: string | null
          referralConfig: ReferralConfigDiagnostics
          cardConfig: CardConfigDiagnostics | null
      }
