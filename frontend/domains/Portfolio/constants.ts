import { staticFromString } from '@zeal/toolkit/Web3/address'

export const DEFI_PROTOCOL_TOKENS = [
    {
        address: staticFromString('0x0618d24902ace2708b6270cd2ae61f21f32924e1'),
        name: 'Augmented Debt WXDAI',
        symbol: 'agvWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xdf55522876e45adec168025a272c802faa9a1b0b'),
        name: 'Augmented Deposit GNO',
        symbol: 'agGNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Axol Gnosis',
        symbol: 'axlGNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x0142f6c3fca82f96c7cfde8e8b93824c349ae<PERSON><PERSON>'),
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        symbol: 'DX<PERSON>',
        type: 'DeF<PERSON>',
    },
    {
        address: staticFromString('******************************************'),
        name: 'BCoW AMM 50WETH-50GNO',
        symbol: 'BCoW-50WETH-50GNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Gyroscope ECLP GYD/AUSDC.e Aura Deposit Vault',
        symbol: 'auraECLP-GYD-AUSDC.e-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark Variable Debt EURe',
        symbol: 'variableDebtEURe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis Variable Debt WETH',
        symbol: 'variableDebtGnoWETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Curve EURe-3Crv',
        symbol: 'crvEUReUSD',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'RealT RMM V3 WXDAI',
        symbol: 'armmv3WXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'ButteredBread',
        symbol: 'BB',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Cent Pool Token',
        symbol: 'CPT',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'TEST Liquidity Provider',
        symbol: 'TLP',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x1337bedc9d22ecbe766df105c9623922a27963ec'),
        name: 'Curve.fi wxDAI/USDC/USDT',
        symbol: 'x3CRV',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer osGNO/GNO-BPT Gauge Deposit',
        symbol: 'osGNO/GNO-BPT-gauge',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer osGNO/GNO StablePool Aura Deposit Vault',
        symbol: 'auraosGNO/GNO-BPT-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Moo Aura Gnosis WETH-rETH',
        symbol: 'mooAuraGnosisWETH-rETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis wstETH',
        symbol: 'aGnowstETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis Variable Debt WXDAI',
        symbol: 'variableDebtGnoWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis Variable Debt USDCe',
        symbol: 'variableDebtGnoUSDCe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark Variable Debt USDC.e',
        symbol: 'variableDebtUSDC.e',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark Variable Debt USDT',
        symbol: 'variableDebtUSDT',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x3b28e683d0f7012e3194d2dbc0247d227b859b00'),
        name: '50SAFE-50OLAS',
        symbol: '50SAFE-50OLAS',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x3e442af13db47e27b48de6caf2708b2d180b10f9'),
        name: 'Moo Aura Gnosis sDAI-EURe',
        symbol: 'mooAuraGnosissDAI-EURe',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x3ffea21b2f44fafd6938e3946005c8f4a12988b7'),
        name: '50waGnoUSDCe 50s HDGDL',
        symbol: '50waGnoUSDCe-50s-HDGDL',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x408883e983695dec78cf66480e6efef907a73c21'),
        name: 'Balancer Stable stEUR EURe pool Aura Deposit Vault',
        symbol: 'aurastEUR/EURe-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Gyroscope ECLP sDAI/USDC.e Rehype Aura Deposit Vault',
        symbol: 'auraECLP-sDAI-USDC.e-rh-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Agave interest bearing WETH',
        symbol: 'agWETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer 50sDAI-50wstETH Aura Deposit Vault',
        symbol: 'auraB-50sDAI-50wstETH-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'RMM yield generating REALTOKEN-S-13245-MONICA-ST-DETROIT-MI',
        symbol: 'armmREALTOKEN-S-13245-MONICA-ST-DETROIT-MI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Gyroscope ECLP GYD/sDAI Aura Deposit Vault',
        symbol: 'auraECLP-GYD-sDAI-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer B-50sDAI-50wstETH Gauge Deposit',
        symbol: 'B-50sDAI-50wstETH-gauge',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Moo Aura Gnosis WETH-wstETH',
        symbol: 'mooAuraGnosisWETH-wstETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'DXswap',
        symbol: 'DXS',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'BCoW AMM 50wstETH-50sDAI',
        symbol: 'BCoW-50wstETH-50sDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'DXswap',
        symbol: 'DXS',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Agave interest bearing wstETH',
        symbol: 'agwstETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark WETH',
        symbol: 'spWETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Gyroscope ECLP wstETH/WETH Aura Deposit Vault',
        symbol: 'auraECLP-wstETH-WETH-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'RealT RMM V3 Variable Debt USDC',
        symbol: 'variableDebtrmmv3USDC',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'GNO Swapr GC TVL 04-14',
        symbol: 'GNOxSWAPRTVL-0414',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'StakeWise Reward GNO',
        symbol: 'rGNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark EURe',
        symbol: 'spEURe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'RMM yield generating WXDAI',
        symbol: 'armmWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave variable debt bearing WETH',
        symbol: 'variableDebtWETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Swapr GC GNO TVL 07-07',
        symbol: 'gSWAPRGNOTVL-0707',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Moo Aura Gnosis stEUR-EURe',
        symbol: 'mooAuraGnosisstEUR-EURe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis sDAI',
        symbol: 'aGnosDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'DXswap',
        symbol: 'DXS',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: '50s HDGDL 50WXDAI',
        symbol: '50s-HDGDL-50WXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer CoW AMM 50 osGNO 50 wstETH',
        symbol: 'BCoW-50osGNO-50wstETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark Variable Debt WXDAI',
        symbol: 'variableDebtWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: '40AGVE-60GNO',
        symbol: '40AGVE-60GNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'De-xDai',
        symbol: 'DXDC',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'DXswap',
        symbol: 'DXS',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'DXswap',
        symbol: 'DXS',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x95a21fcbb57ed54d3a5a706068b06cee8637998a'),
        name: 'Agave interest bearing WXDAI',
        symbol: 'agWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x96b804f432eb38bbf9a6be93e3ef29a9d3c5107d'),
        name: 'USDC-LP',
        symbol: 'S*USDC',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x9908801df7902675c3fedd6fea0294d18d5d5d34'),
        name: 'RealT RMM V3 Variable Debt WXDAI',
        symbol: 'variableDebtrmmv3WXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x99272c6e2baa601cea8212b8fbaa7920a9f916f0'),
        name: 'Aave variable debt bearing GNO',
        symbol: 'variableDebtGNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'RMM yield generating REALTOKEN-S-1389-BIRD-AVE-BIRMINGHAM-MI',
        symbol: 'armmREALTOKEN-S-1389-BIRD-AVE-BIRMINGHAM-MI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis Variable Debt wstETH',
        symbol: 'variableDebtGnowstETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark wstETH',
        symbol: 'spwstETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis GNO',
        symbol: 'aGnoGNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave variable debt bearing USDC',
        symbol: 'variableDebtUSDC',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis WETH',
        symbol: 'aGnoWETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Gyroscope ECLP rETH/WETH',
        symbol: 'ECLP-rETH-WETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: '50bCSPX-50sDAI',
        symbol: '50bCSPX-50sDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis Variable Debt EURe',
        symbol: 'variableDebtGnoEURe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'DXswap',
        symbol: 'DXS',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer EURe/sDAI Gauge Deposit',
        symbol: 'EURe/sDAI-gauge',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xbc4f20daf4e05c17e93676d2cec39769506b8219'),
        name: 'Spark Variable Debt USDC',
        symbol: 'variableDebtUSDC',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis Variable Debt GNO',
        symbol: 'variableDebtGnoGNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis USDCe',
        symbol: 'aGnoUSDCe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Moo Balancer Gnosis wagwstETH/wagWETH',
        symbol: 'mooBalancerGnosiswagwstETH/wagWETH',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis USDC',
        symbol: 'aGnoUSDC',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Spark WXDAI',
        symbol: 'spWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Staking PNK on xDai',
        symbol: 'stPNK',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis WXDAI',
        symbol: 'aGnoWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Agave interest bearing WXDAI',
        symbol: 'agWXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xd91770e868c7471a9585d1819143063a40c54d00'),
        name: 'Curve.fi crvEUReUSD Gauge Deposit',
        symbol: 'crvEUReUSD-gauge',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xdd439304a77f54b1f7854751ac1169b279591ef7'),
        name: 'Balancer EURe/sDAI',
        symbol: 'EURe/sDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xde151980d461696543aa07a19bbe2603b20ecbae'),
        name: 'Balancer EURe/sDAI Aura Deposit Vault',
        symbol: 'auraEURe/sDAI-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xdec0362b3229690fbe4f88c57472610588bb9a2e'),
        name: 'Balancer sBAL3 Gauge Deposit',
        symbol: 'sBAL3-gauge',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xdf82e3bd7b5b30b6084b5e945924358d7d5f31d1'),
        name: '80COW-20WXDAI',
        symbol: '80COW-20WXDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xe1cf0d5a56c993c3c2a0442dd645386aeff1fc9a'),
        name: 'Agave interest bearing sDAI',
        symbol: 'agsDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xe877b96caf9f180916bf2b5ce7ea8069e0123182'),
        name: 'Spark sDAI',
        symbol: 'spsDAI',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xeb20b07a9abe765252e6b45e8292b12cb553cca6'),
        name: 'Agave interest bearing EURe',
        symbol: 'agEURe',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xed56f76e9cbc6a64b821e9c016eafbd3db5436d1'),
        name: 'RealT RMM V3 USDC',
        symbol: 'armmv3USDC',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Aave Gnosis EURe',
        symbol: 'aGnoEURe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Static Aave Gnosis USDCe',
        symbol: 'stataGnoUSDCe',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Moo Balancer Gnosis wagwstETH/wagGNO',
        symbol: 'mooBalancerGnosiswagwstETH/wagGNO',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'RealT RMM V3 RTW-USD-01',
        symbol: 'armmv3RTW-USD-01',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'DXswap',
        symbol: 'DXS',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer 50bbagGNO-50bbagWETH Gauge Deposit',
        symbol: '50bbagGNO-50bbagWETH-gauge',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Balancer 50bCSPX-50sDAI Gauge Deposit',
        symbol: '50bCSPX-50sDAI-gauge',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'EURe-jEUR Aura Deposit Vault',
        symbol: 'auraEURe-jEUR-vault',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Swapr GC GNO TVL 09-01',
        symbol: 'gSWAPRGNOTVL-0901',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Swapr GC GNO TVL 08-04',
        symbol: 'gSWAPRGNOTVL-0804',
        type: 'DeFi',
    },
    {
        address: staticFromString('******************************************'),
        name: 'Uniswap V2',
        symbol: 'UNI-V2',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xfcd125f4c5030137634863a2d3e5d6d89158f19c'),
        name: 'BCoW AMM 50sDAI-50COW',
        symbol: 'BCoW-50sDAI-50COW',
        type: 'DeFi',
    },
    {
        address: staticFromString('0xfe31f6ff4366d2e17904e474605c2c6c42323380'),
        name: 'Agave variable debt bearing STAKE',
        symbol: 'variableDebtSTAKE',
        type: 'DeFi',
    },
    {
        address: staticFromString('0x5f6f7b0a87ca3cf3d0b431ae03ef3305180bff4d'),
        name: 'Agave variable debt bearing STAKE',
        symbol: 'variableDebtGnoUSDC',
        type: 'DeFi',
    },
]

export const DEFI_PROTOCOL_ADDRESSES = new Set(
    DEFI_PROTOCOL_TOKENS.map((token) => token.address)
)
