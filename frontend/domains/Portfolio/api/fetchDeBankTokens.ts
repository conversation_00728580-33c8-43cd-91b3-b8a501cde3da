import { get } from '@zeal/api/requestBackend'

import { keys } from '@zeal/toolkit/Object'
import {
    array,
    groupByType,
    UnexpectedResultFailureError,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { fetchStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { Token2 } from '@zeal/domains/Token'
import { DEBANK_NETWORK_TO_NETWORK_MAP } from '@zeal/domains/Transactions/domains/DeBank/constants'

import { parseDebankToken } from '../helpers/parseDeBankToken'

export const fetchDeBankTokens = async ({
    address,
    signal,
}: {
    address: Web3.address.Address
    signal?: AbortSignal
}): Promise<Token2[]> => {
    const currencies = await fetchStaticCurrencies()
    const response = await get(
        `/proxy/dbk/user/all_token_list`,
        {
            query: {
                id: address,
                chain_ids: keys(DEBANK_NETWORK_TO_NETWORK_MAP).filter(
                    (chainId) => chainId !== 'xdai'
                ),
            },
        },
        signal
    )
    const tokenArray = array(response).getSuccessResultOrThrow(
        'Failed to parse DeBank tokens array'
    )

    const [debankTokenErrors, tokens] = groupByType(
        tokenArray.map((token) => parseDebankToken(token))
    )
    tokens.forEach((token) => {
        const staticCurrency = currencies[token.balance.currency.id]
        if (staticCurrency) {
            token.balance.currency = staticCurrency
        }
    })

    if (debankTokenErrors.length) {
        captureError(
            new UnexpectedResultFailureError(
                'Failed to parse DeBank tokens',
                debankTokenErrors
            )
        )
    }
    return tokens
}
