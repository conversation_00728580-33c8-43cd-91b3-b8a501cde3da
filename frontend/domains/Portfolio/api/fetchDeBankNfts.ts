import { get } from '@zeal/api/requestBackend'

import { unsafe_fromNumberWithFraction } from '@zeal/toolkit/BigInt'
import { keys, mapValues, values } from '@zeal/toolkit/Object'
import {
    array,
    failure,
    groupByType,
    match,
    nullableOf,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
    UnexpectedResultFailureError,
    ValidObject,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { currencyId as getCurrencyId } from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { sum } from '@zeal/domains/Money/helpers/sum'
import { PredefinedNetwork } from '@zeal/domains/Network'
import {
    PortfolioNFT,
    PortfolioNFTCollection,
} from '@zeal/domains/NFTCollection'
import { DeBankNetwork } from '@zeal/domains/Transactions/domains/DeBank'
import { DEBANK_NETWORK_TO_NETWORK_MAP } from '@zeal/domains/Transactions/domains/DeBank/constants'

type ParsedNFT = {
    network: PredefinedNetwork
    name: string
    mintAddress: Web3.address.Address
    nft: PortfolioNFT
}

const parseDeBankNetwork = (
    input: unknown
): Result<unknown, PredefinedNetwork> =>
    string(input).andThen((network) => {
        if (network in DEBANK_NETWORK_TO_NETWORK_MAP) {
            return success(
                DEBANK_NETWORK_TO_NETWORK_MAP[network as DeBankNetwork]
            )
        }
        return failure({ type: 'invalid_network', network })
    })

const parseDeBankNFT = (input: unknown): Result<unknown, ParsedNFT> =>
    object(input).andThen((obj) =>
        shape({
            network: parseDeBankNetwork(obj.chain),
            name: string(obj.contract_name),
            mintAddress: Web3.address.parse(obj.contract_id),
            nft: parseNft(obj),
        })
    )

export const parseNft = (obj: ValidObject): Result<unknown, PortfolioNFT> => {
    return shape({
        tokenId: string(obj.inner_id),
        name: string(obj.name),
        uri: nullableOf(obj.content, string),
        standard: oneOf(obj.standard, [
            match(obj.is_erc721, true).map(() => 'Erc721' as const),
            match(obj.is_erc1155, true).map(() => 'Erc1155' as const),
            success('Erc721' as const),
        ]),
        priceInUsd: nullableOf(obj.usd_price, number).map((price) => {
            return {
                amount: unsafe_fromNumberWithFraction(
                    price || 0,
                    FIAT_CURRENCIES.USD.fraction
                ),
                currency: FIAT_CURRENCIES.USD,
            }
        }),
        priceInDefaultCurrency: success(null),
    })
}

const createPortfolioNFTCollection = (
    nft: ParsedNFT
): PortfolioNFTCollection => {
    return {
        networkHexId: nft.network.hexChainId,
        name: nft.name,
        mintAddress: nft.mintAddress,
        priceInUsd: {
            amount: 0n,
            currency: FIAT_CURRENCIES.USD,
        },
        priceInDefaultCurrency: null,
        nfts: [],
        standard: nft.nft.standard,
    }
}

const groupNFTsIntoCollections = (
    nfts: ParsedNFT[]
): PortfolioNFTCollection[] => {
    const collections = nfts.reduce(
        (acc, nft) => {
            const collectionId = getCurrencyId({
                network: nft.network.hexChainId,
                address: nft.mintAddress,
            })

            const item = acc[collectionId] || createPortfolioNFTCollection(nft)
            item.nfts.push(nft.nft)
            acc[collectionId] = item
            return acc
        },
        {} as Record<string, PortfolioNFTCollection>
    )
    const summedCollections = mapValues(collections, (_, collection) => {
        const money = collection.nfts.map((nft) => nft.priceInUsd)
        collection.priceInUsd = sum(money) || collection.priceInUsd
        return collection
    })
    return values(summedCollections)
}

export const fetchDeBankNFTCollections = async ({
    address,
    signal,
}: {
    address: Web3.address.Address
    signal?: AbortSignal
}): Promise<PortfolioNFTCollection[]> => {
    const response = await get(
        `/proxy/dbk/user/all_nft_list`,
        {
            query: {
                id: address,
                chain_ids: keys(DEBANK_NETWORK_TO_NETWORK_MAP),
                is_all: false,
            },
        },
        signal
    )

    const nftArray = array(response).getSuccessResultOrThrow(
        'Failed to parse DeBank NFTs array'
    )

    const [debankNFTErrors, parsedNFTs] = groupByType(
        nftArray.map((nft) => parseDeBankNFT(nft))
    )

    if (debankNFTErrors.length) {
        captureError(
            new UnexpectedResultFailureError(
                'Failed to parse DeBank NFTs',
                debankNFTErrors
            )
        )
    }

    return groupNFTsIntoCollections(parsedNFTs)
}
