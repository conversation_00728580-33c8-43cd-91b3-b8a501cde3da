import { get } from '@zeal/api/requestBackend'

import { arrayOf, object, Result, string } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { captureError } from '@zeal/domains/Error/helpers/captureError'

export type ChainActivityResult =
    | { type: 'no_activity' }
    | { type: 'gnosis_only' }
    | { type: 'mixed_activity' }

export const fetchChainActivity = async ({
    address,
    signal,
}: {
    address: Web3.address.Address
    signal?: AbortSignal
}): Promise<ChainActivityResult> => {
    try {
        const response = await get(
            '/proxy/dbk/user/used_chain_list',
            {
                query: {
                    id: address,
                },
            },
            signal
        )

        return classifyChainActivity(response).getSuccessResultOrThrow(
            'Failed to parse chain activity response'
        )
    } catch (error) {
        captureError(error, {
            extra: { context: 'fetchChainActivity', address },
        })
        return { type: 'mixed_activity' }
    }
}

const classifyChainActivity = (
    chains: unknown
): Result<unknown, ChainActivityResult> => {
    return arrayOf(chains, (chain) =>
        object(chain).andThen((obj) => string(obj.id))
    ).map((chainIds) => {
        switch (chainIds.length) {
            case 0:
                return { type: 'no_activity' }
            case 1:
                return chainIds[0] === 'xdai'
                    ? { type: 'gnosis_only' }
                    : { type: 'mixed_activity' }
            default:
                return { type: 'mixed_activity' }
        }
    })
}
