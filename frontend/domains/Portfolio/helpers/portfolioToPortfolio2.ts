import { tokenToToken2 } from '@zeal/domains/Token/helpers/tokenToToken2'

import { Portfolio, Portfolio2, ServerPortfolio, ServerPortfolio2 } from '..'

export const serverPortfolioToServerPortfolio2 = ({
    serverPortfolio,
}: {
    serverPortfolio: ServerPortfolio
}): ServerPortfolio2 => ({
    tokens: serverPortfolio.tokens.map((token) =>
        tokenToToken2({
            token,
            knownCurrencies: serverPortfolio.currencies,
        })
    ),
    apps: serverPortfolio.apps,
    nftCollections: serverPortfolio.nftCollections,
})

export const portfolioToPortfolio2 = ({
    portfolio,
}: {
    portfolio: Portfolio
}): Portfolio2 => ({
    ...serverPortfolioToServerPortfolio2({
        serverPortfolio: portfolio,
    }),
    earn: portfolio.earn,
    cardBalance: portfolio.cardBalance,
})
