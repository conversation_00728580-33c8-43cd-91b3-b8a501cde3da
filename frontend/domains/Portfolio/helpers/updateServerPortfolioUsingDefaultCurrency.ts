import { notReachable } from '@zeal/toolkit'

import { AppProtocol2 } from '@zeal/domains/App'
import {
    CurrencyId,
    DefaultCurrency,
    FiatCurrency,
} from '@zeal/domains/Currency'
import { PricesMap } from '@zeal/domains/Currency/api/fetchPriceChange'
import { FXRate2, RatesMap } from '@zeal/domains/FXRate'
import {
    applyNullableRate,
    applyRate2,
    mergeRates,
} from '@zeal/domains/FXRate/helpers/applyRate'
import { PortfolioNFT } from '@zeal/domains/NFTCollection'
import { ServerPortfolio } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { Token2 } from '@zeal/domains/Token'
import { tokenToToken2 } from '@zeal/domains/Token/helpers/tokenToToken2'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

export const updateServerPortfolioUsingDefaultCurrency = ({
    portfolio,
    defaultCurrencyConfig,
    rates,
    rateToUsd,
    historyPrices,
    installationId,
}: {
    portfolio: ServerPortfolio
    defaultCurrencyConfig: DefaultCurrencyConfig
    rates: RatesMap
    rateToUsd: FXRate2<FiatCurrency, DefaultCurrency> | null
    historyPrices: PricesMap
    installationId: string
}): ServerPortfolio => {
    const currencies = portfolio.currencies

    // TODO :: @Nicvaniek - we should not mutate portfolio currencies directly here
    currencies[defaultCurrencyConfig.defaultCurrency.id] =
        defaultCurrencyConfig.defaultCurrency

    const currenciesWithNullableRates: CurrencyId[] = []

    const tokens = portfolio.tokens
        .map((item) => {
            if (!item.priceInDefaultCurrency) {
                return item
            }

            const token2 = tokenToToken2({
                token: item,
                knownCurrencies: currencies,
            })

            const rate = rates[token2.balance.currency.id]
            if (!rate && !token2.scam) {
                currenciesWithNullableRates.push(token2.balance.currency.id)
            }
            const priceInDefaultCurrency = rate
                ? applyRate2({
                      baseAmount: token2.balance,
                      rate,
                  })
                : rateToUsd && item.priceInDefaultCurrency
                  ? applyRate2({
                        baseAmount: {
                            currency: rateToUsd.base,
                            amount: item.priceInDefaultCurrency.amount,
                        },
                        rate: rateToUsd,
                    }) // TODO @negriienko delete when we solve issues in coingeko rates api
                  : null
            const priceChange24h = historyPrices[token2.balance.currency.id]
            const debankRate = rateToUsd
                ? token2.rate &&
                  mergeRates({ rateA: token2.rate, rateB: rateToUsd })
                : null
            return {
                ...item,
                rate: rate
                    ? {
                          base: rate.base.id,
                          quote: rate.quote.id,
                          rate: rate.rate,
                      }
                    : debankRate
                      ? {
                            base: debankRate.base.id,
                            quote: debankRate.quote.id,
                            rate: debankRate.rate,
                        }
                      : null,
                priceInDefaultCurrency: priceInDefaultCurrency
                    ? {
                          currencyId: priceInDefaultCurrency.currency.id,
                          amount: priceInDefaultCurrency.amount,
                      }
                    : null,
                marketData: priceChange24h ? { priceChange24h } : null,
            }
        })
        .sort((a, b) => {
            const aAmount = a.priceInDefaultCurrency?.amount
            const bAmount = b.priceInDefaultCurrency?.amount

            if (aAmount === undefined || aAmount === null) return 1
            if (bAmount === undefined || bAmount === null) return -1

            return aAmount > bAmount ? -1 : aAmount < bAmount ? 1 : 0
        })

    const apps = portfolio.apps.map((item) => {
        return {
            ...item,
            protocols: updateAppProtocols(item.protocols, rateToUsd),
            priceInDefaultCurrency: rateToUsd
                ? applyRate2({
                      baseAmount: item.priceInUsd,
                      rate: rateToUsd,
                  })
                : null,
        }
    })
    const nftCollections = portfolio.nftCollections.map((item) => {
        return {
            ...item,
            nfts: updateNfts(item.nfts, rateToUsd),
            priceInDefaultCurrency: rateToUsd
                ? applyRate2({
                      baseAmount: item.priceInUsd,
                      rate: rateToUsd,
                  })
                : null,
        }
    })

    if (currenciesWithNullableRates.length > 0) {
        postUserEvent({
            type: 'PortfolioLoadedCurrenciesWithNullableRates',
            currenciesIds: currenciesWithNullableRates,
            installationId,
        })
    }

    return {
        nftCollections,
        apps,
        tokens,
        currencies,
    }
}

const updateNfts = (
    nfts: PortfolioNFT[],
    rate: FXRate2<FiatCurrency, DefaultCurrency> | null
): PortfolioNFT[] => {
    return nfts.map((nft) => {
        return {
            ...nft,
            priceInDefaultCurrency: applyNullableRate({
                baseAmount: nft.priceInUsd,
                rate,
            }),
        }
    })
}

const updateAppProtocols = (
    protocols: AppProtocol2[],
    rate: FXRate2<FiatCurrency, DefaultCurrency> | null
): AppProtocol2[] =>
    protocols.map((protocol) => {
        switch (protocol.type) {
            case 'CommonAppProtocol':
            case 'LendingAppProtocol': {
                return {
                    ...protocol,
                    suppliedTokens: updateTokens(protocol.suppliedTokens, rate),
                    rewardTokens: updateTokens(protocol.rewardTokens, rate),
                    borrowedTokens: updateTokens(protocol.borrowedTokens, rate),
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInDefaultCurrency,
                        rate,
                    }),
                }
            }
            case 'LockedTokenAppProtocol':
                return {
                    ...protocol,
                    rewardTokens: updateTokens(protocol.rewardTokens, rate),
                    lockedTokens: updateTokens(protocol.lockedTokens, rate),
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInDefaultCurrency,
                        rate,
                    }),
                }

            case 'UnknownAppProtocol':
                return {
                    ...protocol,
                    tokens: updateTokens(protocol.tokens, rate),
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInDefaultCurrency,
                        rate,
                    }),
                }

            case 'VestingAppProtocol':
                return {
                    ...protocol,
                    priceInDefaultCurrency: applyNullableRate({
                        baseAmount: protocol.priceInDefaultCurrency,
                        rate,
                    }),
                    vestedToken: {
                        ...protocol.vestedToken,
                        priceInDefaultCurrency: applyNullableRate({
                            baseAmount:
                                protocol.vestedToken.priceInDefaultCurrency,
                            rate,
                        }),
                    },
                    claimableToken: {
                        ...protocol.claimableToken,
                        priceInDefaultCurrency: applyNullableRate({
                            baseAmount:
                                protocol.claimableToken.priceInDefaultCurrency,
                            rate,
                        }),
                    },
                }

            default:
                return notReachable(protocol)
        }
    })

const updateTokens = (
    tokens: Token2[],
    rate: FXRate2<FiatCurrency, DefaultCurrency> | null
): Token2[] =>
    tokens.map((item) => {
        return {
            ...item,
            priceInDefaultCurrency: applyNullableRate({
                baseAmount: item.priceInDefaultCurrency,
                rate,
            }),
        }
    })
