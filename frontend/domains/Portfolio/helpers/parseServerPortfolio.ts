import {
    array,
    arrayOf,
    combine,
    object,
    oneOf,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'

import { App2 } from '@zeal/domains/App'
import { appToApp2 } from '@zeal/domains/App/helpers/appToApp2'
import { parse as parseApp } from '@zeal/domains/App/helpers/parse'
import { parseApp2FromStorage } from '@zeal/domains/App/helpers/parseApp2'
import { KnownCurrencies } from '@zeal/domains/Currency'
import { parseKnownCurrencies } from '@zeal/domains/Currency/helpers/parse'
import { parse as parseNFTCollection } from '@zeal/domains/NFTCollection/parsers/parse'
import { ServerPortfolio } from '@zeal/domains/Portfolio'
import { parse as parseToken } from '@zeal/domains/Token/helpers/parse'

export const parseServerPortfolio = (
    input: unknown
): Result<unknown, ServerPortfolio> =>
    object(input)
        .andThen((obj) =>
            parseKnownCurrencies(obj.currencies).andThen((knownCurrencies) =>
                shape({
                    currencies: success(knownCurrencies),
                    tokens: array(obj.tokens).andThen((arr) =>
                        combine(arr.map(parseToken))
                    ),
                    apps: oneOf(obj.apps, [
                        arrayOf(obj.apps, parseApp2FromStorage),
                        arrayOf(obj.apps, parseApp1(knownCurrencies)),
                    ]),
                    nftCollections: array(obj.nftCollections).andThen((arr) =>
                        combine(arr.map(parseNFTCollection))
                    ),
                })
            )
        )
        .map(({ currencies, tokens, apps, nftCollections }) => ({
            currencies,
            tokens: tokens.filter(
                (token) => currencies[token.balance.currencyId]
            ),
            apps,
            nftCollections,
        }))

const parseApp1 =
    (knownCurrencies: KnownCurrencies) =>
    (input: unknown): Result<unknown, App2> => {
        return parseApp(input, knownCurrencies).map(appToApp2)
    }
