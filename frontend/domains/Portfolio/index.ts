import { Address } from '@zeal/domains/Address'
import { App2 } from '@zeal/domains/App'
import { CardBalance } from '@zeal/domains/Card'
import { KnownCurrencies } from '@zeal/domains/Currency'
import { Earn } from '@zeal/domains/Earn'
import { PortfolioNFTCollection } from '@zeal/domains/NFTCollection'
import { Token, Token2 } from '@zeal/domains/Token'

/**
 * @deprecated use ServerPortfolio2 instead
 */
export type ServerPortfolio = {
    currencies: KnownCurrencies
    tokens: Token[]
    apps: App2[]
    nftCollections: PortfolioNFTCollection[]
}

export type ServerPortfolio2 = {
    tokens: Token2[]
    apps: App2[]
    nftCollections: PortfolioNFTCollection[]
}

/**
 * @deprecated use Portfolio2 instead
 */
export type Portfolio = {
    earn: Earn
    cardBalance: CardBalance | null
} & ServerPortfolio

export type Portfolio2 = {
    earn: Earn
    cardBalance: CardBalance | null
} & ServerPortfolio2

declare const PortfolioMapIndexSymbol: unique symbol

export type PortfolioMap = Record<
    Address & {
        __portfolioMapIndex: typeof PortfolioMapIndexSymbol
    },
    Portfolio
>
