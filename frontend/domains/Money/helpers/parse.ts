import {
    bigint,
    object,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import { FiatCurrencyCode } from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import {
    parseCryptoCurrency,
    parseFiatCurrency,
} from '@zeal/domains/Currency/helpers/parse'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'

export type MoneyDTO = {
    currencyId: string
    amount: bigint
}

export const parseDTO = (input: unknown): Result<unknown, MoneyDTO> =>
    object(input).andThen((obj) =>
        shape({
            amount: bigint(obj.amount),
            currencyId: string(obj.currencyId),
        })
    )

/**
 * @deprecated use parseFiatMoneyFromDTO
 */
export const parseFiatMoney = (
    input: unknown,
    knownCurrencies: unknown
): Result<unknown, FiatMoney> =>
    parseDTO(input).andThen((moneyDto) =>
        shape({
            amount: success(moneyDto.amount),
            currency: object(knownCurrencies)
                .andThen((knownCurrenciesObj) =>
                    object(knownCurrenciesObj[moneyDto.currencyId])
                )
                .andThen(parseFiatCurrency),
        })
    )

export const parseCryptoMoneyFromDTO = ({
    dto,
    knownCurrencies,
}: {
    dto: MoneyDTO
    knownCurrencies: unknown
}) =>
    shape({
        amount: success(dto.amount),
        currency: object(knownCurrencies)
            .andThen((knownCurrenciesObj) =>
                object(knownCurrenciesObj[dto.currencyId])
            )
            .andThen(parseCryptoCurrency),
    })

export const parseFiatMoneyFromDTO = ({ dto }: { dto: MoneyDTO }) =>
    shape({
        amount: success(dto.amount),
        currency: parseFiatCurrency(
            FIAT_CURRENCIES[dto.currencyId as FiatCurrencyCode]
        ),
    })

export const parseCryptoMoney = (
    input: unknown,
    knownCurrencies: unknown
): Result<unknown, CryptoMoney> =>
    parseDTO(input).andThen((dto) =>
        parseCryptoMoneyFromDTO({ dto, knownCurrencies })
    )

// TODO @resetko-zeal naming is not the best, because this is used to parse also from network, if it's full object
export const parseFiatMoneyFromStorage = (
    input: unknown
): Result<unknown, FiatMoney> =>
    object(input).andThen((money) =>
        shape({
            amount: bigint(money.amount),
            currency: object(money.currency).andThen(parseFiatCurrency),
        })
    )

// TODO @resetko-zeal naming is not the best, because this is used to parse also from network, if it's full object
export const parseCryptoMoneyFromStorage = (
    input: unknown
): Result<unknown, CryptoMoney> =>
    object(input).andThen((money) =>
        shape({
            amount: bigint(money.amount),
            currency: object(money.currency).andThen(parseCryptoCurrency),
        })
    )
