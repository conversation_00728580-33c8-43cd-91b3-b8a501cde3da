import { notReachable } from '@zeal/toolkit'
import {
    abs,
    getDecimalsWithFraction,
    unsafe_toNumberWithFraction,
} from '@zeal/toolkit/BigInt'
import { staticFromString } from '@zeal/toolkit/Web3/address'

import { currencyId } from '@zeal/domains/Currency'
import {
    CryptoMoney,
    FiatMoney,
    Money2,
    MoneyByCurrency,
} from '@zeal/domains/Money'
import {
    ARBITRUM,
    AVALANCHE,
    BASE,
    BLAST,
    BSC,
    ETHEREUM,
    GNOSIS,
    LINEA,
    OPTIMISM,
    POLYGON,
} from '@zeal/domains/Network/constants'

import { format } from './format'

export const formattedFiatMoneyPrecise = ({
    money,
    withSymbol,
    sign,
}: {
    money: FiatMoney
    withSymbol: boolean
    sign: '+' | '-' | null
}): string => {
    const currency = money.currency

    const amount = unsafe_toNumberWithFraction(
        abs(money.amount),
        currency.fraction
    )

    const decimals = getDecimalsWithFraction(money.amount, currency.fraction)

    if (amount !== 0 && amount < 0.01) {
        return `${sign ? sign : ''}<${withSymbol ? currency.symbol : ''}0.01`
    }
    const formattedAmount = (() => {
        switch (true) {
            case amount === 0:
            case amount >= 1 && decimals === 0:
                return format({ money, variant: 'no_decimals' })
            default:
                return format({
                    money,
                    variant: 'just_2_decimals',
                })
        }
    })()

    return `${sign ? sign : ''}${
        withSymbol ? currency.symbol : ''
    }${formattedAmount}`
}

const PRECISE_TOKENS: Record<string, true> = {
    // BTC
    [currencyId({
        network: GNOSIS.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: POLYGON.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: OPTIMISM.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: ETHEREUM.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: LINEA.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: ARBITRUM.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: AVALANCHE.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: BSC.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: BASE.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: BLAST.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    // WETH
    [currencyId({
        network: GNOSIS.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: POLYGON.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: OPTIMISM.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: LINEA.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: ARBITRUM.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: AVALANCHE.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: BSC.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: BASE.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    [currencyId({
        network: BLAST.hexChainId,
        address: staticFromString('******************************************'),
    })]: true,
    // Native ETH token
    [currencyId({
        network: ETHEREUM.hexChainId,
        address: ETHEREUM.nativeCurrency.address,
    })]: true,
    [currencyId({
        network: BASE.hexChainId,
        address: BASE.nativeCurrency.address,
    })]: true,
    [currencyId({
        network: ARBITRUM.hexChainId,
        address: ARBITRUM.nativeCurrency.address,
    })]: true,
    [currencyId({
        network: OPTIMISM.hexChainId,
        address: OPTIMISM.nativeCurrency.address,
    })]: true,
    [currencyId({
        network: BLAST.hexChainId,
        address: BLAST.nativeCurrency.address,
    })]: true,
    [currencyId({
        network: LINEA.hexChainId,
        address: LINEA.nativeCurrency.address,
    })]: true,
}

export const formattedCryptoMoneyPrecise = ({
    money,
    withSymbol,
    sign,
}: {
    money: CryptoMoney
    withSymbol: boolean
    sign: '+' | '-' | null
}): string => {
    const formattedAmount = PRECISE_TOKENS[money.currency.id]
        ? formatLargeCryptoCurrency({ money })
        : formatRegularCryptoCurrency({ money })

    return `${sign ? sign : ''}${formattedAmount}${
        withSymbol ? ` ${truncateSymbol(money.currency.symbol)}` : ''
    }`
}

const truncateSymbol = (symbol: string) =>
    symbol.length > 9 ? symbol.slice(0, 6) + '...' : symbol

const formatLargeCryptoCurrency = ({ money }: { money: CryptoMoney }) => {
    const amount = unsafe_toNumberWithFraction(
        abs(money.amount),
        money.currency.fraction
    )

    switch (true) {
        case amount < 0.000001:
            return format({
                money,
                variant: 'min_2_decimals_max_2_significant',
            })

        // TODO @resetko-zeal should we really keep it separate case, will it change any time soon?
        case amount < 1:
            return format({
                money,
                variant: 'min_2_decimals_max_4_significant',
            })

        default:
            return format({
                money,
                variant: 'min_2_decimals_max_4_significant',
            })
    }
}

const formatRegularCryptoCurrency = ({ money }: { money: CryptoMoney }) => {
    const amount = unsafe_toNumberWithFraction(
        abs(money.amount),
        money.currency.fraction
    )

    const decimals = getDecimalsWithFraction(
        money.amount,
        money.currency.fraction
    )
    switch (true) {
        case amount === 0:
        case amount >= 1 && (decimals === 0 || decimals < 0.00000000001):
            return format({ money, variant: 'no_decimals' })

        // TODO @resetko-zeal should we really keep it separate case, will it change any time soon?
        case amount < 0.01:
            return format({
                money,
                variant: 'min_2_decimals_max_2_significant',
            })

        default:
            return format({
                money,
                variant: 'min_2_decimals_max_2_significant',
            })
    }
}

export const formattedMoneyPrecise = ({
    money,
    withSymbol,
    sign,
}: {
    money: Money2
    withSymbol: boolean
    sign: '+' | '-' | null
}) => {
    switch (money.currency.type) {
        case 'FiatCurrency':
            return formattedFiatMoneyPrecise({
                money: money as MoneyByCurrency<typeof money.currency>,
                withSymbol,
                sign,
            })
        case 'CryptoCurrency':
            return formattedCryptoMoneyPrecise({
                money: money as MoneyByCurrency<typeof money.currency>,
                withSymbol,
                sign,
            })
        /* istanbul ignore next */
        default:
            return notReachable(money.currency)
    }
}
