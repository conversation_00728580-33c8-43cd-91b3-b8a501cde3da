import { FormattedMessage } from 'react-intl'
import { SectionListData } from 'react-native'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Rocket } from '@zeal/uikit/Icon/Rocket'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { SectionList } from '@zeal/uikit/SectionList'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'

import {
    CryptoCurrency,
    GasCurrencyPresetMap,
    ShortKnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import { fetchShortStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { PAYMASTER_MAP } from '@zeal/domains/Currency/constants'
import { getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies } from '@zeal/domains/Currency/helpers/getShortKnownCryptoCurrenciesfromKnownCryptoCurrencies'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { FormattedFeeInDefaultCurrency2 } from '@zeal/domains/Money/components/FormattedFeeInDefaultCurrency'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { Network, NetworkMap, PredefinedNetwork } from '@zeal/domains/Network'
import { Avatar as NetworkAvatar } from '@zeal/domains/Network/components/Avatar'
import { ServerPortfolio } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import {
    ERC20GasAbstractionTransactionFee,
    NativeGasAbstractionTransactionFee,
} from '@zeal/domains/UserOperation'

type Props = {
    networkMap: NetworkMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currentFee:
        | ERC20GasAbstractionTransactionFee
        | NativeGasAbstractionTransactionFee
    portfolio: ServerPortfolio
    nativeFee: CryptoMoney
    nativeFeeInDefaultCurrency: FiatMoney | null
    erc20FeeInDefaultCurrency: FiatMoney | null
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_4337_auto_gas_token_selection_clicked'; network: Network }
    | {
          type: 'on_4337_gas_currency_selected'
          selectedGasCurrency: CryptoCurrency
      }

export const GasTokenSelector = ({
    currentFee,
    portfolio,
    gasCurrencyPresetMap,
    networkMap,
    nativeFee,
    nativeFeeInDefaultCurrency,
    erc20FeeInDefaultCurrency,
    onMsg,
}: Props) => {
    const [loadable] = useLoadableData(fetchShortStaticCurrencies, {
        type: 'loading',
        params: {},
    })

    const network = networkMap[
        currentFee.feeInTokenCurrency.currency.networkHexChainId
    ] as PredefinedNetwork

    const autoGasTokenSelectionEnabled =
        !gasCurrencyPresetMap[network.hexChainId]

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12}>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="GasCurrencySelector.networkFee"
                                        defaultMessage="Network fee"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                    right={
                        <NetworkAvatar
                            currentNetwork={{
                                type: 'specific_network',
                                network,
                            }}
                            size={24}
                        />
                    }
                />

                <Column spacing={0}>
                    <Group variant="default">
                        <ListItem
                            variant="default"
                            size="regular"
                            onClick={() => {
                                onMsg({
                                    type: 'on_4337_auto_gas_token_selection_clicked',
                                    network,
                                })
                            }}
                            aria-current={autoGasTokenSelectionEnabled}
                            avatar={({ size }) => (
                                <Rocket size={size} color="iconAccent2" />
                            )}
                            primaryText={
                                <FormattedMessage
                                    id="GasCurrencySelector.removeDefaultGasToken.title"
                                    defaultMessage="Automatic fee handling"
                                />
                            }
                            shortText={
                                <FormattedMessage
                                    id="GasCurrencySelector.removeDefaultGasToken.description"
                                    defaultMessage="Pay fees from the largest balance"
                                />
                            }
                        />
                    </Group>
                </Column>
                {(() => {
                    switch (loadable.type) {
                        case 'error':
                        case 'loading':
                            return null
                        case 'loaded':
                            return (
                                <Content
                                    nativeFee={nativeFee}
                                    nativeFeeInDefaultCurrency={
                                        nativeFeeInDefaultCurrency
                                    }
                                    erc20FeeInDefaultCurrency={
                                        erc20FeeInDefaultCurrency
                                    }
                                    network={network}
                                    currentFee={currentFee}
                                    shortCurrencies={loadable.data}
                                    portfolio={portfolio}
                                    onMsg={onMsg}
                                    autoGasTokenSelectionEnabled={
                                        autoGasTokenSelectionEnabled
                                    }
                                />
                            )

                        default:
                            return notReachable(loadable)
                    }
                })()}
            </Column>
        </Screen>
    )
}

const Content = ({
    shortCurrencies,
    currentFee,
    portfolio,
    autoGasTokenSelectionEnabled,
    network,
    nativeFee,
    nativeFeeInDefaultCurrency,
    erc20FeeInDefaultCurrency,
    onMsg,
}: {
    network: Network
    shortCurrencies: ShortKnownCryptoCurrencies
    currentFee:
        | ERC20GasAbstractionTransactionFee
        | NativeGasAbstractionTransactionFee
    portfolio: ServerPortfolio
    autoGasTokenSelectionEnabled: boolean
    nativeFee: CryptoMoney
    nativeFeeInDefaultCurrency: FiatMoney | null
    erc20FeeInDefaultCurrency: FiatMoney | null
    onMsg: (msg: Msg) => void
}) => {
    const currencies =
        getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies(shortCurrencies)
    const supportedCurrencies: CryptoCurrency[] = PAYMASTER_MAP[
        network.hexChainId
    ].map((id) => currencies[id])

    const portfolioFeeCurrencies = portfolio
        ? supportedCurrencies.filter((currency) =>
              portfolio.tokens
                  .map((t) => t.balance.currencyId)
                  .includes(currency.id)
          )
        : []

    const portfolioFees = portfolioFeeCurrencies.map((currency) => {
        return {
            currency,
            feeInTokenCurrency:
                nativeFee.currency.address === currency.address
                    ? nativeFee
                    : currentFee.feeInTokenCurrency.currency.id === currency.id
                      ? currentFee.feeInTokenCurrency
                      : null,
            feeInDefaultCurrency:
                nativeFee.currency.address === currency.address
                    ? nativeFeeInDefaultCurrency
                    : erc20FeeInDefaultCurrency,
        }
    })

    const nonPortfolioFees = supportedCurrencies
        .filter((currency) => !portfolioFeeCurrencies.includes(currency))
        .map((currency) => {
            return {
                currency,
                feeInDefaultCurrency: erc20FeeInDefaultCurrency,
                feeInTokenCurrency: null,
            }
        })

    const sections: SectionListData<{
        currency: CryptoCurrency
        feeInDefaultCurrency: FiatMoney | null
        feeInTokenCurrency: CryptoMoney | null
    }>[] = [
        {
            data: portfolioFees,
        },
        {
            data: nonPortfolioFees,
        },
    ]

    return (
        <Column spacing={12} fill shrink>
            <Column spacing={4} shrink>
                <Row spacing={0}>
                    <Text
                        variant="paragraph"
                        color="textSecondary"
                        weight="regular"
                    >
                        <FormattedMessage
                            id="GasCurrencySelector.payNetworkFeesUsing"
                            defaultMessage="Pay network fees using"
                        />
                    </Text>
                </Row>

                <SectionList
                    variant="grouped"
                    itemSpacing={8}
                    sectionSpacing={8}
                    sections={sections}
                    renderItem={({ item }) => (
                        <FeeOptionListItem
                            portfolio={portfolio}
                            key={item.currency.id}
                            item={item}
                            aria-current={
                                autoGasTokenSelectionEnabled
                                    ? false
                                    : currentFee.feeInTokenCurrency.currency
                                          .id === item.currency.id
                            }
                            onClick={() =>
                                onMsg({
                                    type: 'on_4337_gas_currency_selected',
                                    selectedGasCurrency: item.currency,
                                })
                            }
                        />
                    )}
                />
            </Column>
        </Column>
    )
}

const FeeOptionListItem = ({
    item,
    portfolio,

    'aria-current': ariaCurrent,
    onClick,
}: {
    'aria-current': boolean
    item: {
        currency: CryptoCurrency
        feeInDefaultCurrency: FiatMoney | null
        feeInTokenCurrency: CryptoMoney | null
    }
    portfolio: ServerPortfolio | null
    onClick: () => void
}) => {
    const balance = portfolio
        ? getBalanceByCryptoCurrency({
              currency: item.currency,
              portfolio,
          })
        : {
              amount: 0n,
              currency: item.currency,
          }

    return (
        <ListItem
            size="large"
            aria-current={ariaCurrent}
            primaryText={item.currency.code}
            onClick={onClick}
            avatar={({ size }) => (
                <CurrencyAvatar
                    rightBadge={() => null}
                    size={size}
                    currency={item.currency}
                />
            )}
            shortText={
                <Text>
                    <FormattedMessage
                        id="GasCurrencySelector.balance"
                        defaultMessage="Balance: {balance}"
                        values={{
                            balance: (
                                <FormattedMoneyPrecise
                                    withSymbol={false}
                                    sign={null}
                                    money={balance}
                                />
                            ),
                        }}
                    />
                </Text>
            }
            side={{
                title: item.feeInDefaultCurrency ? (
                    <FormattedFeeInDefaultCurrency2
                        money={item.feeInDefaultCurrency}
                    />
                ) : (
                    ' ' // non-breaking-space thing to keep right side at right place
                ),
                subtitle: item.feeInTokenCurrency ? (
                    <FormattedMoneyPrecise
                        withSymbol={false}
                        sign={null}
                        money={item.feeInTokenCurrency}
                    />
                ) : (
                    ' ' // non-breaking-space thing to keep right side at right place
                ),
            }}
        />
    )
}
