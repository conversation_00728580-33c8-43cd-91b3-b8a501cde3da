// TODO @manovikov-zeal find a proper way to mock earn
import * as Web3 from '@zeal/toolkit/Web3'
import { staticFromString } from '@zeal/toolkit/Web3/address'

import { CryptoCurrency, DefaultCurrency } from '@zeal/domains/Currency'

import { NotConfiguredEarn } from '../..'

export const nonConfiguredEarn: NotConfiguredEarn = {
    type: 'not_configured',
    holder: '******************************************' as Web3.address.Address,
    takers: [
        {
            type: 'usd',
            cryptoCurrency: {
                type: 'CryptoCurrency',
                id: 'Gnosis|******************************************',
                symbol: 'sDAI',
                code: 'sDAI',
                fraction: 18,
                rateFraction: 18,
                icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                name: 'Savings xDAI',
                address: staticFromString(
                    '******************************************'
                ),
                marketCapRank: null,
                networkHexChainId: '0x64',
            } as CryptoCurrency,
            state: 'not_deployed',
            address:
                '******************************************' as Web3.address.Address,
        },
        {
            type: 'eur',
            cryptoCurrency: {
                type: 'CryptoCurrency',
                id: 'Gnosis|******************************************',
                symbol: 'aGnoEURe',
                code: 'aGnoEURe',
                fraction: 18,
                rateFraction: 18,
                icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                name: 'Aave Gnosis EURe',
                address: staticFromString(
                    '******************************************'
                ),
                marketCapRank: null,
                networkHexChainId: '0x64',
            } as CryptoCurrency,
            state: 'not_deployed',
            address:
                '******************************************' as Web3.address.Address,
        },
        {
            type: 'eth',
            cryptoCurrency: {
                type: 'CryptoCurrency',
                id: 'Gnosis|******************************************',
                symbol: 'wstETH',
                code: 'wstETH',
                fraction: 18,
                rateFraction: 18,
                icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
                address: staticFromString(
                    '******************************************'
                ),
                marketCapRank: null,
                networkHexChainId: '0x64',
            } as CryptoCurrency,
            state: 'not_deployed',
            address:
                '******************************************' as Web3.address.Address,
        },
    ],
    takerPortfolioMap: {
        usd: {
            apy: 7.783826530595155,
            assetBalance: {
                amount: 0n,
                currency: {
                    type: 'CryptoCurrency',
                    id: 'Gnosis|******************************************',
                    symbol: 'sDAI',
                    code: 'sDAI',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                    name: 'Savings xDAI',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: null,
                    networkHexChainId: '0x64',
                } as CryptoCurrency,
            },
            userCurrencyRate: {
                base: {
                    type: 'CryptoCurrency',
                    id: 'Gnosis|******************************************',
                    symbol: 'sDAI',
                    code: 'sDAI',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                    name: 'Savings xDAI',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: null,
                    networkHexChainId: '0x64',
                } as CryptoCurrency,
                quote: {
                    type: 'FiatCurrency',
                    id: 'USD',
                    symbol: '$',
                    code: 'USD',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'TODO',
                    name: 'USD',
                },
                rate: 1166361347503312119n,
            },
            dataTimestampMs: 1742205050000,
            userCurrencyToDefaultCurrencyRate: {
                base: {
                    type: 'FiatCurrency',
                    id: 'USD',
                    symbol: '$',
                    code: 'USD',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'TODO',
                    name: 'USD',
                },
                quote: {
                    type: 'FiatCurrency',
                    id: 'USD',
                    symbol: '$',
                    code: 'USD',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'TODO',
                    name: 'USD',
                } as DefaultCurrency,
                rate: 3834235000000000000n,
            },
        },
        eur: {
            apy: 1.7554528169404968,
            assetBalance: {
                amount: 0n,
                currency: {
                    type: 'CryptoCurrency',
                    id: 'Gnosis|******************************************',
                    symbol: 'aGnoEURe',
                    code: 'aGnoEURe',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                    name: 'Aave Gnosis EURe',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: null,
                    networkHexChainId: '0x64',
                } as CryptoCurrency,
            },
            userCurrencyRate: {
                base: {
                    type: 'CryptoCurrency',
                    id: 'Gnosis|******************************************',
                    symbol: 'aGnoEURe',
                    code: 'aGnoEURe',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                    name: 'Aave Gnosis EURe',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: null,
                    networkHexChainId: '0x64',
                } as CryptoCurrency,
                quote: {
                    type: 'FiatCurrency',
                    id: 'EUR',
                    symbol: '€',
                    code: 'EUR',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'TODO',
                    name: 'Euro',
                },
                rate: 1000000000000000000n,
            },
            dataTimestampMs: 1742205131603,
            userCurrencyToDefaultCurrencyRate: {
                base: {
                    type: 'FiatCurrency',
                    id: 'EUR',
                    symbol: '€',
                    code: 'EUR',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'TODO',
                    name: 'Euro',
                },
                quote: {
                    type: 'FiatCurrency',
                    id: 'USD',
                    symbol: '$',
                    code: 'USD',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'TODO',
                    name: 'USD',
                } as DefaultCurrency,
                rate: 4177974000000000000n,
            },
        },
        eth: {
            apy: 2.8,
            assetBalance: {
                amount: 0n,
                currency: {
                    type: 'CryptoCurrency',
                    id: 'Gnosis|******************************************',
                    symbol: 'wstETH',
                    code: 'wstETH',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                    name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: null,
                    networkHexChainId: '0x64',
                } as CryptoCurrency,
            },
            userCurrencyRate: {
                base: {
                    type: 'CryptoCurrency',
                    id: 'Gnosis|******************************************',
                    symbol: 'wstETH',
                    code: 'wstETH',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
                    name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: null,
                    networkHexChainId: '0x64',
                } as CryptoCurrency,
                quote: {
                    type: 'CryptoCurrency',
                    id: 'Ethereum|******************************************',
                    symbol: 'ETH',
                    code: 'ETH',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
                    name: 'ETH',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: 2,
                    networkHexChainId: '0x1',
                } as CryptoCurrency,
                rate: 1196958368134494071n,
            },
            dataTimestampMs: 1742205131608,
            userCurrencyToDefaultCurrencyRate: {
                base: {
                    type: 'CryptoCurrency',
                    id: 'Ethereum|******************************************',
                    symbol: 'ETH',
                    code: 'ETH',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
                    name: 'ETH',
                    address: staticFromString(
                        '******************************************'
                    ),
                    marketCapRank: 2,
                    networkHexChainId: '0x1',
                } as CryptoCurrency,
                rate: 7304730000000000000000n,
                quote: {
                    type: 'FiatCurrency',
                    id: 'USD',
                    symbol: '$',
                    code: 'USD',
                    fraction: 18,
                    rateFraction: 18,
                    icon: 'TODO',
                    name: 'USD',
                } as DefaultCurrency,
            },
        },
    },
    takerApyMap: { usd: 7.783826530595155, eur: 1.7554528169404968, eth: 2.8 },
}
