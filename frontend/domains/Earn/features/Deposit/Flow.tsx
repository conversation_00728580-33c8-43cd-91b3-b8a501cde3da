import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { AddFunds } from '@zeal/domains/Account/features/AddFunds'
import { CardConfig } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { calculateInitialFromCurrency } from '@zeal/domains/Currency/helpers/calculateInitialFromCurrency'
import { Earn, Taker } from '@zeal/domains/Earn'
import { ChooseWallet } from '@zeal/domains/Earn/components/ChooseWallet'
import { DepositForm } from '@zeal/domains/Earn/features/DepositForm'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio } from '@zeal/domains/Portfolio'
import { hasTokens } from '@zeal/domains/Portfolio/helpers/hasTokens'
import { serverPortfolioToServerPortfolio2 } from '@zeal/domains/Portfolio/helpers/portfolioToPortfolio2'
import { unsafeGetPortfolioCache } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { EarnEventLocation } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    fromAccount: Account
    serverPortfolio: ServerPortfolio
    owner: Account
    taker: Taker

    earn: Earn
    cardConfig: CardConfig
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    customCurrencies: CustomCurrencyMap
    isEthereumNetworkFeeWarningSeen: boolean
    fromCurrencies: CryptoCurrency[]
    swapsIOContractsMap: SwapsIOContractsMap
    eventLocation: EarnEventLocation
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof ChooseWallet>,
          {
              type: 'close' | 'on_external_earn_deposit_completed_close_click'
          }
      >
    | MsgOf<typeof AddFunds>
    | Extract<
          MsgOf<typeof DepositForm>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_configured'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_account_selected'
          }
      >

type State =
    | {
          type: 'add_funds_flow'
      }
    | {
          type: 'deposit_form'
          initialAmount: CryptoMoney
      }

const calculateState = ({
    portfolioMap,
    fromCurrencies,
    currencyHiddenMap,
    owner,
    networkMap,
}: {
    owner: Account
    portfolioMap: PortfolioMap
    fromCurrencies: CryptoCurrency[]
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
}): State => {
    const cachedPortfolio = unsafeGetPortfolioCache({
        address: owner.address,
        portfolioMap,
    })

    const funded =
        cachedPortfolio &&
        hasTokens({
            currencyHiddenMap,
            portfolio: serverPortfolioToServerPortfolio2({
                serverPortfolio: cachedPortfolio,
            }),
        })

    if (!funded) {
        return { type: 'add_funds_flow' }
    }

    return {
        type: 'deposit_form',
        initialAmount: {
            currency: calculateInitialFromCurrency({
                portfolio: cachedPortfolio,
                currencies: fromCurrencies,
                toCurrency: null,
                networkMap,
            }),
            amount: 0n,
        },
    }
}

export const Flow = ({
    fromAccount,
    serverPortfolio,
    onMsg,
    gasCurrencyPresetMap,
    feePresetMap,
    networkMap,
    earn,
    installationId,
    portfolioMap,
    sessionPassword,
    isEthereumNetworkFeeWarningSeen,
    networkRPCMap,
    taker,
    owner,
    cardConfig,
    accountsMap,
    keyStoreMap,
    customCurrencies,
    currencyHiddenMap,
    currencyPinMap,
    fromCurrencies,
    defaultCurrencyConfig,
    swapsIOContractsMap,
    eventLocation,
}: Props) => {
    // this is a bit wired but we will do check for funded state only for owner (usually a default wallet)
    // on happy path you have one wallet and it will be owner and we will check if it is funded
    // but there is an edge case when get to the flow and change a wallet and selected non funded wallet by mistake - you will get addfunds view and will stuck with it
    // probably we don't want this so check if funded only for owner
    // as a down side if owner is not funded you will never get to the flow in the first place
    const state = calculateState({
        portfolioMap,
        fromCurrencies,
        currencyHiddenMap,
        owner,
        networkMap,
    })

    switch (state.type) {
        case 'add_funds_flow':
            return (
                <AddFunds
                    accountsMap={accountsMap}
                    address={owner.address as Web3.address.Address}
                    installationId={installationId}
                    networkMap={networkMap}
                    keyStoreMap={keyStoreMap}
                    portfolioMap={portfolioMap}
                    networkRPCMap={networkRPCMap}
                    isEthereumNetworkFeeWarningSeen={
                        isEthereumNetworkFeeWarningSeen
                    }
                    feePresetMap={feePresetMap}
                    customCurrencies={customCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )

        case 'deposit_form':
            return (
                <DepositForm
                    serverPortfolio={serverPortfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    earn={earn}
                    portfolioMap={portfolioMap}
                    earnOwner={owner}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keystoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    initialTaker={taker}
                    fromAccount={fromAccount}
                    fromCurrencies={fromCurrencies}
                    initialAmount={state.initialAmount}
                    eventLocation={eventLocation}
                    swapsIOContractsMap={swapsIOContractsMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_4337_gas_currency_selected':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_earn_configured':
                            case 'on_swaps_io_swap_request_created':
                            case 'on_account_selected':
                                onMsg(msg)
                                break
                            case 'on_earn_deposit_success':
                                postUserEvent({
                                    type: 'EarnDepositCompletedEvent',
                                    source: 'zeal',
                                    asset: taker.type,
                                    installationId,
                                })
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
