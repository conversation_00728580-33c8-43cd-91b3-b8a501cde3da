import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import {
    EarnCelebrationConfigForOneAccount,
    TakerPortfolioMap2,
    TakerToCelebrate,
} from '@zeal/domains/Earn'
import { AppRating } from '@zeal/domains/Feedback'
import { formattedMoneyPrecise } from '@zeal/domains/Money/helpers/formattedMoneyPrecise'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    takerPortfolioMap: TakerPortfolioMap2
    takerToCelebrate: TakerToCelebrate
    owner: Account
    earnCelebrationConfig: EarnCelebrationConfigForOneAccount
    installationId: string
    appRating: AppRating
    location: 'portfolio' | 'earn_details'
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_earn_celebration_triggered'
          earnCelebrationConfig: EarnCelebrationConfigForOneAccount
          owner: Account
      }
    | MsgOf<typeof Modal>

export const EarnCelebration = ({
    appRating,
    installationId,
    takerPortfolioMap,
    earnCelebrationConfig,
    takerToCelebrate,
    location,
    owner,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        postUserEvent({
            type: 'EarnCelebrationEvent',
            installationId,
            location,
            currencyCode:
                takerToCelebrate.totalTakerEarningsInUserCurrency.currency.code,
            takerType: takerToCelebrate.taker.type,
            totalTakerEarningsInUserCurrency: formattedMoneyPrecise({
                money: takerToCelebrate.totalTakerEarningsInUserCurrency,
                withSymbol: false,
                sign: null,
            }),
        })
        onMsgLive.current({
            type: 'on_earn_celebration_triggered',
            owner,
            earnCelebrationConfig,
        })
    }, [
        earnCelebrationConfig,
        installationId,
        onMsgLive,
        owner,
        takerToCelebrate.taker.type,
        takerToCelebrate.totalTakerEarningsInUserCurrency,
        location,
    ])

    return (
        <>
            <Layout
                takerToCelebrate={takerToCelebrate}
                takerPortfolioMap={takerPortfolioMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'app_rating_request' })
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg.type)
                    }
                }}
            />
            <Modal
                installationId={installationId}
                state={modal}
                appRating={appRating}
                onMsg={onMsg}
            />
        </>
    )
}
