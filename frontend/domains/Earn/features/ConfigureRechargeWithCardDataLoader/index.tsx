import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { fetchGnosisPayAccountState2WithSilentLogin } from '@zeal/domains/Card/api/fetchGnosisPayAccountState'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, EarnTakerMetrics } from '@zeal/domains/Earn'
import { ConfigureRecharge } from '@zeal/domains/Earn/features/ConfigureRecharge'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { UserEvent } from '@zeal/domains/UserEvents'

export type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn

    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    installationId: string
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
    isEthereumNetworkFeeWarningSeen: boolean
    defaultCurrencyConfig: DefaultCurrencyConfig
    customCurrencies: CustomCurrencyMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    earnTakerMetrics: EarnTakerMetrics
    location: Extract<
        UserEvent,
        { type: 'RechargeConfigFlowEnteredEvent' }
    >['location']
}

type Msg =
    | {
          type: 'close'
      }
    | Extract<
          MsgOf<typeof ConfigureRecharge>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_earn_recharge_configured'
                  | 'on_earn_deposit_success'
                  | 'add_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_earn_configured'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_swaps_io_swap_request_created'
          }
      >

export const ConfigureRechargeWithCardDataLoader = ({
    earn,
    sessionPassword,
    gasCurrencyPresetMap,
    feePresetMap,
    portfolioMap,
    accountsMap,
    networkMap,
    networkRPCMap,
    installationId,
    keystores,
    keyStore,
    isEthereumNetworkFeeWarningSeen,
    onMsg,
    earnTakerMetrics,
    cardConfig,
    location,
    currencyHiddenMap,
    customCurrencies,
    currencyPinMap,
    defaultCurrencyConfig,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(
        fetchGnosisPayAccountState2WithSilentLogin,
        {
            type: 'loading',
            params: {
                sessionPassword,
                keyStore,
                readonlySignerAddress: cardConfig.readonlySignerAddress,
                selectedCardId: cardConfig.selectedCardId,
                networkRPCMap,
                networkMap,
                defaultCurrencyConfig,
            },
        }
    )
    const cardReadonlySigner = accountsMap[cardConfig.readonlySignerAddress]

    switch (loadable.type) {
        case 'loading':
            return (
                <DataLoaderLoadingLayout
                    onCloseClick={() => onMsg({ type: 'close' })}
                />
            )
        case 'loaded':
            switch (loadable.data.type) {
                case 'not_onboarded':
                    throw new ImperativeError(
                        'Impossible state: not onboarded card in recharge configuration'
                    )
                case 'onboarded':
                    return (
                        <ConfigureRecharge
                            earnTakerMetrics={earnTakerMetrics}
                            customCurrencies={customCurrencies}
                            cardConfig={cardConfig}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            cardReadonlySigner={cardReadonlySigner}
                            earn={earn}
                            gnosisPayAccountOnboardedState={loadable.data}
                            portfolioMap={portfolioMap}
                            accountsMap={accountsMap}
                            isEthereumNetworkFeeWarningSeen={
                                isEthereumNetworkFeeWarningSeen
                            }
                            keystores={keystores}
                            networkMap={networkMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            sessionPassword={sessionPassword}
                            installationId={installationId}
                            networkRPCMap={networkRPCMap}
                            currencyPinMap={currencyPinMap}
                            currencyHiddenMap={currencyHiddenMap}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'import_keys_button_clicked':
                                    case 'on_predefined_fee_preset_selected':
                                    case 'on_earn_recharge_configured':
                                    case 'on_earn_deposit_success':
                                    case 'add_wallet_clicked':
                                    case 'on_bank_transfer_selected':
                                    case 'track_wallet_clicked':
                                    case 'on_account_create_request':
                                    case 'on_accounts_create_success_animation_finished':
                                    case 'hardware_wallet_clicked':
                                    case 'on_add_label_to_track_only_account_during_send':
                                    case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                    case 'cancel_submitted':
                                    case 'on_transaction_completed_splash_animation_screen_competed':
                                    case 'transaction_request_replaced':
                                    case 'transaction_submited':
                                    case 'on_ethereum_network_fee_warning_understand_clicked':
                                    case 'on_earn_configured':
                                    case 'close':
                                    case 'on_address_scanned':
                                    case 'on_address_scanned_and_add_label':
                                    case 'on_usd_taker_metrics_loaded':
                                    case 'on_eur_taker_metrics_loaded':
                                    case 'on_swaps_io_swap_request_created':
                                        onMsg(msg)
                                        break
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                            location={location}
                        />
                    )
                default:
                    return notReachable(loadable.data)
            }
        case 'error':
            const error = parseAppError(loadable.error)
            return (
                <>
                    <DataLoaderLoadingLayout onCloseClick={noop} />
                    <AppErrorPopup
                        error={error}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        default:
            return notReachable(loadable)
    }
}

const DataLoaderLoadingLayout = ({
    onCloseClick,
}: {
    onCloseClick: () => void
}) => {
    return (
        <LoadingLayout
            title={null}
            actionBar={
                <ActionBar
                    left={
                        <Clickable onClick={onCloseClick}>
                            <BackIcon size={24} color="iconDefault" />
                        </Clickable>
                    }
                />
            }
            onClose={onCloseClick}
        />
    )
}
