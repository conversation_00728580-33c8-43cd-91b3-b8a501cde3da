import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    ConfiguredEarn,
    EarnCelebrationConfigForOneAccount,
    EarnTakerMetrics,
    TakerPortfolioMap2,
    TakerToCelebrate,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { TakerTransactionsMap } from '@zeal/domains/Earn/api/fetchTakerTransactionsMap'
import { EarnCelebration } from '@zeal/domains/Earn/features/EarnCelebration'
import { ViewEarn } from '@zeal/domains/Earn/features/ViewEarn'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { UserEvent } from '@zeal/domains/UserEvents'

type Props = {
    owner: Account
    state: State
    earn: ConfiguredEarn
    cardConfig: CardConfig

    portfolioMap: PortfolioMap

    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    isEthereumNetworkFeeWarningSeen: boolean
    networkRPCMap: NetworkRPCMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencies: CustomCurrencyMap
    earnTakerMetrics: EarnTakerMetrics
    pollable: PollableData<
        {
            takerTransactionsMap: TakerTransactionsMap
            takerPortfolioMap: TakerPortfolioMap2
        },
        unknown
    >
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap

    location: Extract<UserEvent, { type: 'EarnFlowEnteredEvent' }>['location']
    defaultCurrencyConfig: DefaultCurrencyConfig
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof ViewEarn> | MsgOf<typeof EarnCelebration>

export type State =
    | { type: 'closed' }
    | {
          type: 'view_earn'
      }
    | {
          type: 'earn_celebration'
          takerPortfolioMap: TakerPortfolioMap2
          earnCelebrationConfig: EarnCelebrationConfigForOneAccount
          takerToCelebrate: TakerToCelebrate
      }

export const Modal = ({
    state,
    earn,
    sessionPassword,
    networkRPCMap,
    networkMap,
    feePresetMap,
    gasCurrencyPresetMap,
    accounts,
    earnTakerMetrics,
    installationId,
    keystores,
    owner,
    currencyPinMap,
    portfolioMap,
    currencyHiddenMap,
    location,
    isEthereumNetworkFeeWarningSeen,
    cardConfig,
    customCurrencies,
    defaultCurrencyConfig,
    pollable,
    totalEarningsInDefaultCurrencyMap,
    appRating,
    celebrationConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null

        case 'earn_celebration':
            return (
                <UIModal>
                    <EarnCelebration
                        location="portfolio"
                        appRating={appRating}
                        takerPortfolioMap={state.takerPortfolioMap}
                        takerToCelebrate={state.takerToCelebrate}
                        onMsg={onMsg}
                        installationId={installationId}
                        owner={owner}
                        earnCelebrationConfig={state.earnCelebrationConfig}
                    />
                </UIModal>
            )

        case 'view_earn':
            return (
                <UIModal>
                    <ViewEarn
                        celebrationConfig={celebrationConfig}
                        appRating={appRating}
                        earnTakerMetrics={earnTakerMetrics}
                        customCurrencies={customCurrencies}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        portfolioMap={portfolioMap}
                        earn={earn}
                        cardConfig={cardConfig}
                        sessionPassword={sessionPassword}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        accounts={accounts}
                        keystores={keystores}
                        networkMap={networkMap}
                        feePresetMap={feePresetMap}
                        installationId={installationId}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        networkRPCMap={networkRPCMap}
                        owner={owner}
                        currencyPinMap={currencyPinMap}
                        currencyHiddenMap={currencyHiddenMap}
                        location={location}
                        pollable={pollable}
                        totalEarningsInDefaultCurrencyMap={
                            totalEarningsInDefaultCurrencyMap
                        }
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
