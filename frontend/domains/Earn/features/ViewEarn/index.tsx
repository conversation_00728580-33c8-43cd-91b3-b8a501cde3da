import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    ConfiguredEarn,
    EarnTakerMetrics,
    TakerPortfolioMap2,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { TakerTransactionsMap } from '@zeal/domains/Earn/api/fetchTakerTransactionsMap'
import { shouldCelebrateTaker } from '@zeal/domains/Earn/helpers/shouldCelbrateTaker'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { AppRating } from '@zeal/domains/Feedback'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { keystoreToUserEventType, UserEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    owner: Account
    earn: ConfiguredEarn
    cardConfig: CardConfig

    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    installationId: string
    portfolioMap: PortfolioMap
    networkRPCMap: NetworkRPCMap
    isEthereumNetworkFeeWarningSeen: boolean
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    customCurrencies: CustomCurrencyMap
    earnTakerMetrics: EarnTakerMetrics
    pollable: PollableData<
        {
            takerTransactionsMap: TakerTransactionsMap
            takerPortfolioMap: TakerPortfolioMap2
        },
        unknown
    >
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap

    location: Extract<UserEvent, { type: 'EarnFlowEnteredEvent' }>['location']
    defaultCurrencyConfig: DefaultCurrencyConfig
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_earn_deposit_success'
                  | 'on_earn_withdrawal_success'
                  | 'on_predefined_fee_preset_selected'
                  | 'safe_wallet_clicked'
                  | 'on_earn_recharge_configured'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_earn_taker_address_click'
                  | 'add_wallet_clicked'
                  | 'on_bank_transfer_selected'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_accounts_create_success_animation_finished'
                  | 'hardware_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_earn_configured'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_app_rating_submitted'
                  | 'on_earn_celebration_triggered'
                  | 'on_swaps_io_swap_request_created'
          }
      >

const calculateInitialModalState = ({
    pollable,
    celebrationConfig,
    owner,
    earn,
    defaultCurrencyConfig,
}: {
    celebrationConfig: CelebrationConfig
    earn: ConfiguredEarn
    pollable: PollableData<
        {
            takerTransactionsMap: TakerTransactionsMap
            takerPortfolioMap: TakerPortfolioMap2
        },
        unknown
    >
    owner: Account
    defaultCurrencyConfig: DefaultCurrencyConfig
}): ModalState => {
    switch (ZealPlatform.OS) {
        case 'web':
            return { type: 'closed' }
        case 'ios':
        case 'android':
            switch (pollable.type) {
                case 'loading':
                case 'error':
                    return { type: 'closed' }
                case 'loaded':
                case 'reloading':
                case 'subsequent_failed':
                    const earnCelebrationConfig = celebrationConfig.earn[
                        owner.address as Web3.address.Address
                    ] || {
                        highestTotalEarningCelebratedInUserCurrency: {},
                    }
                    const takersToCelebrate = earn.takers
                        .map((taker) => {
                            switch (taker.state) {
                                case 'not_deployed':
                                    return null
                                case 'deployed':
                                    const result = shouldCelebrateTaker({
                                        takerType: taker.type,
                                        celebrationConfig:
                                            earnCelebrationConfig,
                                        takerPortfolioMap:
                                            pollable.data.takerPortfolioMap,
                                        transactionsMap:
                                            pollable.data.takerTransactionsMap,
                                    })

                                    switch (result.type) {
                                        case 'should_not_celebrate':
                                            return null
                                        case 'should_celebrate':
                                            earnCelebrationConfig.highestTotalEarningCelebratedInUserCurrency[
                                                taker.type
                                            ] =
                                                result.totalEarningsInUserCurrency
                                            return {
                                                taker,
                                                totalTakerEarningsInUserCurrency:
                                                    result.totalEarningsInUserCurrency,
                                            }
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(result)
                                    }
                                /* istanbul ignore next */
                                default:
                                    return notReachable(taker)
                            }
                        })
                        .filter(excludeNullValues)

                    if (!takersToCelebrate.length) {
                        return { type: 'closed' }
                    }

                    const takerToCelebrate = takersToCelebrate.reduce(
                        (acc, value) => {
                            const valueMoney = sumTakerPortfolio({
                                takerPortfolioMap:
                                    pollable.data.takerPortfolioMap,
                                taker: value.taker,
                                defaultCurrencyConfig,
                            })
                            const accMoney = sumTakerPortfolio({
                                takerPortfolioMap:
                                    pollable.data.takerPortfolioMap,
                                taker: acc.taker,
                                defaultCurrencyConfig,
                            })

                            return valueMoney.amount > accMoney.amount
                                ? value
                                : acc
                        },
                        takersToCelebrate[0]
                    )

                    return {
                        type: 'earn_celebration',
                        takerToCelebrate,
                        takerPortfolioMap: pollable.data.takerPortfolioMap,
                        earnCelebrationConfig,
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(pollable)
            }

        /* istanbul ignore next */
        default:
            return notReachable(ZealPlatform)
    }
}

export const ViewEarn = ({
    earn,
    owner,
    cardConfig,
    installationId,
    keystores,
    networkMap,
    networkRPCMap,
    feePresetMap,
    gasCurrencyPresetMap,
    accounts,
    isEthereumNetworkFeeWarningSeen,
    sessionPassword,
    pollable,
    portfolioMap,
    earnTakerMetrics,
    currencyPinMap,
    currencyHiddenMap,
    customCurrencies,
    location,
    defaultCurrencyConfig,
    totalEarningsInDefaultCurrencyMap,
    appRating,
    celebrationConfig,
    onMsg,
}: Props) => {
    const [modalState, setModalState] = useState<ModalState>(() =>
        calculateInitialModalState({
            celebrationConfig,
            earn,
            pollable,
            owner,
            defaultCurrencyConfig,
        })
    )

    useEffect(() => {
        postUserEvent({
            type: 'EarnFlowEnteredEvent',
            earnStatus: 'enabled',
            location,
            keystoreType: keystoreToUserEventType(
                getKeyStore({ keyStoreMap: keystores, address: owner.address })
            ),
            installationId,
        })
    }, [installationId, location, keystores, owner.address])

    return (
        <>
            <Layout
                keyStoreMap={keystores}
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                earn={earn}
                pollable={pollable}
                cardConfig={cardConfig}
                owner={owner}
                totalEarningsInDefaultCurrencyMap={
                    totalEarningsInDefaultCurrencyMap
                }
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_add_another_asset_click':
                            setModalState({ type: 'add_another_asset' })
                            break
                        case 'on_earn_taker_list_item_click':
                            setModalState({
                                type: 'asset_details',
                                taker: msg.taker,
                            })
                            break
                        case 'on_earn_view_configure_recharge_click':
                            setModalState({
                                type: 'configure_recharge',
                                cardConfig: msg.cardConfig,
                                keyStore: msg.keyStore,
                            })
                            break

                        case 'on_earn_deposit_asset_click':
                            setModalState({
                                type: 'deposit_assets_from_zeal_wallet',
                                taker: msg.taker,
                            })
                            break
                        case 'on_earn_withdraw_asset_click':
                            setModalState({
                                type: 'withdraw_asset',
                                taker: msg.taker,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
            <Modal
                appRating={appRating}
                earnTakerMetrics={earnTakerMetrics}
                defaultCurrencyConfig={defaultCurrencyConfig}
                portfolioMap={portfolioMap}
                customCurrencies={customCurrencies}
                cardConfig={cardConfig}
                accounts={accounts}
                keystores={keystores}
                networkMap={networkMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                sessionPassword={sessionPassword}
                isEthereumNetworkFeeWarningSeen={
                    isEthereumNetworkFeeWarningSeen
                }
                installationId={installationId}
                networkRPCMap={networkRPCMap}
                earn={earn}
                owner={owner}
                currencyPinMap={currencyPinMap}
                currencyHiddenMap={currencyHiddenMap}
                state={modalState}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModalState({ type: 'closed' })
                            break
                        case 'on_earn_deposit_asset_click':
                            setModalState({
                                type: 'deposit_assets_from_zeal_wallet',
                                taker: msg.taker,
                            })
                            break
                        case 'on_earn_recharge_configured':
                            setModalState({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'on_earn_withdrawal_asset_click':
                            setModalState({
                                type: 'withdraw_asset',
                                taker: msg.taker,
                            })
                            break
                        case 'on_earn_withdrawal_success':
                        case 'on_earn_deposit_success':
                            setModalState({ type: 'closed' })
                            onMsg(msg)
                            break
                        case 'on_earn_configured':
                            onMsg(msg)
                            break
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_earn_taker_address_click':
                        case 'add_wallet_clicked':
                        case 'on_bank_transfer_selected':
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'on_accounts_create_success_animation_finished':
                        case 'hardware_wallet_clicked':
                        case 'on_add_label_to_track_only_account_during_send':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'cancel_submitted':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'on_ethereum_network_fee_warning_understand_clicked':
                        case 'on_address_scanned':
                        case 'on_address_scanned_and_add_label':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_app_rating_submitted':
                        case 'on_earn_celebration_triggered':
                        case 'on_swaps_io_swap_request_created':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
