import React from 'react'
import { FormattedMessage } from 'react-intl'

import { StoryWithPersistentActions } from '@zeal/uikit/StoryWithPersistentActions'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof StoryWithPersistentActions>,
          { type: 'on_stories_completed' }
      >
    | { type: 'on_stories_dismissed' }

export const Stories = ({ onMsg, installationId }: Props) => {
    return (
        <StoryWithPersistentActions
            slide={0}
            paused={false}
            stories={[
                {
                    title: (
                        <FormattedMessage
                            id="passkey-story_1.title"
                            defaultMessage="Forget gas - pay network fees in most tokens"
                        />
                    ),
                    subtitle: (
                        <FormattedMessage
                            id="passkey-story_1.subtitle"
                            defaultMessage="With a Smart Wallet you can pay network fees in most tokens, and don't have to worry about gas."
                        />
                    ),
                    artwork: {
                        type: 'lottie',
                        name: 'stables',
                    },
                },
                {
                    title: (
                        <FormattedMessage
                            id="passkey-story_2.title"
                            defaultMessage="Secured by Safe"
                        />
                    ),
                    subtitle: (
                        <FormattedMessage
                            id="passkey-story_2.subtitle"
                            defaultMessage="Built on Safe's industry-leading smart contracts that secure more than $100 billion in more than 20 million wallets."
                        />
                    ),
                    artwork: {
                        type: 'lottie',
                        name: 'safe',
                    },
                },
                {
                    title: (
                        <FormattedMessage
                            id="passkey-story_3.title"
                            defaultMessage="Major EVM networks supported"
                        />
                    ),
                    subtitle: (
                        <FormattedMessage
                            id="passkey-story_3.subtitle"
                            defaultMessage="Smart Wallets work on major Ethereum-compatible networks. Check the supported networks before sending assets."
                        />
                    ),
                    artwork: {
                        type: 'lottie',
                        name: 'networks',
                    },
                },
            ]}
            actions={{
                primary: {
                    title: (
                        <FormattedMessage
                            id="actions.continue"
                            defaultMessage="Create Wallet"
                        />
                    ),
                    onClick: () => onMsg({ type: 'on_stories_completed' }),
                },
                secondary: {
                    title: (
                        <FormattedMessage
                            id="actions.back"
                            defaultMessage="Cancel"
                        />
                    ),
                    onClick: () => {
                        postUserEvent({
                            type: 'StoryFlowDismissedEvent',
                            name: 'safe',
                            installationId,
                        })
                        onMsg({ type: 'on_stories_dismissed' })
                    },
                },
            }}
            onMsg={(msg) => {
                switch (msg.type) {
                    case 'on_next_slide_shown':
                        postUserEvent({
                            type: 'StoryFlowAdvancedEvent',
                            name: 'safe',
                            slideNumber: msg.currentSlide,
                            installationId,
                        })
                        break
                    case 'on_error':
                        captureError(msg.error)
                        break

                    case 'on_stories_completed':
                        postUserEvent({
                            type: 'StoryFlowFinishedEvent',
                            name: 'safe',
                            installationId,
                        })
                        break

                    /* istanbul ignore next */
                    default:
                        return notReachable(msg)
                }
            }}
        />
    )
}
