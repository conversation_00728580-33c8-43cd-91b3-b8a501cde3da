import {
    bigint,
    boolean,
    match,
    nullableOf,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { parseFxRateFromStorage } from '@zeal/domains/FXRate/helpers/parse'
import {
    parseCryptoMoneyFromStorage,
    parseDTO,
    parseFiatMoneyFromStorage,
} from '@zeal/domains/Money/helpers/parse'
import { parse as parseNetworkHexId } from '@zeal/domains/Network/helpers/parse'
import { Token, Token2 } from '@zeal/domains/Token'

/**
 *  @deprecated should die with Token
 *  TODO @resetko-zeal
 */
export const parseFXRate = (input: unknown): Result<unknown, Token['rate']> =>
    object(input).andThen((obj) =>
        shape({
            base: string(obj.base),
            quote: string(obj.quote),
            rate: bigint(obj.rate),
        })
    )

export const parseToken2 = (input: unknown): Result<unknown, Token2> => {
    return object(input).andThen((obj) =>
        shape({
            scam: oneOf(obj.scam, [boolean(obj.scam), success(false)]),
            rate: nullableOf(obj.rate, parseFxRateFromStorage),
            balance: parseCryptoMoneyFromStorage(obj.balance),
            priceInDefaultCurrency: nullableOf(
                obj.priceInDefaultCurrency,
                parseFiatMoneyFromStorage
            ),
            marketData: nullableOf(obj.marketData, (marketData) =>
                object(marketData).andThen((marketDataObj) =>
                    shape({
                        priceChange24h: object(
                            marketDataObj.priceChange24h
                        ).andThen((priceChange24hObj) =>
                            oneOf(priceChange24hObj, [
                                shape({
                                    direction: match(
                                        priceChange24hObj.direction,
                                        'Unchanged' as const
                                    ),
                                }),
                                shape({
                                    direction: match(
                                        priceChange24hObj.direction,
                                        'Up' as const
                                    ),
                                    percentage: number(
                                        priceChange24hObj.percentage
                                    ),
                                }),
                                shape({
                                    direction: match(
                                        priceChange24hObj.direction,
                                        'Down' as const
                                    ),
                                    percentage: number(
                                        priceChange24hObj.percentage
                                    ),
                                }),
                            ])
                        ),
                    })
                )
            ),
        })
    )
}

/**
 *  @deprecated should die with Token
 *  TODO @resetko-zeal
 */
export const parse = (input: unknown): Result<unknown, Token> =>
    object(input).andThen((obj) =>
        shape({
            networkHexId: oneOf(obj, [
                parseNetworkHexId(obj.network),
                parseNetworkHexId(obj.networkHexId),
            ]),
            scam: oneOf(obj.scam, [boolean(obj.scam), success(false)]),
            rate: nullableOf(obj.rate, parseFXRate),
            address: Web3.address.parse(obj.address),
            balance: parseDTO(obj.balance),
            priceInDefaultCurrency: nullableOf(
                obj.priceInDefaultCurrency,
                parseDTO
            ),
            marketData: nullableOf(obj.marketData, (marketData) =>
                object(marketData).andThen((marketDataObj) =>
                    shape({
                        priceChange24h: object(
                            marketDataObj.priceChange24h
                        ).andThen((priceChange24hObj) =>
                            oneOf(priceChange24hObj, [
                                shape({
                                    direction: match(
                                        priceChange24hObj.direction,
                                        'Unchanged' as const
                                    ),
                                }),
                                shape({
                                    direction: match(
                                        priceChange24hObj.direction,
                                        'Up' as const
                                    ),
                                    percentage: number(
                                        priceChange24hObj.percentage
                                    ),
                                }),
                                shape({
                                    direction: match(
                                        priceChange24hObj.direction,
                                        'Down' as const
                                    ),
                                    percentage: number(
                                        priceChange24hObj.percentage
                                    ),
                                }),
                            ])
                        ),
                    })
                )
            ),
        })
    )
