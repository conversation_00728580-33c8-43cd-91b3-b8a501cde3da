import { useEffect } from 'react'

import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'

import { UserAConfiguredReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { generateReferralCodeAndLink } from '@zeal/domains/Card/domains/Reward/api/generateReferralCodeAndLink'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    userAReferralConfig: { type: 'not_configured' }
    ownerAddress: Web3.address.Address
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_a_rewards_configured'
          userAReferralConfig: UserAConfiguredReferralConfig
      }
    | { type: 'on_rewards_config_close' }

export const ConfigureRewards = ({
    userAReferralConfig: _,
    ownerAddress,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(
        generateReferralCodeAndLink,
        {
            type: 'loading',
            params: { address: ownerAddress },
        }
    )

    const msgLive = useLiveRef(onMsg)

    useEffect(() => {
        postUserEvent({
            type: 'RewardsConfigurationEnteredEvent',
            installationId,
        })
    }, [installationId])

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                postUserEvent({
                    type: 'RewardsConfiguredEvent',
                    installationId,
                })
                msgLive.current({
                    type: 'on_a_rewards_configured',
                    userAReferralConfig: loadable.data,
                })
                break
            case 'loading':
                break
            case 'error':
                postUserEvent({
                    type: 'RewardsConfigurationFailedEvent',
                    installationId,
                })
                break
            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [installationId, loadable, msgLive])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={null}
                    onClose={() => onMsg({ type: 'on_rewards_config_close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={null}
                        onClose={() =>
                            onMsg({ type: 'on_rewards_config_close' })
                        }
                    />

                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({ type: 'on_rewards_config_close' })
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: { address: ownerAddress },
                                    })
                                    break

                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        default:
            return notReachable(loadable)
    }
}
