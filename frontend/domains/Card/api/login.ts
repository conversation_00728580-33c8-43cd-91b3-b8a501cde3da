import { post } from '@zeal/api/gnosisApi'

import { addMinutes } from '@zeal/toolkit/Date'
import { ImperativeError } from '@zeal/toolkit/Error'
import { withRetries } from '@zeal/toolkit/Function'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { object, string } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CardSlientSignKeyStore,
    GnosisPayLoginSignature,
} from '@zeal/domains/Card'
import {
    CARD_NETWORK,
    GNOSIS_PAY_SESSION_VALIDITY_MINUTES,
} from '@zeal/domains/Card/constants'
import { getLoginMessage } from '@zeal/domains/Card/helpers/getLoginMessage'
import { parseGnosisPayFailedToVerifySignature } from '@zeal/domains/Error/domains/GnosisPay/parsers/parseGnosisPayError'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { PersonalSign } from '@zeal/domains/RPCRequest'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { GnosisPayLoginInfo } from '..'

const challengeWithRetries = withRetries(
    async (
        gnosisPayLoginSignature: GnosisPayLoginSignature,
        signal?: AbortSignal
    ): Promise<GnosisPayLoginInfo> => {
        try {
            const response = await post(
                '/auth/challenge',
                {
                    body: {
                        message: gnosisPayLoginSignature.message,
                        signature: gnosisPayLoginSignature.signature,
                    },
                },
                signal
            )

            const token = string(response)
                .andThen(parseJSON)
                .andThen(object)
                .andThen((json) => string(json.token))
                .getSuccessResultOrThrow(
                    'Failed to parse gnosis pay login token'
                )

            return {
                type: 'gnosis_pay_login_info',
                token,
                expiresAtMs: addMinutes(
                    Date.now(),
                    GNOSIS_PAY_SESSION_VALIDITY_MINUTES
                ).getTime(),
            }
        } catch (error) {
            parseGnosisPayFailedToVerifySignature(error).tap((signatureError) =>
                captureError(
                    new ImperativeError(
                        '[Gnosis pay login] Failed to challenge signature in retries',
                        {
                            signatureError,
                        }
                    )
                )
            )
            throw error
        }
    },
    3,
    4000
)

export const loginWithoutCache = async ({
    keyStore,
    readonlySignerAddress,
    sessionPassword,
    signal,
}: {
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}): Promise<GnosisPayLoginInfo> => {
    const message = await getLoginMessage({
        address: readonlySignerAddress,
        signal,
    })

    const request: PersonalSign = {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'personal_sign',
        params: [message],
    }

    const signature = await signMessage({
        keyStore,
        network: CARD_NETWORK,
        request,
        sessionPassword,
        dApp: null,
    })

    const gnosisPayLoginSignature: GnosisPayLoginSignature = {
        type: 'gnosis_pay_login_info',
        address: readonlySignerAddress,
        message,
        signature,
    }

    const gnosisPayLoginInfo = await challengeWithRetries(
        gnosisPayLoginSignature,
        signal
    )

    return gnosisPayLoginInfo
}

const cache: Record<string, Promise<GnosisPayLoginInfo>> = {}

export const loginWithCache = async ({
    keyStore,
    readonlySignerAddress,
    sessionPassword,
    signal,
}: {
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
}): Promise<GnosisPayLoginInfo> => {
    const key = `${keyStore.type}-${readonlySignerAddress}`

    if (key in cache) {
        const cachedData = await cache[key]
        const now = Date.now()

        if (cachedData.expiresAtMs > now) {
            return cache[key]
        }
    }

    const promise = loginWithoutCache({
        keyStore,
        readonlySignerAddress,
        sessionPassword,
        signal,
    })
        .then((result) => {
            return result
        })
        .catch((error) => {
            delete cache[key]
            throw error
        })

    cache[key] = promise

    return promise
}
