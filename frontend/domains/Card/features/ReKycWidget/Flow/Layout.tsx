import { FormattedMessage } from 'react-intl'

import { DismissibleBanner } from '@zeal/uikit/DismissibleBanner'
import { BoldCardDanger } from '@zeal/uikit/Icon/BoldCardDanger'
import { BoldCrossOctagon } from '@zeal/uikit/Icon/BoldCrossOctagon'
import { BoldId } from '@zeal/uikit/Icon/BoldId'
import { Clock } from '@zeal/uikit/Icon/Clock'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { ProgressThin } from '@zeal/uikit/ProgressThin'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { differenceInCalendarDays, startOfDay } from '@zeal/toolkit/Date'

import {
    GnosisPayOnboardedKycStatus,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'

type Props = {
    kycStatus: GnosisPayOnboardedKycStatus
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    dismissible: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_kyc_banner_clicked'
          kycStatus: Extract<
              GnosisPayOnboardedKycStatus,
              | 'failed'
              | 'resubmission_requested'
              | 'verification_in_progress'
              | 'documents_requested'
              | 'not_started'
          >
      }
    | {
          type: 'on_dissmiss_card_kyc_onboarded_widget_clicked'
          kycStatus: Extract<
              GnosisPayOnboardedKycStatus,
              'failed' | 'resubmission_requested' | 'verification_in_progress'
          >
      }

type ProgressBannerKycStatus = Extract<
    GnosisPayOnboardedKycStatus,
    'verification_in_progress' | 'resubmission_requested' | 'failed'
>

const RE_KYC_DEADLINE = Date.UTC(2025, 6, 27, 0, 0, 0, 0) // July 27, 2025

type DeadlineResponse =
    | { type: 'deadline_missed' }
    | { type: 'deadline_upcoming'; daysUntil: number }

const getDaysUntilDeadline = (): DeadlineResponse => {
    const daysUntil = differenceInCalendarDays(
        startOfDay(RE_KYC_DEADLINE),
        startOfDay(Date.now())
    )

    if (daysUntil < 0) {
        return { type: 'deadline_missed' }
    }

    return { type: 'deadline_upcoming', daysUntil }
}

export const Layout = ({
    cardConfig,
    onMsg,
    kycStatus,
    dismissible,
}: Props) => {
    if (dismissible && cardConfig.lastDismissedKycBannerState === kycStatus) {
        return null
    }

    switch (kycStatus) {
        case 'approved':
            return null
        case 'not_started':
        case 'documents_requested':
            const daysUntilDeadline = getDaysUntilDeadline()

            return (
                <SubtextListItem
                    variant="outline"
                    size="large"
                    primaryText={
                        <FormattedMessage
                            id="gnosis-pay-re-kyc-widget.title.not-started"
                            defaultMessage="Verify your identity"
                        />
                    }
                    avatar={({ size }) => (
                        <BoldCardDanger size={size} color="orange30" />
                    )}
                    side={{
                        rightIcon: () => (
                            <SubtextListItem.SideButton
                                variant="regular"
                                onClick={() => {
                                    onMsg({
                                        type: 'on_kyc_banner_clicked',
                                        kycStatus,
                                    })
                                }}
                            >
                                <FormattedMessage
                                    id="gnosis-pay-re-kyc-widget.btn-text"
                                    defaultMessage="Verify"
                                />
                            </SubtextListItem.SideButton>
                        ),
                    }}
                    onClick={() => {
                        onMsg({
                            type: 'on_kyc_banner_clicked',
                            kycStatus,
                        })
                    }}
                    subItems={
                        <Text variant="footnote">
                            {(() => {
                                switch (daysUntilDeadline.type) {
                                    case 'deadline_missed':
                                        return (
                                            <FormattedMessage
                                                id="setup-card.order.virtualCard.steps"
                                                defaultMessage="Complete verification to continue using your card."
                                            />
                                        )
                                    case 'deadline_upcoming':
                                        return (
                                            <FormattedMessage
                                                id="setup-card.order.virtualCard.steps"
                                                defaultMessage="Complete verification within {daysUntil} days to keep using your card."
                                                values={{
                                                    daysUntil:
                                                        daysUntilDeadline.daysUntil,
                                                }}
                                            />
                                        )
                                    default:
                                        return notReachable(daysUntilDeadline)
                                }
                            })()}
                        </Text>
                    }
                />
            )
        case 'verification_in_progress':
        case 'resubmission_requested':
        case 'failed':
            return (
                <DismissibleBanner
                    title={<Title kycStatus={kycStatus} />}
                    subtitle={<SubTitle kycStatus={kycStatus} />}
                    bottom={<Progress kycStatus={kycStatus} />}
                    icon={({ size }) => (
                        <Icon kycStatus={kycStatus} size={size} />
                    )}
                    variant="light"
                    onClick={() =>
                        onMsg({ type: 'on_kyc_banner_clicked', kycStatus })
                    }
                    onDismiss={
                        dismissible
                            ? () =>
                                  onMsg({
                                      type: 'on_dissmiss_card_kyc_onboarded_widget_clicked',
                                      kycStatus,
                                  })
                            : undefined
                    }
                />
            )
        default:
            return notReachable(kycStatus)
    }
}

const Icon = ({
    kycStatus,
    size,
}: {
    kycStatus: ProgressBannerKycStatus
    size: number
}) => {
    switch (kycStatus) {
        case 'verification_in_progress':
            return <Clock color="blue30" size={size} />
        case 'resubmission_requested':
            return <BoldId color="orange30" size={size} />
        case 'failed':
            return <BoldCrossOctagon color="orange30" size={size} />
        default:
            return notReachable(kycStatus)
    }
}

const Title = ({ kycStatus }: { kycStatus: ProgressBannerKycStatus }) => {
    switch (kycStatus) {
        case 'verification_in_progress':
            return (
                <FormattedMessage
                    id="onboarding-widget.title.kyc_started_verification_in_progress"
                    defaultMessage="Verifying identity"
                />
            )
        case 'resubmission_requested':
            return (
                <FormattedMessage
                    id="onboarding-widget.title.kyc_started_resubmission_requested"
                    defaultMessage="Retry verification"
                />
            )
        case 'failed':
            return (
                <FormattedMessage
                    id="onboarding-widget.title.kyc_failed"
                    defaultMessage="Account not possible"
                />
            )
        default:
            return notReachable(kycStatus)
    }
}

const SubTitle = ({ kycStatus }: { kycStatus: ProgressBannerKycStatus }) => {
    switch (kycStatus) {
        case 'verification_in_progress':
            return (
                <FormattedMessage
                    id="onboarding-widget.title.kyc_started_verification_in_progress"
                    defaultMessage="Verifying identity"
                />
            )
        case 'resubmission_requested':
            return (
                <FormattedMessage
                    id="onboarding-widget.title.kyc_started_resubmission_requested"
                    defaultMessage="Retry verification"
                />
            )
        case 'failed':
            return (
                <FormattedMessage
                    id="onboarding-widget.title.kyc_failed"
                    defaultMessage="Account not possible"
                />
            )
        default:
            return notReachable(kycStatus)
    }
}

const Progress = ({ kycStatus }: { kycStatus: ProgressBannerKycStatus }) => {
    switch (kycStatus) {
        case 'verification_in_progress':
            return (
                <ProgressThin
                    animationTimeMs={300}
                    initialProgress={null}
                    progress={50}
                    background="neutral"
                />
            )
        case 'resubmission_requested':
            return (
                <ProgressThin
                    animationTimeMs={300}
                    initialProgress={null}
                    progress={50}
                    background="warning"
                />
            )
        case 'failed':
            return null
        default:
            return notReachable(kycStatus)
    }
}
