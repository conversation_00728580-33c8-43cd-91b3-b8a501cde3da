import { StepW<PERSON>rd } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSafeFullyConfigured,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { CreateVirtualCardOrder } from '@zeal/domains/Card/features/CreateVirtualCardOrder'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { Earn } from '@zeal/domains/Earn'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { FetchNewVirtualCard } from './FetchNewVirtualCard'
import { VirtualCardConfirmation } from './VirtualCardConfirmation'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn
    accountsMap: AccountsMap
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    location: 'card_settings' | 'card_tab'
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof VirtualCardConfirmation>, { type: 'close' }>
    | {
          type: 'on_new_virtual_card_created_successfully'
          cardId: string | null
      }

export type State =
    | { type: 'get_virtual_card_confirmation' }
    | { type: 'create_card_order' }
    | { type: 'fetch_new_virtual_card' }

const getCardSafeFullyConfigured = (
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
): CardSafeFullyConfigured => {
    return {
        type: 'fully_configured',
        address: cardConfig.lastSeenSafeAddress,
        cryptoCurrency: cardConfig.currency,
        fiatCurrency: convertStableCoinCurrencyToFiatCurrency({
            cryptoCurrency: cardConfig.currency,
        }),
    }
}

export const VirtualCardOrderFlow = ({
    defaultCurrencyConfig,
    networkMap,
    installationId,
    networkRPCMap,
    accountsMap,
    earn,
    cardConfig,
    sessionPassword,
    keyStore,
    location,
    onMsg,
}: Props) => {
    return (
        <StepWizard<State>
            initialStep={{
                type: 'get_virtual_card_confirmation',
            }}
        >
            {({ step, forwardTo, backTo, moveTo }) => {
                switch (step.type) {
                    case 'get_virtual_card_confirmation':
                        return (
                            <VirtualCardConfirmation
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg({ type: 'close' })
                                            break
                                        case 'on_order_vitrual_card_clicked':
                                            forwardTo({
                                                type: 'create_card_order',
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'create_card_order':
                        return (
                            <CreateVirtualCardOrder
                                location={location}
                                networkRPCMap={networkRPCMap}
                                networkMap={networkMap}
                                earn={earn}
                                cardSafe={getCardSafeFullyConfigured(
                                    cardConfig
                                )}
                                accountsMap={accountsMap}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                installationId={installationId}
                                sessionPassword={sessionPassword}
                                keyStore={keyStore}
                                cardConfig={cardConfig}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            backTo({
                                                type: 'get_virtual_card_confirmation',
                                            })
                                            break
                                        case 'on_virtual_card_order_created_animation_completed':
                                            moveTo({
                                                type: 'fetch_new_virtual_card',
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'fetch_new_virtual_card':
                        return (
                            <FetchNewVirtualCard
                                sessionPassword={sessionPassword}
                                keyStore={keyStore}
                                cardConfig={cardConfig}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            backTo({
                                                type: 'get_virtual_card_confirmation',
                                            })
                                            break
                                        case 'on_new_virtual_card_fetched':
                                            onMsg({
                                                type: 'on_new_virtual_card_created_successfully',
                                                cardId: msg.cardId,
                                            })
                                            break
                                        case 'on_new_virtual_card_not_found':
                                            onMsg({
                                                type: 'on_new_virtual_card_created_successfully',
                                                cardId: null,
                                            })
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
