import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountNotOnboardedStateGnosisPayKYCApprovedState,
    GnosisPayAccountNotOnboardedStatePostKYCCardCreatedFromOrder,
    GnosisPayKYCApprovedState,
    GnosisPayPostKYCApprovedStateCardCreatedFromOrder,
    GnosisPayPreKYCApprovedState,
    ReadonlySignerSelectedCardConfig,
} from '@zeal/domains/Card'
import { GnosisPayKycApprovedPopup } from '@zeal/domains/Card/components/GnosisPayKYCApprovedPopup'
import { GnosisPayKycFailedPopup } from '@zeal/domains/Card/components/GnosisPayKYCFailedPopup'
import { GnosisPayKycInProgressPopup } from '@zeal/domains/Card/components/GnosisPayKYCInProgressPopup'
import { ActivatePhysicalCard } from '@zeal/domains/Card/features/ActivatePhysicalCard'
import { GnosisPayOnboardingFlow } from '@zeal/domains/Card/features/GnosisPayOnboardingFlow'
import { PhysicalCardOrderFlow } from '@zeal/domains/Card/features/PhysicalCardOrderFlow'
import { Earn } from '@zeal/domains/Earn'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { GnosisPayOnboardingLocation } from '@zeal/domains/UserEvents'

import { ActivatePhysicalCardOrderOnboardingFlow } from './ActivatePhysicalCardOnboardingFlow'
import { VirtualCardOrderOnboardingFlow } from './VirtualCardOrderOnboardingFlow'

type Props = {
    cardReadonlySigner: Account
    earn: Earn
    accountsMap: AccountsMap
    cardConfig: ReadonlySignerSelectedCardConfig
    location: GnosisPayOnboardingLocation
    installationId: string
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    sessionPassword: string
    state: State
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof GnosisPayOnboardingFlow>
    | MsgOf<typeof GnosisPayKycApprovedPopup>
    | MsgOf<typeof GnosisPayKycFailedPopup>
    | MsgOf<typeof GnosisPayKycInProgressPopup>
    | MsgOf<typeof VirtualCardOrderOnboardingFlow>
    | MsgOf<typeof ActivatePhysicalCardOrderOnboardingFlow>
    | MsgOf<typeof ActivatePhysicalCard>
    | MsgOf<typeof PhysicalCardOrderFlow>

export type State =
    | { type: 'closed' }
    | {
          type: 'gnosis_pay_onboarding_flow'
          accountState: GnosisPayPreKYCApprovedState
      }
    | { type: 'kyc_in_progress_popup' }
    | { type: 'kyc_failed_popup' }
    | {
          type: 'virtual_card_order_onboarding_flow'
          gnosisPayAccountState:
              | GnosisPayKYCApprovedState
              | GnosisPayPostKYCApprovedStateCardCreatedFromOrder
      }
    | {
          type: 'activate_physical_card_onboarding_flow'
          gnosisPayPostKYCApprovedState: GnosisPayPostKYCApprovedStateCardCreatedFromOrder
      }
    | {
          type: 'activate_physical_card_flow'
          gnosisPayAccountState: GnosisPayAccountNotOnboardedStatePostKYCCardCreatedFromOrder
      }
    | {
          type: 'physical_card_order_flow'
          gnosisPayState: GnosisPayAccountNotOnboardedStateGnosisPayKYCApprovedState
      }

export const Modal = ({
    state,
    earn,
    cardConfig,
    location,
    sessionPassword,
    keyStore,
    networkRPCMap,
    installationId,
    defaultCurrencyConfig,
    networkMap,
    accountsMap,
    cardReadonlySigner,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'gnosis_pay_onboarding_flow':
            return (
                <UIModal>
                    <GnosisPayOnboardingFlow
                        accountState={state.accountState}
                        cardReadOnlySigner={cardReadonlySigner}
                        keyStore={keyStore}
                        sessionPassword={sessionPassword}
                        location={location}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'kyc_in_progress_popup':
            return <GnosisPayKycInProgressPopup onMsg={onMsg} />
        case 'kyc_failed_popup':
            return <GnosisPayKycFailedPopup onMsg={onMsg} />
        case 'virtual_card_order_onboarding_flow':
            return (
                <UIModal>
                    <VirtualCardOrderOnboardingFlow
                        earn={earn}
                        cardConfig={cardConfig}
                        accountsMap={accountsMap}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        networkMap={networkMap}
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                        installationId={installationId}
                        gnosisPayAccountState={state.gnosisPayAccountState}
                    />
                </UIModal>
            )

        case 'activate_physical_card_onboarding_flow':
            return (
                <UIModal>
                    <ActivatePhysicalCardOrderOnboardingFlow
                        gnosisPayPostKYCApprovedState={
                            state.gnosisPayPostKYCApprovedState
                        }
                        cardReadonlySigner={cardReadonlySigner}
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        cardConfig={cardConfig}
                        networkRPCMap={networkRPCMap}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'activate_physical_card_flow':
            return (
                <UIModal>
                    <ActivatePhysicalCard
                        gnosisPayAccountState={state.gnosisPayAccountState}
                        cardReadonlySigner={cardReadonlySigner}
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        installationId={installationId}
                        onMsg={onMsg}
                        notActivatedPhysicalCards={
                            state.gnosisPayAccountState.state
                                .notActivatedPhysicalCards
                        }
                        location="card_tab"
                    />
                </UIModal>
            )
        case 'physical_card_order_flow':
            return (
                <UIModal>
                    <PhysicalCardOrderFlow
                        gnosisPayState={state.gnosisPayState}
                        keyStore={keyStore}
                        sessionPassword={sessionPassword}
                        cardConfig={cardConfig}
                        installationId={installationId}
                        networkRPCMap={networkRPCMap}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}
