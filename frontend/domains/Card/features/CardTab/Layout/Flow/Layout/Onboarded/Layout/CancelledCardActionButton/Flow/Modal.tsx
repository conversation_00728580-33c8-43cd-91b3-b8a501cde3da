import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { SwitchCard } from '@zeal/domains/Card/features/SwitchCard'
import { VirtualCardOrderFlow } from '@zeal/domains/Card/features/VirtualCardOrderFlow'
import { Earn } from '@zeal/domains/Earn'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    state: State
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    cardReadonlySigner: Account
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    earn: Earn
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    installationId: string
    accoutsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | { type: 'switch_card' }
    | { type: 'create_virtual_card' }

type Msg = MsgOf<typeof SwitchCard> | MsgOf<typeof VirtualCardOrderFlow>

export const Modal = ({
    state,
    onMsg,
    installationId,
    defaultCurrencyConfig,
    networkMap,
    cardReadonlySigner,
    cardConfig,
    earn,
    accoutsMap,
    keyStore,
    sessionPassword,
    networkRPCMap,
    gnosisPayAccountOnboardedState,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null

        case 'switch_card':
            return (
                <UIModal>
                    <SwitchCard
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        cardReadonlySigner={cardReadonlySigner}
                        gnosisPayAccountOnboardedState={
                            gnosisPayAccountOnboardedState
                        }
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'create_virtual_card':
            return (
                <UIModal>
                    <VirtualCardOrderFlow
                        location="card_tab"
                        cardConfig={cardConfig}
                        earn={earn}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        accountsMap={accoutsMap}
                        networkMap={networkMap}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        keyStore={keyStore}
                        installationId={installationId}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            notReachable(state)
    }
}
