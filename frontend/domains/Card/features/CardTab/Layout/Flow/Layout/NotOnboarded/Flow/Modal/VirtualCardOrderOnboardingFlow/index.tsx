import { useEffect } from 'react'

import { StepWizard } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSafeCurrencyConfigured,
    CardSafeDeployed,
    CardSafeFullyConfigured,
    CardSafeNotDeployed,
    CardSlientSignKeyStore,
    GnosisPayKYCApprovedState,
    GnosisPayPostKYCApprovedStateCardCreatedFromOrder,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { CreateVirtualCardOrder } from '@zeal/domains/Card/features/CreateVirtualCardOrder'
import { PrepareCardSafe } from '@zeal/domains/Card/features/PrepareCardSafe'
import { PrepareCardSafeWithVerification } from '@zeal/domains/Card/features/PrepareCardSafeWithVerification'
import { Earn } from '@zeal/domains/Earn'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    installationId: string
    earn: Earn
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    gnosisPayAccountState:
        | GnosisPayKYCApprovedState
        | GnosisPayPostKYCApprovedStateCardCreatedFromOrder
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type State =
    | {
          type: 'prepare_card_safe'
          gnosisPayAccountState: GnosisPayPostKYCApprovedStateCardCreatedFromOrder
          cardSafe:
              | CardSafeNotDeployed
              | CardSafeDeployed
              | CardSafeCurrencyConfigured
      }
    | {
          type: 'prepare_card_safe_with_verification'
          gnosisPayAccountState: GnosisPayKYCApprovedState
          cardSafe:
              | CardSafeNotDeployed
              | CardSafeDeployed
              | CardSafeCurrencyConfigured
      }
    | {
          type: 'create_card_order'
          cardSafe: CardSafeFullyConfigured
      }

type Msg =
    | Extract<MsgOf<typeof PrepareCardSafe>, { type: 'close' }>
    | Extract<MsgOf<typeof PrepareCardSafeWithVerification>, { type: 'close' }>
    | MsgOf<typeof CreateVirtualCardOrder>

const calculateInitialState = ({
    gnosisPayAccountState,
}: {
    gnosisPayAccountState:
        | GnosisPayKYCApprovedState
        | GnosisPayPostKYCApprovedStateCardCreatedFromOrder
}): State => {
    switch (gnosisPayAccountState.type) {
        case 'kyc_approved': {
            const { cardSafe } = gnosisPayAccountState

            switch (cardSafe.type) {
                case 'not_deployed':
                case 'deployed':
                case 'currency_configured':
                    return {
                        type: 'prepare_card_safe_with_verification',
                        gnosisPayAccountState,
                        cardSafe,
                    }

                case 'fully_configured':
                    return { type: 'create_card_order', cardSafe }

                default:
                    return notReachable(cardSafe)
            }
        }
        case 'card_created_from_order': {
            const { cardSafe } = gnosisPayAccountState

            switch (cardSafe.type) {
                case 'not_deployed':
                case 'deployed':
                case 'currency_configured':
                    return {
                        type: 'prepare_card_safe',
                        gnosisPayAccountState,
                        cardSafe,
                    }
                case 'fully_configured':
                    return { type: 'create_card_order', cardSafe }

                default:
                    return notReachable(cardSafe)
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(gnosisPayAccountState)
    }
}

export const VirtualCardOrderOnboardingFlow = ({
    gnosisPayAccountState,
    sessionPassword,
    networkRPCMap,
    cardConfig,
    earn,
    keyStore,
    installationId,
    defaultCurrencyConfig,
    accountsMap,
    networkMap,
    onMsg,
}: Props) => {
    useEffect(() => {
        postUserEvent({
            type: 'VirtualCardOrderFlowEnteredEvent',
            installationId,
        })
    }, [installationId])

    return (
        <StepWizard<State>
            initialStep={() => calculateInitialState({ gnosisPayAccountState })}
        >
            {({ step, moveTo }) => {
                switch (step.type) {
                    case 'prepare_card_safe':
                        return (
                            <PrepareCardSafe
                                installationId={installationId}
                                keyStore={keyStore}
                                cardSafe={step.cardSafe}
                                cardConfig={cardConfig}
                                networkRPCMap={networkRPCMap}
                                sessionPassword={sessionPassword}
                                location="virtual_card_order_flow"
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'card_safe_configured':
                                            moveTo({
                                                type: 'create_card_order',
                                                cardSafe: msg.cardSafe,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'prepare_card_safe_with_verification':
                        return (
                            <PrepareCardSafeWithVerification
                                gnosisPayState={step.gnosisPayAccountState}
                                installationId={installationId}
                                keyStore={keyStore}
                                cardSafe={step.cardSafe}
                                cardConfig={cardConfig}
                                networkRPCMap={networkRPCMap}
                                sessionPassword={sessionPassword}
                                location="virtual_card_order_flow"
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'card_safe_configured':
                                            moveTo({
                                                type: 'create_card_order',
                                                cardSafe: msg.cardSafe,
                                            })
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )

                    case 'create_card_order':
                        return (
                            <CreateVirtualCardOrder
                                location="card_tab"
                                installationId={installationId}
                                accountsMap={accountsMap}
                                sessionPassword={sessionPassword}
                                earn={earn}
                                cardConfig={cardConfig}
                                cardSafe={step.cardSafe}
                                keyStore={keyStore}
                                networkRPCMap={networkRPCMap}
                                networkMap={networkMap}
                                onMsg={onMsg}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
