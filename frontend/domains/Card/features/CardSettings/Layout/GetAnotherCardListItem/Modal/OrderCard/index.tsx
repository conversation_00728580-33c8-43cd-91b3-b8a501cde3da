import { StepW<PERSON>rd } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { PhysicalCardOrderFlow } from '@zeal/domains/Card/features/PhysicalCardOrderFlow'
import { VirtualCardOrderFlow } from '@zeal/domains/Card/features/VirtualCardOrderFlow'
import { Earn } from '@zeal/domains/Earn'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { ChooseCardType } from './ChooseCardType'

type Props = {
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    gnosisPayState: GnosisPayAccountOnboardedState
    earn: Earn
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    accountsMap: AccountsMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type State =
    | {
          type: 'choose_card_type'
      }
    | { type: 'order_virtual_card' }
    | { type: 'order_physical_card' }

type Msg =
    | Extract<
          MsgOf<typeof VirtualCardOrderFlow>,
          { type: 'on_new_virtual_card_created_successfully' }
      >
    | Extract<MsgOf<typeof ChooseCardType>, { type: 'close' }>
    | Extract<
          MsgOf<typeof PhysicalCardOrderFlow>,
          {
              type:
                  | 'on_new_physical_card_created_successfully'
                  | 'on_card_order_redirect_to_gnosis_pay_clicked'
          }
      >

export const OrderCard = ({
    sessionPassword,
    gnosisPayState,
    earn,
    cardConfig,
    keyStore,
    installationId,
    defaultCurrencyConfig,
    accountsMap,
    networkMap,
    networkRPCMap,
    onMsg,
}: Props) => {
    return (
        <StepWizard<State> initialStep={{ type: 'choose_card_type' }}>
            {({ step, forwardTo, backTo, moveTo }) => {
                switch (step.type) {
                    case 'choose_card_type':
                        return (
                            <ChooseCardType
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_virtual_card_order_clicked':
                                            forwardTo({
                                                type: 'order_virtual_card',
                                            })
                                            break
                                        case 'on_physical_card_order_clicked':
                                            forwardTo({
                                                type: 'order_physical_card',
                                            })
                                            break
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'order_virtual_card':
                        return (
                            <VirtualCardOrderFlow
                                location="card_settings"
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                accountsMap={accountsMap}
                                networkMap={networkMap}
                                networkRPCMap={networkRPCMap}
                                sessionPassword={sessionPassword}
                                earn={earn}
                                cardConfig={cardConfig}
                                keyStore={keyStore}
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            backTo({ type: 'choose_card_type' })
                                            break
                                        case 'on_new_virtual_card_created_successfully':
                                            onMsg(msg)
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )

                    case 'order_physical_card':
                        return (
                            <PhysicalCardOrderFlow
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            backTo({ type: 'choose_card_type' })
                                            break

                                        case 'on_card_order_redirect_to_gnosis_pay_clicked':
                                        case 'on_new_physical_card_created_successfully':
                                            onMsg(msg)
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                                installationId={installationId}
                                cardConfig={cardConfig}
                                gnosisPayState={gnosisPayState}
                                keyStore={keyStore}
                                networkRPCMap={networkRPCMap}
                                sessionPassword={sessionPassword}
                            />
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
