import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { usePollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardBalance, GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { fetchMaxBalance } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchMaxBalance'
import { fetchQuote } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'
import { serverPortfolioToServerPortfolio2 } from '@zeal/domains/Portfolio/helpers/portfolioToPortfolio2'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    selectedCurrency: CryptoCurrency
    cardBalance: CardBalance
    amount: string | null
    portfolio: Portfolio
    sender: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    supportedTopupCurrencies: CryptoCurrency[]
    currencyPinMap: CurrencyPinMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap

    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    contractsMap: SwapsIOContractsMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_crypto_currency_selected'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_swap_created_close_clicked'
                  | 'on_earn_account_selected'
                  | 'on_swaps_io_swap_request_created'
          }
      >
    | Extract<
          MsgOf<typeof Layout>,
          { type: 'on_amount_change' | 'close' | 'on_from_account_click' }
      >

const QUOTE_REFRESH_INTERVAL_MS = 60000

export const Swap = ({
    portfolio,
    sender,
    gnosisPayAccountOnboardedState,
    supportedTopupCurrencies,
    selectedCurrency,
    amount,
    currencyPinMap,
    keyStoreMap,
    installationId,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    sessionPassword,
    cardBalance,
    contractsMap,
    onMsg,
    defaultCurrencyConfig,
}: Props) => {
    const captureErrorOnce = useCaptureErrorOnce()
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    const [pollable, setPollable] = usePollableData(
        fetchQuote,
        {
            type: 'loading',
            params: {
                networkMap,
                networkRPCMap,
                fromCurrency: selectedCurrency,
                toCurrency:
                    gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency,
                amount,
                sender,
                contractsMap,
                receiver: gnosisPayAccountOnboardedState.cardSafe.address,
                defaultCurrencyConfig,
            },
        },
        {
            pollIntervalMilliseconds: QUOTE_REFRESH_INTERVAL_MS,
        }
    )

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'loading':
                break
            case 'subsequent_failed':
            case 'error':
                captureErrorOnce(pollable.error)
                break
            default:
                notReachable(pollable)
        }
    }, [pollable, captureErrorOnce])

    useEffect(() => {
        if (
            amount !== pollable.params.amount ||
            selectedCurrency.id !== pollable.params.fromCurrency.id
        ) {
            switch (pollable.type) {
                case 'loaded':
                case 'reloading':
                case 'subsequent_failed':
                    setPollable({
                        type: 'reloading',
                        params: {
                            ...pollable.params,
                            fromCurrency: selectedCurrency,
                            amount,
                        },
                        data: pollable.data,
                    })
                    break
                case 'loading':
                case 'error':
                    setPollable({
                        type: 'loading',
                        params: {
                            ...pollable.params,
                            fromCurrency: selectedCurrency,
                            amount,
                        },
                    })
                    break
                default:
                    notReachable(pollable)
            }
        }
    }, [amount, pollable, selectedCurrency, setPollable])

    const fromCurrencyBalance = getTokenByCryptoCurrency2({
        currency: pollable.params.fromCurrency,
        portfolio,
    })

    const [maxBalanceLoadable, setMaxBalanceLoadable] = useLoadableData(
        fetchMaxBalance,
        {
            type: 'loading',
            params: {
                swapQuoteRequest: {
                    ...pollable.params,
                    amount: toFixedWithFraction(
                        fromCurrencyBalance.balance.amount,
                        fromCurrencyBalance.balance.currency.fraction
                    ),
                },
                serverPortfolio: serverPortfolioToServerPortfolio2({
                    serverPortfolio: portfolio,
                }),
                gasCurrencyPresetMap,
                installationId,
                keyStore: getKeyStore({
                    address: sender.address,
                    keyStoreMap,
                }),
            },
        }
    )

    useEffect(() => {
        const fromBalance = getTokenByCryptoCurrency2({
            currency: pollable.params.fromCurrency,
            portfolio,
        })

        setMaxBalanceLoadable((old) => ({
            type: 'loading',
            params: {
                ...old.params,
                portfolio,
                swapQuoteRequest: {
                    contractsMap,
                    defaultCurrencyConfig,
                    fromCurrency: pollable.params.fromCurrency,
                    networkMap,
                    networkRPCMap,
                    receiver: pollable.params.receiver,
                    sender,
                    toCurrency: pollable.params.toCurrency,
                    amount: toFixedWithFraction(
                        fromBalance.balance.amount,
                        fromBalance.balance.currency.fraction
                    ),
                },
            },
        }))
    }, [
        pollable.params.fromCurrency,
        pollable.params.receiver,
        pollable.params.toCurrency,
        portfolio,
        setMaxBalanceLoadable,
        contractsMap,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        sender,
    ])

    useEffect(() => {
        switch (maxBalanceLoadable.type) {
            case 'error':
                captureError(maxBalanceLoadable.error, {
                    extra: {
                        context:
                            'maxButton balance correction on add card to cash',
                    },
                })
                break

            case 'loaded':
            case 'loading':
                break

            /* istanbul ignore next */
            default:
                notReachable(maxBalanceLoadable)
        }
    }, [maxBalanceLoadable])

    return (
        <>
            <Layout
                installationId={installationId}
                maxBalanceLoadable={maxBalanceLoadable}
                cardBalance={cardBalance}
                networkMap={networkMap}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                senderPortfolio={portfolio}
                pollable={pollable}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_amount_change':
                        case 'on_from_account_click':
                            onMsg(msg)
                            break
                        case 'on_from_currency_clicked':
                            setModal({ type: 'token_selector' })
                            break
                        case 'on_submit_clicked':
                            setModal({ type: 'submit_swap', quote: msg.quote })
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                state={modal}
                defaultCurrencyConfig={defaultCurrencyConfig}
                sender={sender}
                senderPortfolio={portfolio}
                supportedTopupCurrencies={supportedTopupCurrencies}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                currencyPinMap={currencyPinMap}
                keyStoreMap={keyStoreMap}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break

                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_crypto_currency_selected':
                        case 'on_swap_cancelled_close_clicked':
                        case 'on_swap_success_clicked':
                        case 'on_swap_created_close_clicked':
                        case 'on_earn_account_selected':
                            onMsg(msg)
                            setModal({ type: 'closed' })
                            break
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_swaps_io_swap_request_created':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                sessionPassword={sessionPassword}
            />
        </>
    )
}
