import { useState } from 'react'

import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { notReachable } from '@zeal/toolkit/notReachable'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardBalance, GnosisPayAccountOnboardedState } from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { OPTIMISM_USDC } from '@zeal/domains/Currency/constants'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { ConfiguredEarn, DeployedTaker } from '@zeal/domains/Earn'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { compare2 } from '@zeal/domains/Money/helpers/compare'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio } from '@zeal/domains/Portfolio'
import { portfolioToPortfolio2 } from '@zeal/domains/Portfolio/helpers/portfolioToPortfolio2'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { AddFromEarn } from './AddFromEarn'
import { Send } from './Send'
import { Swap } from './Swap'

type Props = {
    portfolio: Portfolio
    cardBalance: CardBalance
    sender: Account
    initialAmountToTopup: CryptoMoney | null
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    supportedTopupCurrencies: CryptoCurrency[]
    currencyPinMap: CurrencyPinMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    contractsMap: SwapsIOContractsMap

    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Send>,
          {
              type:
                  | 'close'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'cancel_submitted'
                  | 'on_completed_transaction_close_click'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_from_account_click'
          }
      >
    | Extract<
          MsgOf<typeof Swap>,
          {
              type:
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'close'
                  | 'on_swap_created_close_clicked'
                  | 'on_from_account_click'
                  | 'on_swaps_io_swap_request_created'
          }
      >
    | Extract<
          MsgOf<typeof AddFromEarn>,
          {
              type:
                  | 'close'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_completed_transaction_close_click'
                  | 'on_from_account_click'
          }
      >

type TokenTopUpState =
    | {
          type: 'swap'
          currency: CryptoCurrency
          amount: string | null
      }
    | {
          type: 'send'
          currency: CryptoCurrency
          amount: string | null
      }
type State =
    | TokenTopUpState
    | {
          type: 'add_from_earn'
          taker: DeployedTaker
          configuredEarn: ConfiguredEarn
      }

const getEligibleEarnAccounts = ({
    portfolio,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio
    defaultCurrencyConfig: DefaultCurrencyConfig
}): {
    type: 'earn_account'
    taker: DeployedTaker
    balanceInDefaultCurrency: FiatMoney
    earn: ConfiguredEarn
}[] => {
    switch (portfolio.earn.type) {
        case 'not_configured':
            return []
        case 'configured':
            const earn = portfolio.earn
            return earn.takers
                .filter((taker): taker is DeployedTaker => {
                    switch (taker.state) {
                        case 'not_deployed':
                            return false
                        case 'deployed':
                            return true
                        /* istanbul ignore next */
                        default:
                            return notReachable(taker)
                    }
                })
                .map((taker) => ({
                    type: 'earn_account' as const,
                    taker,
                    earn,
                    balanceInDefaultCurrency: sumTakerPortfolio({
                        taker,
                        takerPortfolioMap: earn.takerPortfolioMap,
                        defaultCurrencyConfig,
                    }),
                }))
        /* istanbul ignore next */
        default:
            return notReachable(portfolio.earn)
    }
}

const getEligiblePortfolioTokens = ({
    portfolio,
    supportedTopupCurrencies,
    defaultCurrencyConfig,
}: {
    portfolio: Portfolio
    supportedTopupCurrencies: CryptoCurrency[]
    defaultCurrencyConfig: DefaultCurrencyConfig
}): {
    type: 'token'
    cryptoCurrency: CryptoCurrency
    balanceInDefaultCurrency: FiatMoney
}[] => {
    const supportedCurrencyIds = new Set(
        supportedTopupCurrencies.map(({ id }) => id)
    )

    const portfolioTokens = portfolioToPortfolio2({ portfolio }).tokens

    return portfolioTokens
        .filter((token) => supportedCurrencyIds.has(token.balance.currency.id))
        .map((token) => ({
            type: 'token' as const,
            cryptoCurrency: token.balance.currency,
            balanceInDefaultCurrency: token.priceInDefaultCurrency || {
                amount: 0n,
                currency: defaultCurrencyConfig.defaultCurrency,
            },
        }))
}

const calculateInitialState = ({
    initialAmountToTopup,
    gnosisPayAccountOnboardedState,
    portfolio,
    defaultCurrencyConfig,
    supportedTopupCurrencies,
}: {
    initialAmountToTopup: CryptoMoney | null
    portfolio: Portfolio
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    defaultCurrencyConfig: DefaultCurrencyConfig
    supportedTopupCurrencies: CryptoCurrency[]
}): State => {
    if (initialAmountToTopup) {
        return calculateTokenTopUpState({
            gnosisPayAccountOnboardedState,
            currency: initialAmountToTopup.currency,
            amount: toFixedWithFraction(
                initialAmountToTopup.amount,
                initialAmountToTopup.currency.fraction
            ),
        })
    }
    const eligibleTokens = getEligiblePortfolioTokens({
        defaultCurrencyConfig,
        supportedTopupCurrencies,
        portfolio,
    })

    const eligibleEarnAccounts = getEligibleEarnAccounts({
        defaultCurrencyConfig,
        portfolio,
    })

    const [assetWithMaxBalance] = [
        ...eligibleTokens,
        ...eligibleEarnAccounts,
    ].sort((a, b) =>
        compare2(b.balanceInDefaultCurrency, a.balanceInDefaultCurrency)
    )

    if (
        !assetWithMaxBalance ||
        assetWithMaxBalance.balanceInDefaultCurrency.amount === 0n
    ) {
        return calculateTokenTopUpState({
            gnosisPayAccountOnboardedState,
            amount: null,
            currency: OPTIMISM_USDC,
        })
    }

    switch (assetWithMaxBalance.type) {
        case 'earn_account':
            return {
                type: 'add_from_earn',
                taker: assetWithMaxBalance.taker,
                configuredEarn: assetWithMaxBalance.earn,
            }
        case 'token':
            return calculateTokenTopUpState({
                gnosisPayAccountOnboardedState,
                amount: null,
                currency: assetWithMaxBalance.cryptoCurrency,
            })
        /* istanbul ignore next */
        default:
            return notReachable(assetWithMaxBalance)
    }
}

const calculateTokenTopUpState = ({
    gnosisPayAccountOnboardedState,
    currency,
    amount,
}: {
    currency: CryptoCurrency
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    amount: string | null
}): TokenTopUpState => {
    if (
        gnosisPayAccountOnboardedState.cardSafe.cryptoCurrency.id ===
        currency.id
    ) {
        return { type: 'send', currency, amount }
    }
    return { type: 'swap', currency, amount }
}

export const Fork = ({
    portfolio,
    gnosisPayAccountOnboardedState,
    supportedTopupCurrencies,
    currencyPinMap,
    keyStoreMap,
    installationId,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    sender,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    sessionPassword,
    cardBalance,
    contractsMap,
    defaultCurrencyConfig,
    initialAmountToTopup,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>(
        calculateInitialState({
            portfolio,
            gnosisPayAccountOnboardedState,
            supportedTopupCurrencies,
            defaultCurrencyConfig,
            initialAmountToTopup,
        })
    )

    switch (state.type) {
        case 'swap':
            return (
                <Swap
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    contractsMap={contractsMap}
                    cardBalance={cardBalance}
                    sender={sender}
                    portfolio={portfolio}
                    supportedTopupCurrencies={supportedTopupCurrencies}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    currencyPinMap={currencyPinMap}
                    keyStoreMap={keyStoreMap}
                    installationId={installationId}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    selectedCurrency={state.currency}
                    amount={state.amount}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_crypto_currency_selected':
                                setState(
                                    calculateTokenTopUpState({
                                        amount: state.amount,
                                        currency: msg.currency,
                                        gnosisPayAccountOnboardedState,
                                    })
                                )
                                break
                            case 'on_earn_account_selected':
                                setState({
                                    type: 'add_from_earn',
                                    taker: msg.taker,
                                    configuredEarn: msg.earn,
                                })
                                break
                            case 'on_swap_cancelled_close_clicked':
                            case 'on_swap_success_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_select_rpc_click':
                            case 'on_rpc_change_confirmed':
                            case 'close':
                            case 'on_swap_created_close_clicked':
                            case 'on_from_account_click':
                            case 'on_swaps_io_swap_request_created':
                                onMsg(msg)
                                break

                            case 'on_amount_change':
                                setState({ ...state, amount: msg.amount })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    sessionPassword={sessionPassword}
                />
            )
        case 'send':
            return (
                <Send
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    supportedTopupCurrencies={supportedTopupCurrencies}
                    installationId={installationId}
                    senderPortfolio={portfolio}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    sender={sender}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    feePresetMap={feePresetMap}
                    sessionPassword={sessionPassword}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_crypto_currency_selected':
                                setState(
                                    calculateTokenTopUpState({
                                        amount: state.amount,
                                        currency: msg.currency,
                                        gnosisPayAccountOnboardedState,
                                    })
                                )
                                break
                            case 'on_earn_account_selected':
                                setState({
                                    type: 'add_from_earn',
                                    taker: msg.taker,
                                    configuredEarn: msg.earn,
                                })
                                break
                            case 'close':
                            case 'on_select_rpc_click':
                            case 'on_rpc_change_confirmed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_completed_safe_transaction_close_click':
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'cancel_submitted':
                            case 'on_completed_transaction_close_click':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'transaction_request_replaced':
                            case 'transaction_submited':
                            case 'on_from_account_click':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )

        case 'add_from_earn':
            return (
                <AddFromEarn
                    earn={state.configuredEarn}
                    taker={state.taker}
                    earnOwner={sender}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    supportedTopupCurrencies={supportedTopupCurrencies}
                    gnosisPayAccountOnboardedState={
                        gnosisPayAccountOnboardedState
                    }
                    portfolio={portfolio}
                    currencyPinMap={currencyPinMap}
                    keyStoreMap={keyStoreMap}
                    installationId={installationId}
                    currencyHiddenMap={currencyHiddenMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    sessionPassword={sessionPassword}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    keystoreMap={keyStoreMap}
                    cardBalance={cardBalance}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_crypto_currency_selected':
                                setState(
                                    calculateTokenTopUpState({
                                        currency: msg.currency,
                                        gnosisPayAccountOnboardedState,
                                        amount: null,
                                    })
                                )
                                break
                            case 'close':
                            case 'on_select_rpc_click':
                            case 'on_rpc_change_confirmed':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_completed_safe_transaction_close_click':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_completed_transaction_close_click':
                            case 'on_from_account_click':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
