import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CardBalance,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio, PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Fork } from './Layout/Fork'
import { Modal, State as ModalState } from './Modal'

type Props = {
    initialAmountToTopup: CryptoMoney | null
    sender: Account
    gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
    supportedTopupCurrencies: CryptoCurrency[]
    cardBalance: CardBalance
    contractsMap: SwapsIOContractsMap

    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    portfolio: Portfolio

    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof Fork>,
          {
              type:
                  | 'close'
                  | 'on_select_rpc_click'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_rpc_change_confirmed'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'cancel_submitted'
                  | 'on_completed_transaction_close_click'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_swap_created_close_clicked'
                  | 'on_swaps_io_swap_request_created'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_sender_selected'
          }
      >
    | { type: 'close' }

export const TopUpFlow = ({
    initialAmountToTopup,
    supportedTopupCurrencies,
    gnosisPayAccountOnboardedState,
    customCurrencies,
    currencyHiddenMap,
    currencyPinMap,
    onMsg,
    cardBalance,
    contractsMap,
    sender,
    gasCurrencyPresetMap,
    feePresetMap,
    networkMap,
    networkRPCMap,
    keyStoreMap,
    accountsMap,
    sessionPassword,
    portfolioMap,
    portfolio,
    cardConfig,
    installationId,
    defaultCurrencyConfig,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Fork
                initialAmountToTopup={initialAmountToTopup}
                defaultCurrencyConfig={defaultCurrencyConfig}
                contractsMap={contractsMap}
                cardBalance={cardBalance}
                supportedTopupCurrencies={supportedTopupCurrencies}
                keyStoreMap={keyStoreMap}
                currencyHiddenMap={currencyHiddenMap}
                networkRPCMap={networkRPCMap}
                currencyPinMap={currencyPinMap}
                networkMap={networkMap}
                portfolio={portfolio}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_transaction_cancelled_successfully_close_clicked':
                        case 'cancel_submitted':
                        case 'on_completed_transaction_close_click':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'on_swap_cancelled_close_clicked':
                        case 'on_swap_success_clicked':
                        case 'on_swap_created_close_clicked':
                        case 'on_swaps_io_swap_request_created':
                            onMsg(msg)
                            break

                        case 'on_from_account_click':
                            setModal({ type: 'select_sender' })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                sender={sender}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                installationId={installationId}
                accountsMap={accountsMap}
                feePresetMap={feePresetMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                sessionPassword={sessionPassword}
            />
            <Modal
                state={modal}
                selectedAccount={sender}
                defaultCurrencyConfig={defaultCurrencyConfig}
                accountsMap={accountsMap}
                keyStoreMap={keyStoreMap}
                portfolioMap={portfolioMap}
                currencyHiddenMap={currencyHiddenMap}
                cardConfig={cardConfig}
                installationId={installationId}
                gnosisPayAccountOnboardedState={gnosisPayAccountOnboardedState}
                sessionPassword={sessionPassword}
                networkRPCMap={networkRPCMap}
                networkMap={networkMap}
                customCurrencyMap={customCurrencies}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_sender_selected':
                            setModal({ type: 'closed' })
                            postUserEvent({
                                type: 'AddCashToCardEvent',
                                action: 'select_wallet',
                                installationId,
                            })

                            onMsg(msg)
                            break
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
