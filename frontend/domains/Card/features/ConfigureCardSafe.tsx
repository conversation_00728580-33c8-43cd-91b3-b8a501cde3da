import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'
import { getTypedMessageDataPreHashOrThrow } from '@zeal/toolkit/Web3/sign'

import {
    CardSafeCurrencyConfigured,
    CardSafeFullyConfigured,
    CardSlientSignKeyStore,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { configureCardSafeModules } from '@zeal/domains/Card/api/configureCardSafeModules'
import { fetchActivationSignaturePayload } from '@zeal/domains/Card/api/fetchActivationSignaturePayload'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { fetchSafeOnChainState } from '@zeal/domains/KeyStore/api/fetchSafeOnChainState'
import { formatSignatureForSafe } from '@zeal/domains/KeyStore/helpers/formatSignatureForSafe'
import { getPrivateKey } from '@zeal/domains/KeyStore/helpers/getPrivateKey'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { EthSignTypedDataV4 } from '@zeal/domains/RPCRequest'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'
import { ConfigureCardSafeLocation } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    cardSafe: CardSafeCurrencyConfigured
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    location: ConfigureCardSafeLocation
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'card_safe_configured'; cardSafe: CardSafeFullyConfigured }

/**
 * GnosisPay activation message requires uniq signing procedure, because of 2 issues in Safe(Card) <-> Safe(Zeal) interaction:
 * 1. By default signatures should not be formatted as
 *    per https://docs.safe.global/advanced/smart-account-signatures#contract-signature-eip-1271,
 *    but in this case it should be, so we reformat signature the way that card safe will detect that it's EIP-1271 signature,
 *    and then pass original signature to Zeal Safe.
 * 2. When card safe is passing signature to Zeal Safe, it's not passing `dataHash`, but `data` instead, although generic signing
 *    is signing the dataHash. So Zeal Safe can't validate the signature if it's signed with generic signing procedure.
 *    Hence we need to sign not the hash of the incoming data (message), which is typically done by just calling simple signTypedData,
 *    but the data itself.
 */
const signActivationMessage = async ({
    signMessageRequest,
    keyStore,
    networkRPCMap,
    sessionPassword,
    signal,
}: {
    signMessageRequest: EthSignTypedDataV4
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    signal: AbortSignal
}): Promise<Hexadecimal> => {
    switch (keyStore.type) {
        case 'private_key_store':
        case 'secret_phrase_key':
            const signature = await signMessage({
                keyStore,
                sessionPassword,
                dApp: null,
                network: CARD_NETWORK,
                request: signMessageRequest,
            })

            return signature as Hexadecimal

        case 'safe_4337':
            // TODO :: @Nicvaniek remove this extra call if we don't see errors in Sentry
            const safeState = await fetchSafeOnChainState({
                keyStore,
                network: CARD_NETWORK,
                networkRPCMap,
                signal,
            })

            switch (safeState) {
                case 'not_deployed':
                case 'no_local_signer_added':
                    throw new ImperativeError('Smart wallet not activated', {
                        network: CARD_NETWORK.hexChainId,
                    })
                case 'deployed':
                    const typedMessage = JSON.parse(
                        signMessageRequest.params[1]
                    )

                    const typedMessageData =
                        getTypedMessageDataPreHashOrThrow(typedMessage)

                    const safeMessage = {
                        domain: {
                            chainId: BigInt(CARD_NETWORK.hexChainId).toString(
                                10
                            ),
                            verifyingContract: keyStore.address,
                        },
                        types: {
                            SafeMessage: [{ name: 'message', type: 'bytes' }],
                            EIP712Domain: [
                                { name: 'chainId', type: 'uint256' },
                                { name: 'verifyingContract', type: 'address' },
                            ],
                        },
                        primaryType: 'SafeMessage',
                        message: { message: typedMessageData },
                    }

                    const pk = await getPrivateKey({
                        keyStore: keyStore.localSignerKeyStore,
                        sessionPassword,
                    })

                    const signature = await Web3.sign.signTypedData(
                        pk,
                        JSON.stringify(safeMessage)
                    )

                    return formatSignatureForSafe(keyStore.address, signature)
                /* istanbul ignore next */
                default:
                    return notReachable(safeState)
            }
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}

const fetch = async ({
    cardReadOnlySigner,
    keyStore,
    networkRPCMap,
    sessionPassword,
    signal,
}: {
    keyStore: CardSlientSignKeyStore
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    cardReadOnlySigner: Web3.address.Address
    signal: AbortSignal
}): Promise<void> => {
    const signMessageRequest = await fetchActivationSignaturePayload({
        sessionPassword,
        keyStore,
        readonlySignerAddress: cardReadOnlySigner,
        signal,
    })

    const signature = await signActivationMessage({
        signMessageRequest,
        keyStore,
        sessionPassword,
        signal,
        networkRPCMap,
    })

    await configureCardSafeModules({
        sessionPassword,
        keyStore,
        readonlySignerAddress: cardReadOnlySigner,
        signal,
        signature,
    })
}

export const ConfigureCardSafe = ({
    cardSafe,
    cardConfig,
    keyStore,
    sessionPassword,
    networkRPCMap,
    location,
    installationId,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            cardReadOnlySigner: cardConfig.readonlySignerAddress,
            keyStore,
            sessionPassword,
            networkRPCMap,
        },
    })

    const onMsgLive = useLiveRef(onMsg)
    const cardSafeLive = useLiveRef(cardSafe)

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'error':
                break
            case 'loaded':
                postUserEvent({
                    type: 'CardSafeConfiguredEvent',
                    installationId,
                    location,
                })
                onMsgLive.current({
                    type: 'card_safe_configured',
                    cardSafe: {
                        ...cardSafeLive.current,
                        type: 'fully_configured',
                    },
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, cardSafeLive, onMsgLive, installationId, location])

    useEffect(() => {
        postUserEvent({
            type: 'CardSafeConfigurationInitiatedEvent',
            installationId,
            location,
        })
    }, [installationId, location])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    title={
                        <FormattedMessage
                            id="virtual-card-order.configure-safe.loading-text"
                            defaultMessage="Creating card"
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        title={
                            <FormattedMessage
                                id="virtual-card-order.configure-safe.loading-text"
                                defaultMessage="Creating card"
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        default:
            return notReachable(loadable)
    }
}
