import { notReachable } from '@zeal/toolkit'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { keys, values } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardConfig } from '@zeal/domains/Card'
import { predictDelayModAddress } from '@zeal/domains/Card/helpers/predictDelayModAddress'
import { predictRolesModAddress } from '@zeal/domains/Card/helpers/predictRolesModAddress'
import { CurrencyId } from '@zeal/domains/Currency'
import { Earn, HistoricalTakerUserCurrencyRateMap } from '@zeal/domains/Earn'
import { fetchTakerTransactionsWithUserCurrencyRates } from '@zeal/domains/Earn/api/fetchTakerTransactionsWithUserCurrencyRates'
import {
    CurrentNetwork,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { Portfolio } from '@zeal/domains/Portfolio'
import { DEFI_PROTOCOL_ADDRESSES } from '@zeal/domains/Portfolio/constants'
import { portfolioToPortfolio2 } from '@zeal/domains/Portfolio/helpers/portfolioToPortfolio2'
import { ParsedLog } from '@zeal/domains/RPCRequest'

import { fetchIndexedTransactions } from './fetchIndexedTransactions'

import { IndexedTransaction } from '..'

export const fetchIndexedTransactionActivities = async ({
    address,
    cardConfig,
    portfolio,
    networkRPCMap,
    networkMap,
    currentNetwork,
    earnHistoricalTakerUserCurrencyRateMap,
    startTime,
    endTime,
    withScam,
    signal,
}: {
    address: Web3.address.Address
    currentNetwork: CurrentNetwork
    cardConfig: CardConfig
    portfolio: Portfolio
    networkRPCMap: NetworkRPCMap
    startTime: Date
    endTime: Date
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    networkMap: NetworkMap
    withScam: boolean
    signal?: AbortSignal
}): Promise<{
    indexedTransactions: IndexedTransaction[]
    currencies: CurrencyId[]
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
}> => {
    switch (currentNetwork.type) {
        case 'all_networks':
            break
        case 'specific_network':
            if (currentNetwork.network.hexChainId !== GNOSIS.hexChainId) {
                return {
                    indexedTransactions: [],
                    currencies: [],
                    earnHistoricalTakerUserCurrencyRateMap,
                }
            }
            break
        /* istanbul ignore next */
        default:
            return notReachable(currentNetwork)
    }
    const { earn, tokens } = portfolioToPortfolio2({ portfolio })
    const scamCurrencySet = new Set(
        tokens
            .filter(
                (token) =>
                    token.scam &&
                    !DEFI_PROTOCOL_ADDRESSES.has(
                        token.balance.currency.address as Web3.address.Address
                    )
            )
            .map((token) => token.balance.currency.id)
    )

    const [accountTransactions, cardTransactions, earnTransactions] =
        await Promise.all([
            fetchIndexedTransactions({
                address,
                networkHexId: GNOSIS.hexChainId,
                afterTimestampMs: endTime.getTime(),
                beforeTimestampMs: startTime.getTime(),
                logsForAddresses: null,
                signal,
            }),
            (async () => {
                switch (cardConfig.type) {
                    case 'card_readonly_signer_address_is_not_selected':
                    case 'card_readonly_signer_address_is_selected':
                        return []
                    case 'card_readonly_signer_address_is_selected_fully_onboarded':
                        return cardConfig.readonlySignerAddress === address
                            ? fetchIndexedCardTransactions({
                                  cardSafeAddress:
                                      cardConfig.lastSeenSafeAddress,
                                  endTime,
                                  startTime,
                                  signal,
                              })
                            : []

                    /* istanbul ignore next */
                    default:
                        return notReachable(cardConfig)
                }
            })(),
            fetchIndexedEarnTransactions({
                earn,
                startTime,
                endTime,
                networkMap,
                networkRPCMap,
                earnHistoricalTakerUserCurrencyRateMap,
                signal,
            }),
        ])

    const mergedTransactions = values(
        [
            ...accountTransactions,
            ...cardTransactions,
            ...earnTransactions.takersTransactions,
        ].reduce(
            (acc, tx) => {
                acc[tx.hash] = {
                    ...tx,
                    logs: mergeLogs(tx.logs, acc[tx.hash]?.logs ?? []),
                }
                return acc
            },
            {} as Record<Hexadecimal, IndexedTransaction>
        )
    )

    const filteredTransactions = mergedTransactions.filter((tx) => {
        const hasScamTransfer = tx.logs.some((log) => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                    return false
                case 'erc20_transfer':
                    return scamCurrencySet.has(log.currencyId)
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        })
        return withScam ? hasScamTransfer : !hasScamTransfer
    })

    const currencies = keys(
        filteredTransactions.reduce(
            (acc, tx) => ({
                ...acc,
                ...tx.logs.reduce(
                    (acc, log) => {
                        switch (log.type) {
                            case 'approval':
                            case 'erc20_transfer':
                                return {
                                    ...acc,
                                    [log.currencyId]: true as const,
                                }

                            case 'added_owner':
                            case 'account_deployed':
                            case 'threshold_updated':
                            case 'set_allowance':
                            case 'enable_module':
                            case 'disable_module':
                            case 'safe_module_transaction':
                            case 'safe_received':
                            case 'user_operation_event':
                            case 'unknown':
                                return acc
                            /* istanbul ignore next */
                            default:
                                return notReachable(log)
                        }
                    },
                    {} as Record<CurrencyId, true>
                ),
            }),
            {} as Record<CurrencyId, true>
        )
    )

    return {
        indexedTransactions: filteredTransactions,
        currencies,
        earnHistoricalTakerUserCurrencyRateMap:
            earnTransactions.historicalTakerUserCurrencyRateMap,
    }
}

export const fetchIndexedScamTransactionActivities = async ({
    address,
    portfolio,
    startTime,
    endTime,
    signal,
}: {
    address: Web3.address.Address
    portfolio: Portfolio
    networkRPCMap: NetworkRPCMap
    startTime: Date
    endTime: Date
    signal?: AbortSignal
}): Promise<{
    indexedTransactions: IndexedTransaction[]
    currencies: CurrencyId[]
}> => {
    const { tokens } = portfolioToPortfolio2({ portfolio })

    const scamCurrencySet = new Set(
        tokens
            .filter(
                (token) =>
                    token.scam &&
                    !DEFI_PROTOCOL_ADDRESSES.has(
                        token.balance.currency.address as Web3.address.Address
                    )
            )
            .map((token) => token.balance.currency.id)
    )

    const accountTransactions = await fetchIndexedTransactions({
        address,
        networkHexId: GNOSIS.hexChainId,
        afterTimestampMs: endTime.getTime(),
        beforeTimestampMs: startTime.getTime(),
        logsForAddresses: null,
        signal,
    })

    const mergedTransactions = values(
        accountTransactions.reduce(
            (acc, tx) => {
                acc[tx.hash] = {
                    ...tx,
                    logs: mergeLogs(tx.logs, acc[tx.hash]?.logs ?? []),
                }
                return acc
            },
            {} as Record<Hexadecimal, IndexedTransaction>
        )
    )

    const filteredTransactions = mergedTransactions.filter((tx) => {
        const hasScamTransfer = tx.logs.some((log) => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                    return false
                case 'erc20_transfer':
                    return scamCurrencySet.has(log.currencyId)
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        })
        return hasScamTransfer
    })

    const currencies = keys(
        filteredTransactions.reduce(
            (acc, tx) => ({
                ...acc,
                ...tx.logs.reduce(
                    (acc, log) => {
                        switch (log.type) {
                            case 'approval':
                            case 'erc20_transfer':
                                return {
                                    ...acc,
                                    [log.currencyId]: true as const,
                                }

                            case 'added_owner':
                            case 'account_deployed':
                            case 'threshold_updated':
                            case 'set_allowance':
                            case 'enable_module':
                            case 'disable_module':
                            case 'safe_module_transaction':
                            case 'safe_received':
                            case 'user_operation_event':
                            case 'unknown':
                                return acc
                            /* istanbul ignore next */
                            default:
                                return notReachable(log)
                        }
                    },
                    {} as Record<CurrencyId, true>
                ),
            }),
            {} as Record<CurrencyId, true>
        )
    )

    return {
        indexedTransactions: filteredTransactions,
        currencies,
    }
}

const fetchIndexedEarnTransactions = async ({
    earn,
    startTime,
    endTime,
    networkMap,
    networkRPCMap,
    earnHistoricalTakerUserCurrencyRateMap,
    signal,
}: {
    earn: Earn
    startTime: Date
    endTime: Date
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    signal?: AbortSignal
}): Promise<{
    takersTransactions: IndexedTransaction[]
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
}> => {
    const earnTransactions = await Promise.all(
        earn.takers.map(async (taker) => {
            switch (taker.state) {
                case 'not_deployed':
                    return []
                case 'deployed':
                    return fetchTakerTransactionsWithUserCurrencyRates({
                        taker,
                        afterTimestampMs: endTime.getTime(),
                        beforeTimestampMs: startTime.getTime(),
                        networkMap,
                        networkRPCMap,
                        cache: earnHistoricalTakerUserCurrencyRateMap,
                        signal,
                    }).then((res) => ({
                        ...res,
                        takerType: taker.type,
                    }))
                /* istanbul ignore next */
                default:
                    return notReachable(taker)
            }
        })
    )

    const { takersTransactions, historicalTakerUserCurrencyRateMap } =
        earnTransactions.flat().reduce(
            (
                acc,
                {
                    indexedTakerTransactions,
                    blockUserCurrencyRateMap,
                    takerType,
                }
            ) => ({
                takersTransactions: [
                    ...acc.takersTransactions,
                    ...indexedTakerTransactions,
                ],
                historicalTakerUserCurrencyRateMap: {
                    ...acc.historicalTakerUserCurrencyRateMap,
                    [takerType]: blockUserCurrencyRateMap,
                },
            }),
            {
                takersTransactions: [] as IndexedTransaction[],
                historicalTakerUserCurrencyRateMap:
                    {} as HistoricalTakerUserCurrencyRateMap,
            }
        )

    return {
        takersTransactions,
        historicalTakerUserCurrencyRateMap,
    }
}

const fetchIndexedCardTransactions = async ({
    cardSafeAddress,
    startTime,
    endTime,
    signal,
}: {
    cardSafeAddress: Web3.address.Address
    startTime: Date
    endTime: Date
    signal?: AbortSignal
}): Promise<IndexedTransaction[]> => {
    const rolesModule = predictRolesModAddress(cardSafeAddress)
    const delayModule = predictDelayModAddress(cardSafeAddress)

    const transactions = await Promise.all([
        fetchIndexedTransactions({
            address: cardSafeAddress,
            networkHexId: GNOSIS.hexChainId,
            afterTimestampMs: endTime.getTime(),
            beforeTimestampMs: startTime.getTime(),
            logsForAddresses: null,
            signal,
        }),
        fetchIndexedTransactions({
            address: rolesModule,
            networkHexId: GNOSIS.hexChainId,
            afterTimestampMs: endTime.getTime(),
            beforeTimestampMs: startTime.getTime(),
            logsForAddresses: null,
            signal,
        }),
        fetchIndexedTransactions({
            address: delayModule,
            networkHexId: GNOSIS.hexChainId,
            afterTimestampMs: endTime.getTime(),
            beforeTimestampMs: startTime.getTime(),
            logsForAddresses: null,
            signal,
        }),
    ])

    return values(
        transactions.flat().reduce(
            (acc, tx) => {
                acc[tx.hash] = {
                    ...tx,
                    logs: mergeLogs(tx.logs, acc[tx.hash]?.logs ?? []),
                }
                return acc
            },
            {} as Record<Hexadecimal, IndexedTransaction>
        )
    )
}

const mergeLogs = (logsA: ParsedLog[], logsB: ParsedLog[]): ParsedLog[] =>
    values(
        [...logsA, ...logsB].reduce(
            (acc, log) => {
                const key = (() => {
                    switch (log.type) {
                        case 'erc20_transfer':
                            return `${log.type}-${log.currencyId}-${log.from}-${log.to}-${log.amount}-${log.logIndex}`
                        case 'added_owner':
                            return `${log.type}-${log.owner}`
                        case 'approval':
                            return `${log.type}-${log.currencyId}-${log.amount}`
                        case 'account_deployed':
                            return `${log.type}-${log.userOperationHash}`
                        case 'threshold_updated':
                            return `${log.type}-${log.threshold}`
                        case 'set_allowance':
                            return `${log.type}-${log.allowanceKey}-${log.timestamp}-${log.balance}-${log.period}`
                        case 'enable_module':
                            return `${log.type}-${log.module}`
                        case 'disable_module':
                            return `${log.type}-${log.module}`
                        case 'safe_module_transaction':
                            return `${log.type}-${log.module}-${log.to}-${log.from}-${log.value}-${log.operation}`
                        case 'safe_received':
                            return `${log.type}-${log.to}-${log.from}-${log.value}`
                        case 'unknown':
                            return `${log.type}`
                        case 'user_operation_event':
                            return `${log.type}-${log.nonce}-${log.success}`
                        /* istanbul ignore next */
                        default:
                            return notReachable(log)
                    }
                })()

                return {
                    ...acc,
                    [key]: log,
                }
            },
            {} as Record<string, ParsedLog>
        )
    )
