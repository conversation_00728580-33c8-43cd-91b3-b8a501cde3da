import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoCurrency, currencyId } from '@zeal/domains/Currency'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { getNativeTokenAddress } from '@zeal/domains/Network/helpers/getNativeTokenAddress'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { GasInfo } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'

const SCALAR_PRECISION = 1000000

const getFee = ({
    currency,
    gasInfo,
}: {
    gasInfo: GasInfo
    currency: CryptoCurrency
}): CryptoMoney => {
    switch (gasInfo.type) {
        case 'generic':
            return {
                amount: gasInfo.gasUsed * gasInfo.effectiveGasPrice,
                currency,
            }

        case 'l2_rollup':
            const l1FeeWithoutScalar = gasInfo.l1GasUsed * gasInfo.l1GasPrice
            const l1Fee =
                (l1FeeWithoutScalar *
                    BigInt(gasInfo.l1FeeScalar * SCALAR_PRECISION)) /
                BigInt(SCALAR_PRECISION)

            const l2Fee = gasInfo.gasUsed * gasInfo.l2GasPrice

            return {
                amount: l1Fee + l2Fee,
                currency,
            }

        /* istanbul ignore next */
        default:
            return notReachable(gasInfo)
    }
}

export const fetchFinalFee = async ({
    network,
    gasInfo,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    signal,
}: {
    gasInfo: GasInfo
    network: Network
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    fee: CryptoMoney
    priceInDefaultCurrency: FiatMoney | null
}> => {
    switch (network.type) {
        case 'predefined': {
            const nativeTokenAddress = getNativeTokenAddress(
                network
            ) as Web3.address.Address

            const nativeCurrencyId = currencyId({
                address: nativeTokenAddress,
                network: network.hexChainId,
            })
            const nativeCurrency =
                (
                    await fetchCryptoCurrency2({
                        currencies: [nativeCurrencyId],
                        networkRPCMap,
                    })
                )[nativeCurrencyId] || null

            if (!nativeCurrency) {
                throw new ImperativeError(
                    `Failed to fetch native currency for rate`,
                    { networkHexId: network.hexChainId }
                )
            }

            const rate = await fetchRate({
                cryptoCurrency: nativeCurrency,
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                signal,
            })

            const fee = getFee({ currency: nativeCurrency, gasInfo })

            if (!rate) {
                return {
                    fee,
                    priceInDefaultCurrency: null,
                }
            }

            const priceInDefaultCurrency = applyRate2({ baseAmount: fee, rate })

            return {
                priceInDefaultCurrency,
                fee,
            }
        }
        case 'custom':
        case 'testnet': {
            const currency = network.nativeCurrency

            return {
                fee: getFee({
                    gasInfo,
                    currency,
                }),
                priceInDefaultCurrency: null,
            }
        }

        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
