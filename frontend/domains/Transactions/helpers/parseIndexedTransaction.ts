import {
    nullableParse as parseNullableHex,
    parse as parseHex,
    parserHexAsNumber,
    toBigInt,
} from '@zeal/toolkit/Hexadecimal'
import {
    array,
    arrayOf,
    combine,
    nullableOf,
    number,
    object,
    Result,
    shape,
    string,
} from '@zeal/toolkit/Result'
import { parse as parseAddress } from '@zeal/toolkit/Web3/address'

import { EARN_NETWORK } from '@zeal/domains/Earn/constants'

import { parseLog } from './parseLog'

import { IndexedTransaction } from '..'

export const parseIndexedTransaction = (
    input: unknown
): Result<unknown, IndexedTransaction> =>
    object(input).andThen((obj) =>
        shape({
            from: string(obj.from).andThen(parseAddress),
            to: nullableOf(obj.to, (val) => string(val).andThen(parseAddress)),
            blockNumber: parseHex(obj.blockNumber).map(toBigInt),
            timestamp: number(obj.timestamp).map((ts) => ts * 1000), // We have a timestamp in seconds
            hash: parseHex(obj.hash),
            value: parseHex(obj.value),
            logs: array(obj.logs).andThen((arr) =>
                combine(
                    arr.map((logInput) =>
                        object(logInput)
                            .andThen((logObj) =>
                                shape({
                                    address: string(logObj.address).andThen(
                                        parseAddress
                                    ),
                                    topics: arrayOf(logObj.topics, (t) =>
                                        parseHex(t)
                                    ),
                                    data: parseNullableHex(logObj.data),
                                    logIndex: parserHexAsNumber(
                                        logObj.logIndex
                                    ),
                                })
                            )
                            .andThen((dto) => parseLog(dto, EARN_NETWORK))
                    )
                )
            ),
        })
    )
