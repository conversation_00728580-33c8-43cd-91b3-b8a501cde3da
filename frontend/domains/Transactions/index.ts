import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardTransaction } from '@zeal/domains/Card'
import { SwapsIOSwapRequest } from '@zeal/domains/Currency/domains/SwapsIO'
import { DeployedTaker, TakerType } from '@zeal/domains/Earn'
import { InternalTransactionActionSource } from '@zeal/domains/Main'
import { CryptoMoney, FiatMoney, Money2 } from '@zeal/domains/Money'
import { CurrentNetwork, NetworkHexId } from '@zeal/domains/Network'
import { Nft, NftCollectionInfo } from '@zeal/domains/NFTCollection'
import { ParsedLog } from '@zeal/domains/RPCRequest'
import { SmartContract } from '@zeal/domains/SmartContract'
import {
    SubmitedTransactionIncludedInBlock,
    SubmitedTransactionQueued,
} from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'
import { SubmittedUserOperation } from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'

export type TransactionActivityFilter =
    | {
          type: 'card_filter'
      }
    | {
          type: 'network_filter'
          network: CurrentNetwork
      }

export type IndexedTransactionLogDTO = {
    address: Web3.address.Address
    topics: Hexadecimal[]
    data: Hexadecimal | null
    logIndex: number
}

export type IndexedTransaction = {
    from: Web3.address.Address
    to: Web3.address.Address | null
    blockNumber: bigint
    timestamp: number
    hash: Hexadecimal
    value: Hexadecimal

    logs: ParsedLog[]
}

export type RpcTransaction = {
    blockNumber: bigint
    nonce: bigint
}

export type TransactionActivity = {
    continueFromTimestamp: number | null
    transactions: ActivityTransaction[]
}

export type ActivityTransaction =
    | InboundP2PActivityTransaction
    | OutboundP2PActivityTransaction
    | OutboundP2PNftActivityTransaction
    | SelfP2PActivityTransaction
    | SingleNftApprovalActivityTransaction
    | SingleNftApprovalRevokeActivityTransaction
    | NftCollectionApprovalActivityTransaction
    | NftCollectionApprovalRevokeActivityTransaction
    | Erc20ApprovalActivityTransaction
    | Erc20ApprovalRevokeActivityTransaction
    | PartialTokenApprovalActivityTransaction
    | UnknownActivityTransaction
    | FailedActivityTransaction
    | SmartWalletDeploymentActivityTransaction

export type InboundP2PActivityTransaction = {
    type: 'InboundP2PActivityTransaction'
    tokens: TransactionToken[]
    nfts: TransactionNft[]
    sender: Web3.address.Address
} & Omit<Common, 'paidFee'>

export type OutboundP2PActivityTransaction = {
    type: 'OutboundP2PActivityTransaction'
    token: TransactionToken
    receiver: Web3.address.Address
} & Common

export type OutboundP2PNftActivityTransaction = {
    type: 'OutboundP2PNftActivityTransaction'
    nft: TransactionNft
    receiver: Web3.address.Address
} & Common

export type SelfP2PActivityTransaction = {
    type: 'SelfP2PActivityTransaction'
} & Common

export type SingleNftApprovalActivityTransaction = {
    type: 'SingleNftApprovalActivityTransaction'
    nft: Nft
    approveTo: SmartContract
} & Common

export type SingleNftApprovalRevokeActivityTransaction = {
    type: 'SingleNftApprovalRevokeActivityTransaction'
    nft: Nft
    revokeFrom: SmartContract
} & Common

export type NftCollectionApprovalActivityTransaction = {
    type: 'NftCollectionApprovalActivityTransaction'
    nftCollectionInfo: NftCollectionInfo
    approveTo: SmartContract
} & Common

export type NftCollectionApprovalRevokeActivityTransaction = {
    type: 'NftCollectionApprovalRevokeActivityTransaction'
    nftCollectionInfo: NftCollectionInfo
    revokeFrom: SmartContract
} & Common

export type Erc20ApprovalActivityTransaction = {
    type: 'Erc20ApprovalActivityTransaction'
    approveTo: SmartContract
    allowance: ApprovalAmount
} & Common

export type Erc20ApprovalRevokeActivityTransaction = {
    type: 'Erc20ApprovalRevokeActivityTransaction'
    revokeFrom: SmartContract
    allowance: ApprovalAmount
} & Common

export type PartialTokenApprovalActivityTransaction = {
    type: 'PartialTokenApprovalActivityTransaction'
    approveTo: SmartContract
} & Common

export type UnknownActivityTransaction = {
    type: 'UnknownActivityTransaction'
    method: string
    smartContract: SmartContract
    tokens: TransactionToken[]
    nfts: TransactionNft[]
} & Common

export type SmartWalletDeploymentActivityTransaction = {
    type: 'SmartWalletDeploymentActivityTransaction'
    smartWalletAddress: Web3.address.Address
} & Common

export type FailedActivityTransaction = {
    type: 'FailedActivityTransaction'
    method: string
    smartContract: SmartContract
} & Common

export type Common = {
    networkHexId: NetworkHexId
    hash: string
    timestamp: Date
    paidFee: PaidFee | null
}

export type PaidFee = {
    priceInNativeCurrency: CryptoMoney
    priceInDefaultCurrency: FiatMoney | null
}

export type TransactionToken = {
    type: 'transaction_token'
    direction: 'Send' | 'Receive'
    amount: CryptoMoney | FiatMoney
    priceInDefaultCurrency: FiatMoney | null
}

export type TransactionNft = {
    type: 'transaction_nft'
    nft: Nft
    amount: bigint
    direction: 'Send' | 'Receive'
}

export type ApprovalAmount =
    | {
          type: 'Limited'
          amount: CryptoMoney
      }
    | {
          type: 'Unlimited'
          amount: CryptoMoney
      }

export type TransactionActivityV2 =
    | RegularTransactionActivity
    | CardTransactionActivity
    | SwapsIOTransactionActivityCompleted
    | SwapsIOTransactionActivityFailed

export type CardTransactionActivity = {
    type: 'card_transaction'
    cardTransaction: CardTransaction
    timestamp: Date
}

export type SwapsIOTransactionActivity =
    | SwapsIOIntoCardTransactionActivity
    | SwapsIOBuyTransactionActivity
    | SwapsIOIntoEarnTransactionActivity

export type SwapsIOTransactionActivityCommon =
    | {
          state: 'completed'
          swapsIOSwapRequest: Extract<
              SwapsIOSwapRequest,
              { state: 'completed_sent' } | { state: 'completed_liq_sent' }
          >
          timestamp: Date
      }
    | {
          state: 'failed'
          swapsIOSwapRequest: Extract<
              SwapsIOSwapRequest,
              {
                  state: 'cancelled_no_slash' | 'cancelled_slashed'
              }
          >
          timestamp: Date
      }
    | {
          state: 'pending'
          swapsIOSwapRequest: Extract<
              SwapsIOSwapRequest,
              {
                  state:
                      | 'cancelled_awaiting_slash'
                      | 'awaiting_liq_send'
                      | 'awaiting_signature'
                      | 'awaiting_receive'
                      | 'awaiting_send'
              }
          >
          createdAt: Date
      }

export type SwapsIOTransactionActivityCompleted =
    | Extract<SwapsIOIntoCardTransactionActivity, { state: 'completed' }>
    | Extract<SwapsIOIntoEarnTransactionActivity, { state: 'completed' }>
    | Extract<SwapsIOBuyTransactionActivity, { state: 'completed' }>

export type SwapsIOTransactionActivityFailed =
    | Extract<SwapsIOIntoCardTransactionActivity, { state: 'failed' }>
    | Extract<SwapsIOIntoEarnTransactionActivity, { state: 'failed' }>
    | Extract<SwapsIOBuyTransactionActivity, { state: 'failed' }>

export type SwapsIOTransactionActivityPending =
    | Extract<SwapsIOIntoCardTransactionActivity, { state: 'pending' }>
    | Extract<SwapsIOIntoEarnTransactionActivity, { state: 'pending' }>
    | Extract<SwapsIOBuyTransactionActivity, { state: 'pending' }>

export type SwapsIOIntoCardTransactionActivity = {
    type: 'swaps_io_into_card'
    toAmount: FiatMoney
} & SwapsIOTransactionActivityCommon

export type SwapsIOIntoEarnTransactionActivity = {
    type: 'swaps_io_into_earn'
    taker: DeployedTaker
    toAmount: CryptoMoney
    toAmountInUserCurrency: Money2 | null // FIXME :: @mike - this should be nullable only for failed state
} & SwapsIOTransactionActivityCommon

export type SwapsIOBuyTransactionActivity = {
    type: 'swaps_io_buy'
} & SwapsIOTransactionActivityCommon

export type PendingSendTransactionActivity = {
    type: 'pending_send'
    receiver: Web3.address.Address
    amount: CryptoMoney
    amountInDefaultCurrency: FiatMoney | null

    networkHexId: NetworkHexId
    actionSource: InternalTransactionActionSource
    submittedUserOperation: Extract<
        SubmittedUserOperation,
        { state: 'bundled' | 'pending' }
    >
}

// TODO @resetko-zeal kill that one and just use areward pending, rename to "reward_pending". https://linear.app/zeal/issue/ZEAL-4076
export type PendingBRewardClaimTransactionActivity = {
    type: 'pending_breward_claim'
    taker: DeployedTaker
    amount: CryptoMoney
    amountInUserCurrency: Money2
    amountInDefaultCurrency: FiatMoney | null
    submittedTransaction:
        | SubmitedTransactionQueued
        | SubmitedTransactionIncludedInBlock
}

export type PendingARewardClaimTransactionActivity = {
    type: 'pending_areward_claim'
    amountInTakerInvestmentCurrency: CryptoMoney
    submittedTransaction:
        | SubmitedTransactionQueued
        | SubmitedTransactionIncludedInBlock
}

export type RegularTransactionActivity =
    // === ARBITRARY ===
    | ArbitrarySmartContractTransactionActivity

    // === BANK ===
    | DepositFromBankTransactionActivity
    | SendToBankTransactionActivity

    // === CARD ===
    | DepositIntoCardTransactionActivity
    | WithdrawFromCardTransactionActivity

    // === CARD SETTINGS ===
    | CardOwnersUpdatedTransactionActivity
    | CardSpendLimitUpdatedTransactionActivity

    // === CASHBACK ===
    | CashBackDepositTransaction
    | CashBackWithdrawTransaction
    | CashBackRewardTransaction

    // === DEPLOYED SMART WALLET ===
    | DeployedSmartWalletGnosisTransactionActivity
    | RecoveredSmartWalletGnosisTransactionActivity

    // === EARN ===
    | DepositIntoEarnTransactionActivity
    | WithdrawFromEarnTransactionActivity
    | BRewardClaimTransactionActivity

    // === INTERNAL BUY / SWAP / BRIDGE ===
    | SwapTransactionActivity
    | IncomingBridgeTransactionActivity
    | OutgoingBridgeTransactionActivity

    // === RECHARGE ===
    | RechargeDisabledTransactionActivity
    | RechargeTargetSetTransactionActivity

    // === SEND / RECEIVE ===
    | ReceiveTransactionActivity
    | SendTransactionActivity

    // === TOKEN APPROVAL ===
    | TokenApprovalTransactionActivity
    | TokenApprovalRevokedTransactionActivity

    // === FAILED ===
    | FailedTransactionActivity

// === CARD ===
export type DepositIntoCardTransactionActivity = {
    type: 'deposit_into_card'
} & (
    | { from: 'bank'; toAmount: CryptoMoney; fromAmount: FiatMoney }
    | {
          from: 'earn'
          taker: DeployedTaker // TODO @resetko-zeal change to TakerType
          toAmount: CryptoMoney
          fromAmountInUserCurrency: Money2
      }
    | { from: 'wallet'; toAmount: CryptoMoney; fromAmount: CryptoMoney }
) &
    Common

export type WithdrawFromCardTransactionActivity = {
    type: 'withdraw_from_card'
    token: CryptoMoney
} & Common

export type TokenApprovalTransactionActivity = {
    type: 'token_approval'
    approveTo: SmartContract
    limit: ApprovalAmount
} & Common

export type TokenApprovalRevokedTransactionActivity = {
    type: 'token_approval_revoked'
    approveTo: SmartContract
    limit: ApprovalAmount
} & Common

// === SEND / RECEIVE ===
export type SendTransactionActivity = {
    type: 'send'
    receiver: Web3.address.Address
    amount: CryptoMoney
    amountInDefaultCurrency: FiatMoney | null
} & Common

export type ReceiveTransactionActivity = {
    type: 'receive'
    sender: Web3.address.Address
    amount: CryptoMoney
    amountInDefaultCurrency: FiatMoney | null
} & Common

// === EARN ===

// TODO :: @Nicvaniek rename this since it is not specific to B_Rewards https://linear.app/zeal/issue/ZEAL-4076
export type BRewardClaimTransactionActivity = {
    type: 'breward_claim'
    takerType: TakerType
    toAmount: CryptoMoney
    toAmountInUserCurrency: Money2
    fromAmount: null
} & Common

export type DepositIntoEarnTransactionActivity = {
    type: 'deposit_into_earn'
    takerType: TakerType
    fromAmount: CryptoMoney | null
    toAmount: CryptoMoney
    toAmountInUserCurrency: Money2
} & Common

export type WithdrawFromEarnTransactionActivity = {
    type: 'withdraw_from_earn'
    taker: DeployedTaker
    toAmount: CryptoMoney
    fromAmountInUserCurrency: Money2
} & Common

// === BANK DEPOSIT / SEND ===
export type DepositFromBankTransactionActivity = {
    type: 'deposit_from_bank'
    fromAmount: FiatMoney
    toAmount: CryptoMoney
} & Common

export type SendToBankTransactionActivity = {
    type: 'send_to_bank'
    fromAmount: CryptoMoney
    toAmount: FiatMoney
} & Common

// === DEPLOYED SMART WALLET ===
export type DeployedSmartWalletGnosisTransactionActivity = {
    type: 'deployed_smart_wallet_gnosis'
} & Common

export type RecoveredSmartWalletGnosisTransactionActivity = {
    type: 'recovered_smart_wallet_gnosis'
} & Common

// === ARBITRARY TXNS ===
export type ArbitrarySmartContractTransactionActivity = {
    type: 'arbitrary_smart_contract_interaction'
    functionSignature: string | null
    smartContract: SmartContract | null
    transactionType: ArbitrarySmartContractTransactionType
} & Common

// === SWAP ===
export type SwapTransactionActivity = {
    type: 'swap'
    fromToken: CryptoMoney
    toToken: CryptoMoney
} & Common

export type IncomingBridgeTransactionActivity = {
    type: 'incoming_bridge'
    fromToken: CryptoMoney
} & Common

export type OutgoingBridgeTransactionActivity = {
    type: 'outgoing_bridge'
    toToken: CryptoMoney
} & Common

// === RECHARGE ===
export type RechargeTargetSetTransactionActivity = {
    type: 'recharge_target_set'
    target: CryptoMoney
} & Common

export type RechargeDisabledTransactionActivity = {
    type: 'recharge_disabled'
} & Common

// === CARD SETTINGS ===
export type CardOwnersUpdatedTransactionActivity = {
    type: 'card_owners_updated'
} & Common

export type CardSpendLimitUpdatedTransactionActivity = {
    type: 'card_spend_limit_updated'
    spendLimit: CryptoMoney
} & Common

export type CashBackDepositTransaction = {
    type: 'cashback_deposit'
    fromAddress: Web3.address.Address
    amount: CryptoMoney
} & Common

export type CashBackWithdrawTransaction = {
    type: 'cashback_withdraw'
    toAddress: Web3.address.Address
    amount: CryptoMoney
} & Common

export type CashBackRewardTransaction = {
    type: 'cashback_reward'
    amount: CryptoMoney
} & Common

export type ArbitrarySmartContractTransactionType =
    | {
          type: 'exactlyOneInOneOut'
          incoming: CryptoMoney
          outgoing: CryptoMoney
      }
    | {
          type: 'multipleInAndOut'
          incoming: CryptoMoney[]
          outgoing: CryptoMoney[]
      }
    | {
          type: 'onlyIn'
          incoming: CryptoMoney[]
          priceInDefaultCurrency: FiatMoney | null
      }
    | {
          type: 'onlyOut'
          outgoing: CryptoMoney[]
          priceInDefaultCurrency: FiatMoney | null
      }
    | {
          type: 'noInNoOut'
      }

export type FailedTransactionActivity = {
    type: 'failed_transaction'
    functionSignature: string | null
    smartContract: SmartContract | null
} & Common

export type TransactionActivitiesCache = {
    transactionActivities: TransactionActivityV2[]
    pendingSwapsIOTransactionActivities: SwapsIOTransactionActivityPending[]
    pendingSendTransactionActivities: PendingSendTransactionActivity[]
    pendingBRewardClaimTransactionActivity: PendingBRewardClaimTransactionActivity | null
    pendingARewardClaimTransactionActivity: PendingARewardClaimTransactionActivity | null

    // TODO @kate or @mike change type to transactionActivities and pendingActivities
}

declare const TransactionActivitiesCacheMapIndexSymbol: unique symbol

export type TransactionActivitiesCacheMap = Record<
    Web3.address.Address & {
        __TransactionActivitiesCacheMapIndexSymbol: typeof TransactionActivitiesCacheMapIndexSymbol
    },
    TransactionActivitiesCache | null
>
