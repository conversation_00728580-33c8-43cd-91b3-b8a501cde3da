import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { GroupHeader, Section } from '@zeal/uikit/Group'
import { Text } from '@zeal/uikit/Text'

import { CryptoMoneyListItem } from '@zeal/domains/Money/components/CryptoMoneyListItem'
import { NetworkMap } from '@zeal/domains/Network'

import { SwapsIONativeTokenSwap } from '../SimulatedTransaction'

type Props = {
    transaction: SwapsIONativeTokenSwap
    networkMap: NetworkMap
}

export const SwapsIONativeTokenSwapView = ({
    networkMap,
    transaction,
}: Props) => (
    <Column spacing={16}>
        <Section>
            <GroupHeader
                left={({ color, textVariant, textWeight }) => (
                    <Text
                        color={color}
                        variant={textVariant}
                        weight={textWeight}
                    >
                        <FormattedMessage
                            id="send.titile"
                            defaultMessage="Send"
                        />
                    </Text>
                )}
                right={null}
            />

            <CryptoMoneyListItem
                networkMap={networkMap}
                size="large"
                balance={transaction.swapsIOQuote.from}
                priceInDefaultCurrency={
                    transaction.swapsIOQuote.fromInDefaultCurrency
                }
                sign="-"
            />
        </Section>

        <Section>
            <GroupHeader
                left={({ color, textVariant, textWeight }) => (
                    <Text
                        color={color}
                        variant={textVariant}
                        weight={textWeight}
                    >
                        <FormattedMessage id="to.titile" defaultMessage="To" />
                    </Text>
                )}
                right={null}
            />
            <CryptoMoneyListItem
                networkMap={networkMap}
                size="large"
                balance={transaction.swapsIOQuote.to}
                priceInDefaultCurrency={
                    transaction.swapsIOQuote.toInDefaultCurrency
                }
                sign="+"
            />
        </Section>
    </Column>
)
