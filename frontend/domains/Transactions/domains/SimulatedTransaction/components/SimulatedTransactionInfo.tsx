import { notReachable } from '@zeal/toolkit'

import { AccountsMap } from '@zeal/domains/Account'
import { OffRampTransactionView } from '@zeal/domains/Currency/domains/BankTransfer/components/OffRampTransactionView'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { TakerSubtextListItemNoBalance } from '@zeal/domains/Earn/components/TakerSubtextListItemNoBalance'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap } from '@zeal/domains/Network'
import { NftCollectionListItem } from '@zeal/domains/NFTCollection/components/NftCollectionListItem'
import { NftListItem } from '@zeal/domains/NFTCollection/components/NftListItem'
import { SimulateTransactionResponse } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { CardCashbackDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardCashbackDepositTrxView'
import { CardTopUpFromEarnTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpFromEarnTrxView'
import { CardTopUpTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/CardTopUpTrxView'
import { EarnDepositTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnDepositTrxView'
import { EarnWithdrawalTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/EarnWithdrawalTrxView'
import { SmartWalletActivationTrxView } from '@zeal/domains/Transactions/domains/SimulatedTransaction/components/SmartWalletActivationTrxView'

import { Approve } from './Approve'
import { BridgeTrxView } from './BridgeTrx'
import { EarnRechargeConfiguredTrxView } from './EarnRechargeConfiguredTrxView'
import { Failed } from './Failed'
import { P2PTransactionView } from './P2PTransactionView'
import { SwapsIONativeTokenSwapView } from './SwapsIONativeTokenSwap'
import { Unknown } from './Unknown'

type Props = {
    simulation: SimulateTransactionResponse
    installationId: string
    dApp: DAppSiteInfo | null
    accounts: AccountsMap
    keystores: KeyStoreMap
    networkMap: NetworkMap
}

export const SimulatedTransactionInfo = ({
    simulation,
    accounts,
    keystores,
    installationId,
    networkMap,
    dApp,
}: Props) => {
    const { transaction, checks, currencies: knownCurrencies } = simulation

    switch (transaction.type) {
        case 'earn_recharge_configured':
        case 'earn_recharge_updated':
            return <EarnRechargeConfiguredTrxView transaction={transaction} />
        case 'earn_recharge_disabled':
            return null

        case 'earn_deposit_with_swap':
        case 'earn_deposit_direct_send':
            return <EarnDepositTrxView transaction={transaction} />

        case 'card_top_up_from_earn':
            return <CardTopUpFromEarnTrxView transaction={transaction} />

        case 'earn_withdraw':
            return <EarnWithdrawalTrxView transaction={transaction} />

        case 'card_cashback_deposit':
            return <CardCashbackDepositTrxView transaction={transaction} />

        case 'deploy_earn_account':
            return (
                <TakerSubtextListItemNoBalance
                    taker={transaction.taker}
                    takerApyMap={transaction.takerApyMap}
                />
            )
        case 'WithdrawalTrx':
            return (
                <OffRampTransactionView
                    variant={{ type: 'no_status' }}
                    networkMap={networkMap}
                    withdrawalRequest={transaction.withdrawalRequest}
                />
            )

        case 'BridgeTrx':
            return (
                <BridgeTrxView
                    networkMap={networkMap}
                    transaction={transaction}
                />
            )
        case 'swaps_io_native_token_swap':
            return (
                <SwapsIONativeTokenSwapView
                    networkMap={networkMap}
                    transaction={transaction}
                />
            )
        case 'CardTopUpTrx':
            return (
                <CardTopUpTrxView transaction={transaction} checks={checks} />
            )
        case 'P2PTransaction':
        case 'P2PNftTransaction':
            return (
                <P2PTransactionView
                    installationId={installationId}
                    networkMap={networkMap}
                    transaction={transaction}
                    dApp={dApp}
                    knownCurrencies={knownCurrencies}
                    checks={checks}
                    accounts={accounts}
                    keystores={keystores}
                />
            )

        case 'ApprovalTransaction':
            return (
                <Approve
                    checks={checks}
                    knownCurrencies={knownCurrencies}
                    transaction={transaction}
                />
            )

        case 'UnknownTransaction':
            return (
                <Unknown
                    networkMap={networkMap}
                    checks={checks}
                    knownCurrencies={knownCurrencies}
                    transaction={transaction}
                />
            )

        case 'FailedTransaction':
            return <Failed dApp={dApp} transaction={transaction} />

        case 'SingleNftApprovalTransaction':
            return (
                <NftListItem
                    networkMap={networkMap}
                    nft={transaction.nft}
                    checks={checks}
                    rightNode={null}
                />
            )

        case 'NftCollectionApprovalTransaction':
            return (
                <NftCollectionListItem
                    networkMap={networkMap}
                    checks={checks}
                    nftCollection={transaction.nftCollectionInfo}
                />
            )
        case 'smart_wallet_activation':
            return <SmartWalletActivationTrxView transaction={transaction} />

        /* istanbul ignore next */
        default:
            return notReachable(transaction)
    }
}
