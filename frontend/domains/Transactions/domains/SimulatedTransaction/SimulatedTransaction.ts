import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import {
    ActivatedCard,
    CardTopUpRequest,
    TopUpCardFromEarnRequest,
} from '@zeal/domains/Card'
import { CashbackDepositRequest } from '@zeal/domains/Card/domains/Cashback'
import { WithdrawalRequest } from '@zeal/domains/Currency/domains/BankTransfer'
import { BridgeRoute } from '@zeal/domains/Currency/domains/Bridge'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import {
    EarnDepositDirectSendRequest,
    EarnDepositWithSwapRequest,
    EarnWithdrawRequest,
    Taker,
    TakerApyMap,
} from '@zeal/domains/Earn'
import { UpdateRechargeRequest } from '@zeal/domains/Earn/helpers/createUpdateRechargeRequest'
import { FiatMoney, Money } from '@zeal/domains/Money'
import { Network } from '@zeal/domains/Network'
import { Nft, NftCollectionInfo } from '@zeal/domains/NFTCollection'
import { SmartContract } from '@zeal/domains/SmartContract'
import { ApprovalAmount, TransactionNft } from '@zeal/domains/Transactions'

export type SimulatedTransaction =
    | ApprovalTransaction
    | UnknownTransaction
    | FailedTransaction
    | SingleNftApprovalTransaction
    | NftCollectionApprovalTransaction
    | P2PTransaction
    | P2PNFTTransaction
    | BridgeTrx
    | WithdrawalTrx
    | CardTopUpTrx
    | DeployEarnAccountTrx
    | EarnRechargeConfigured
    | EarnRechargeUpdated
    | EarnDepositWithSwap
    | EarnDepositDirectSend
    | EarnWithdraw
    | EarnRechargeDisabled
    | CardCashbackDeposit
    | SmartWalletActivationTrx
    | CardTopUpFromEarnTrx
    | SwapsIONativeTokenSwap

export type SimulatedGasEstimate =
    | {
          type: 'GasEstimate'
          gas: string
      }
    | {
          type: 'OptimisticRollupGasEstimate'
          l1Gas: string
          l2Gas: string
      }

export type EarnRechargeConfigured = {
    type: 'earn_recharge_configured'
    request: UpdateRechargeRequest
}

// TODO @resetko-zeal Do we really need both EarnRechargeConfigured and EarnRechargeUpdated?
export type EarnRechargeUpdated = {
    type: 'earn_recharge_updated'
    request: UpdateRechargeRequest
}

export type EarnRechargeDisabled = {
    type: 'earn_recharge_disabled'
}

export type EarnDepositWithSwap = {
    type: 'earn_deposit_with_swap'
    earnDepositRequest: EarnDepositWithSwapRequest
    takerApyMap: TakerApyMap
}

export type EarnDepositDirectSend = {
    type: 'earn_deposit_direct_send'
    earnDepositRequest: EarnDepositDirectSendRequest
    takerApyMap: TakerApyMap
}

export type EarnWithdraw = {
    type: 'earn_withdraw'
    request: EarnWithdrawRequest
    state: 'withdraw' | 'approval' | 'swap'
}

export type DeployEarnAccountTrx = {
    type: 'deploy_earn_account'
    owner: Account
    taker: Taker
    takerApyMap: TakerApyMap
}

export type WithdrawalTrx = {
    type: 'WithdrawalTrx'
    withdrawalRequest: WithdrawalRequest
}

export type CardTopUpTrx = {
    type: 'CardTopUpTrx'
    topUpRequest: CardTopUpRequest
}

export type CardTopUpFromEarnTrx = {
    type: 'card_top_up_from_earn'
    topUpRequest: TopUpCardFromEarnRequest
    state: 'withdraw' | 'approval' | 'swap'
}

export type BridgeTrx = {
    type: 'BridgeTrx'
    bridgeRoute: BridgeRoute
}

export type SwapsIONativeTokenSwap = {
    type: 'swaps_io_native_token_swap'
    swapsIOQuote: SwapsIOQuote
}

export type ApprovalTransaction = {
    type: 'ApprovalTransaction'
    amount: ApprovalAmount
    approveTo: SmartContract
}

export type UnknownTransaction = {
    type: 'UnknownTransaction'
    method: string
    tokens: UnknownTransactionToken[]
    nfts: TransactionNft[]
}

export type FailedTransaction = {
    type: 'FailedTransaction'
    method: string
}

export type P2PTransaction = {
    type: 'P2PTransaction'
    token: UnknownTransactionToken
    toAddress: Web3.address.Address
}

export type P2PNFTTransaction = {
    type: 'P2PNftTransaction'
    nft: TransactionNft
    toAddress: Web3.address.Address
}

export type SingleNftApprovalTransaction = {
    type: 'SingleNftApprovalTransaction'
    approveTo: SmartContract
    nft: Nft
}

export type NftCollectionApprovalTransaction = {
    type: 'NftCollectionApprovalTransaction'
    nftCollectionInfo: NftCollectionInfo
    approveTo: SmartContract
}

export type CardCashbackDeposit = {
    type: 'card_cashback_deposit'
    cashbackDepositRequest: CashbackDepositRequest
    card: ActivatedCard
}

export type SmartWalletActivationTrx = {
    type: 'smart_wallet_activation'
    account: Account
    network: Network
}

/**
 * It might look same as TransactionToken, but it renders little different
 * and also have priceInDefaultCurrency not optional
 */
export type UnknownTransactionToken = {
    direction: 'Send' | 'Receive'
    amount: Money
    priceInDefaultCurrency: FiatMoney | null
}
