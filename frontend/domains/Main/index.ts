import { Intersect } from '@zeal/toolkit/Intersect'
import { Result } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { Address } from '@zeal/domains/Address'
import { CurrencyId } from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { ConnectionState } from '@zeal/domains/DApp/domains/ConnectionState'
import { KeyStore } from '@zeal/domains/KeyStore'
import { NetworkHexId } from '@zeal/domains/Network'

export type EntryPoint =
    | AddAccount
    | AddFromHardwareWallet
    | RecoverSafe
    | CreateContact
    | Onboarding
    | SendERC20Token
    | SendNFT
    | SetupRecoveryKit
    | Swap
    | Bridge
    | BankTransfer
    | KycProcess
    | CreateSafe
    | ZWidget
    | Extension
    | AddFunds
    | Buy
    | SendMoney

export type AddFunds = {
    type: 'add_funds'
    address: Web3.address.Address
}

export type TabControllerTab =
    | { type: 'portfolio' }
    | { type: 'activity' }
    | { type: 'card' }
    | { type: 'rewards' }
    | { type: 'browse'; url: URL | null }

export type StrippedHomeTab = Extract<
    TabControllerTab,
    { type: 'portfolio' | 'card' | 'rewards' }
>

export type StrippedHomeWithBrowserTab = Extract<
    TabControllerTab,
    { type: 'portfolio' | 'card' | 'rewards' | 'browse' }
>

export type InitialActiveTab = Intersect<
    TabControllerTab,
    StrippedHomeWithBrowserTab
>

export type ZealDAppEntryPoint = ZealDAppAccountTopUp

export type ZealDAppAccountTopUp = {
    type: 'account_top_up'
    account: Account
    keyStoreType: KeyStore['type']
    installationId: string
}

export type PageEntrypoint = Exclude<EntryPoint, ZWidget | Extension>

export type OnboardedEntrypoint = Exclude<PageEntrypoint, Onboarding>

export type CreateSafe = {
    type: 'create_safe'
}

export type BankTransfer = {
    type: 'bank_transfer'
    variant: DepositWithdrawVariant
}

export type KycProcess = {
    type: 'kyc_process'
}

export type CreateContact = {
    type: 'create_contact'
}

export type AddFromHardwareWallet = {
    type: 'add_from_hardware_wallet'
}

export type RecoverSafe = {
    type: 'recover_safe'
}

export type Swap = {
    type: 'swap'
    fromAddress: Address
    fromCurrencyId: CurrencyId | null
    toCurrencyId: CurrencyId | null
}

export type Buy = {
    type: 'buy'
    fromAddress: Web3.address.Address
}

export type Bridge = {
    type: 'bridge'
    fromAddress: Address
    fromCurrencyId: CurrencyId | null
}

export type Onboarding = {
    type: 'onboarding'
}

export type ZWidget = {
    type: 'zwidget'
    dAppUrl: string
}

export type Mode = 'fullscreen' | 'popup'

export type Extension = {
    type: 'extension'
    mode: Mode
    initialActiveTab: InitialActiveTab
}

export type AddAccount = {
    type: 'add_account'
}

export type SendMoney = {
    type: 'send_money'
    fromAddress: Web3.address.Address
    tokenCurrencyId: CurrencyId | null
    toAddress: Web3.address.Address | null
}

export type SendERC20Token = {
    type: 'send_erc20_token'
    fromAddress: Web3.address.Address
    tokenCurrencyId: CurrencyId | null
    toAddress: Web3.address.Address | null
}

export type SendNFT = {
    type: 'send_nft'
    fromAddress: Address
    nftId: string
    mintAddress: Address
    networkHexId: NetworkHexId
}

export type SetupRecoveryKit = {
    type: 'setup_recovery_kit'
    address: Address
}
export type InternalTransactionEventSource =
    | 'bridge'
    | 'bridgeApprove'
    | 'cardAddCash'
    | 'cardCashBackDeposit'
    | 'earnAddTakerToHolder'
    | 'earnDeposit'
    | 'earnHolderDeploy'
    | 'earnDisableRecharge'
    | 'earnEnableRecharge'
    | 'earnTakerDeploy'
    | 'earnWithdrawal'
    | 'kinetexApprove'
    | 'kinetexNativeSwap'
    | 'offramp'
    | 'send'
    | 'swap'
    | 'swapApprove'
    | 'topupCardFromEarn'
    | 'topupSend'
    | 'transactionRequestWidget'

export type InternalTransactionActionSource = {
    type: 'internal'
    dAppSiteInfo?: DAppSiteInfo // TODO @resetko-zeal we user it for swap, so once we have vanity for swap it should die
    transactionEventSource: InternalTransactionEventSource
}

type InternalSignActionSource = {
    type: 'internal_sign' // TODO @resetko-zeal should die after vanity for sign is done in all places
    dAppSiteInfo: DAppSiteInfo
    transactionEventSource:
        | 'gnosisPayLogin'
        | 'gnosisPaySignSafeRelayTransaction'
        | 'swapsioSign'
        | 'unblockLogin'
}

type ExternalActionSource = {
    type: 'external'
    dAppSiteInfo: DAppSiteInfo
    transactionEventSource: 'walletConnect' | 'zwidget' | 'browsertab'
}

export type ActionSource2 =
    | InternalTransactionActionSource
    | InternalSignActionSource
    | ExternalActionSource

export type SignActionSource = InternalSignActionSource | ExternalActionSource

// Messages

export type RPCRequestMsg = {
    type: 'rpc_request'
    request: {
        id: number | string
        method: unknown
        params: unknown
    }
}

export type ChangeIframeSizeMessage = {
    type: 'change_iframe_size'
    size:
        | 'icon'
        | 'small'
        | 'large'
        | 'large_with_full_screen_takeover'
        | 'hide'
}

export type ChangeMetaMaskMode = {
    type: 'meta_mask_mode_changed'
    mode: boolean
}

export type Drag = { type: 'drag'; movement: { x: number; y: number } }

export type RPCResponse = {
    type: 'rpc_response'
    id: number | string
    response: Result<unknown, unknown>
}

export type ReadyMsg = {
    type: 'ready'
    state:
        | { type: 'disconnected' | 'not_interacted' }
        | {
              type: 'connected'
              networkHexId: NetworkHexId
              address: Address
          }
}

export type NetworkChangeMsg = {
    type: 'network_change'
    chainId: string
}

export type Disconnect = {
    type: 'disconnect'
}

export type AccountsChangeMsg = {
    type: 'account_change'
    account: string
}

export type ExtensionToZwidgetExtensionAddressChange = {
    type: 'extension_to_zwidget_extension_address_change'
    address: string
}

export type ExtensionToZwidgetQueryZWidgetConnectionStateAndNetwork = {
    type: 'extension_to_zwidget_query_zwidget_connection_state_and_network'
}

export type MetaMaskProviderAvailable = {
    type: 'meta_mask_provider_available'
}

export type MetaMaskModeInit = {
    type: 'meta_mask_mode_init'
    mode: boolean
}

export type AlternativeProvider = 'metamask' | 'provider_unavailable'

export type ConnectZealProvider = {
    type: 'connect_zeal_provider'
    address: Address
    chainId: NetworkHexId
}

export type ToServiceWorkerTrezorConnectGetPublicKey = {
    type: 'to_service_worker_trezor_connect_get_public_key'
    coin: string
    path: string
}

export type ToServiceWorkerTrezorConnectSignTransaction = {
    type: 'to_service_worker_trezor_connect_sign_transaction'
    transaction: object
    path: string
}

export type ToServiceWorkerTrezorConnectSignMessage = {
    type: 'to_service_worker_trezor_connect_sign_message'
    message: string
    path: string
}

export type ToServiceWorkerTrezorConnectSignTypedData = {
    type: 'to_service_worker_trezor_connect_sign_typed_data'
    typedData: object
    path: string
}

export type CurrentZWidgetConnectionStateAndNetwork = {
    type: 'current_zwidget_connection_state_and_network'
    state: ConnectionState
    networkHexId: NetworkHexId
    metaMaskMode: boolean
}

export type ExtensionToZwidgetExpandZWidget = {
    type: 'extension_to_zwidget_expand_zwidget'
}

export type WebviewProviderInjected = {
    type: 'webview_provider_injected'
}

export type ExtensionToZwidgetMetaMaskModeChanged = {
    type: 'extension_to_zwidget_meta_mask_mode_changed'
    mode: boolean
}

export type ZWidgetToExtension = CurrentZWidgetConnectionStateAndNetwork

export type ChromeRuntimeMessageRequest =
    | ExtensionToZwidgetExtensionAddressChange
    | ExtensionToZwidgetQueryZWidgetConnectionStateAndNetwork
    | ExtensionToZwidgetExpandZWidget
    | ToServiceWorkerTrezorConnectGetPublicKey
    | ToServiceWorkerTrezorConnectSignTransaction
    | ToServiceWorkerTrezorConnectSignMessage
    | ToServiceWorkerTrezorConnectSignTypedData
    | ExtensionToZwidgetMetaMaskModeChanged

export type ProviderToZwidget =
    | RPCRequestMsg
    | MetaMaskProviderAvailable
    | MetaMaskModeInit

export type WebviewProviderToInAppBrowser =
    | WebviewProviderInjected
    | RPCRequestMsg
export type InAppBrowserToWebviewProvider =
    | ReadyMsg
    | RPCResponse
    | NetworkChangeMsg
    | AccountsChangeMsg

export type ZwidgetToContentScript =
    | ChangeIframeSizeMessage
    | Drag
    | ChangeMetaMaskMode

export type ZwidgetToProvider =
    | RPCResponse
    | AccountsChangeMsg
    | NetworkChangeMsg
    | Disconnect
    | ReadyMsg
    | ConnectZealProvider

export type ExtensionToTopUpDapp = {
    type: 'init_zeal_dapp_entrypoint'
    zealDAppEntryPoint: ZealDAppEntryPoint
}

export type TopUpDappToExtension = { type: 'ready' }
