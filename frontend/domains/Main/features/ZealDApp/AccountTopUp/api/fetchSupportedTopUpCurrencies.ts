import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { values } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoCurrency, currencyId } from '@zeal/domains/Currency'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStore } from '@zeal/domains/KeyStore'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { filterNetworksByKeyStoreType } from '@zeal/domains/Network/helpers/filterNetworksByKeyStore'

import {
    BICONOMY_SUPPORTED_GAS_ABSTRACTION_CURRENCIES,
    FALLBACK_TOP_UP_CURRENCIES,
} from '../constants'

const getTopUpCurrenciesByKeystoreType = ({
    network,
    keyStoreType,
}: {
    network: PredefinedNetwork
    keyStoreType: KeyStore['type']
}): Web3.address.Address[] => {
    switch (keyStoreType) {
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return [network.nativeCurrency.address]
        case 'safe_4337':
            const gasTokenAddresses =
                BICONOMY_SUPPORTED_GAS_ABSTRACTION_CURRENCIES[
                    network.hexChainId
                ] || null

            if (!gasTokenAddresses) {
                captureError(
                    new ImperativeError(
                        'Missing gas abstraction topup currencies in dApp for network',
                        { network: network }
                    )
                )
                return [network.nativeCurrency.address]
            }
            return [network.nativeCurrency.address, ...gasTokenAddresses]
        /* istanbul ignore next */
        default:
            return notReachable(keyStoreType)
    }
}

export const fetchSupportedTopUpCurrencies = async ({
    supportedNetworks,
    keyStoreType,
}: {
    supportedNetworks: PredefinedNetwork[]
    keyStoreType: KeyStore['type']
}): Promise<CryptoCurrency[]> => {
    const networks = filterNetworksByKeyStoreType({
        networks: supportedNetworks,
        keyStoreType,
    })

    try {
        const ids = networks.flatMap((network) =>
            getTopUpCurrenciesByKeystoreType({
                network,
                keyStoreType,
            }).map((address) =>
                currencyId({ address, network: network.hexChainId })
            )
        )

        return values(
            await fetchCryptoCurrency2({
                currencies: ids,
                networkRPCMap: {},
            })
        )
    } catch (e) {
        captureError(e)
        return FALLBACK_TOP_UP_CURRENCIES
    }
}
