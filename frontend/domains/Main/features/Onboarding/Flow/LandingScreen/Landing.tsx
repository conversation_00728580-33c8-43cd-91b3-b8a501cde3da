import React, { useEffect, useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { ArtworkLayout } from '@zeal/uikit/ArtworkLayout'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { StoryPage } from '@zeal/uikit/StoryWithPersistentActions'
import { StoryWithPersistentActions } from '@zeal/uikit/StoryWithPersistentActions'
import { Text } from '@zeal/uikit/Text'
import { TextButton } from '@zeal/uikit/TextButton'

import { noop, notReachable } from '@zeal/toolkit'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'
import { openExternalURL } from '@zeal/toolkit/Window'

import { isLandingScreen2Enabled } from '@zeal/domains/ABTest'
import { COUNTRY_TO_CURRENCY_MAP } from '@zeal/domains/Country/constants'
import { tryToGetUserCurrentCountry } from '@zeal/domains/Country/helpers/tryToGetUserCurrentCountry'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import {
    ZEAL_PRIVACY_POLICY_URL,
    ZEAL_TERMS_OF_USE_URL,
} from '@zeal/domains/Main/constants'
import {
    postUserEvent,
    postUserEventOnce,
} from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    skyApy: number
    slide: number
    installationId: string
    paused: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_login_clicked'; currentArtwork: number }
    | { type: 'on_create_account_clicked' }

const getCurrencySymbol = (): string => {
    const userCountryResult = tryToGetUserCurrentCountry()

    switch (userCountryResult.type) {
        case 'Success': {
            const countryCode = userCountryResult.data.code
            const fiatCurrencyCode = COUNTRY_TO_CURRENCY_MAP[countryCode]

            if (countryCode === 'BR') {
                return FIAT_CURRENCIES.USD.symbol
            }

            if (countryCode === 'GB' || fiatCurrencyCode === 'GBP') {
                return FIAT_CURRENCIES.GBP.symbol
            }

            return FIAT_CURRENCIES.EUR.symbol
        }

        case 'Failure':
            return FIAT_CURRENCIES.EUR.symbol

        default:
            return notReachable(userCountryResult)
    }
}

const createStories = (skyApy: number): StoryPage[] => [
    {
        artwork: { type: 'static' as const, name: 'cards', background: 'dark' },
        title: (
            <FormattedMessage
                id="onboarding.loginOrCreateAccount.cards.title"
                defaultMessage="Visa card with high{br}returns and no fees"
                values={{
                    br: '\n',
                }}
            />
        ),
        subtitle: (
            <FormattedMessage
                id="onboarding.loginOrCreateAccount.cards.subtitle"
                defaultMessage="Only available in certain regions. By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>"
                values={{
                    Terms: (message) => (
                        <TextButton
                            onClick={() =>
                                openExternalURL(ZEAL_TERMS_OF_USE_URL)
                            }
                        >
                            {message}
                        </TextButton>
                    ),
                    PrivacyPolicy: (message) => (
                        <TextButton
                            onClick={() => {
                                openExternalURL(ZEAL_PRIVACY_POLICY_URL)
                            }}
                        >
                            {message}
                        </TextButton>
                    ),
                }}
            />
        ),
    },
    {
        artwork: { type: 'static', name: 'earn_black', background: 'dark' },
        title: (
            <FormattedMessage
                id="onboarding.loginOrCreateAccount.earn.title"
                defaultMessage="Earning {percent} per year{br}Trusted by {currencySymbol}5bn+"
                values={{
                    br: '\n',
                    percent: getFormattedPercentage(skyApy),
                    currencySymbol: getCurrencySymbol(),
                }}
            />
        ),
        subtitle: (
            <FormattedMessage
                id="onboarding.loginOrCreateAccount.earn.subtitle"
                defaultMessage="Returns vary; capital at risk. By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>"
                values={{
                    Terms: (message) => (
                        <TextButton
                            onClick={() =>
                                openExternalURL(ZEAL_TERMS_OF_USE_URL)
                            }
                        >
                            {message}
                        </TextButton>
                    ),
                    PrivacyPolicy: (message) => (
                        <TextButton
                            onClick={() => {
                                openExternalURL(ZEAL_PRIVACY_POLICY_URL)
                            }}
                        >
                            {message}
                        </TextButton>
                    ),
                }}
            />
        ),
    },
    {
        artwork: { type: 'static', name: 'trading', background: 'dark' },
        title: (
            <FormattedMessage
                id="onboarding.loginOrCreateAccount.trading.title"
                defaultMessage="Invest in everything,{br}BTC to S&P"
                values={{
                    br: '\n',
                }}
            />
        ),
        subtitle: (
            <FormattedMessage
                id="onboarding.loginOrCreateAccount.trading.subtitle"
                defaultMessage="Capital at risk. By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>"
                values={{
                    Terms: (message) => (
                        <TextButton
                            onClick={() =>
                                openExternalURL(ZEAL_TERMS_OF_USE_URL)
                            }
                        >
                            {message}
                        </TextButton>
                    ),
                    PrivacyPolicy: (message) => (
                        <TextButton
                            onClick={() => {
                                openExternalURL(ZEAL_PRIVACY_POLICY_URL)
                            }}
                        >
                            {message}
                        </TextButton>
                    ),
                }}
            />
        ),
    },
]

export const Landing = ({
    onMsg,
    skyApy,
    slide,
    installationId,
    paused = false,
}: Props) => {
    const [currentStoryIndex, setCurrentStoryIndex] = useState(slide)

    useEffect(() => {
        setCurrentStoryIndex(slide)
    }, [slide])

    if (isLandingScreen2Enabled(installationId)) {
        const stories = createStories(skyApy)

        postUserEventOnce({
            type: 'StoryFlowStartedEvent',
            name: 'onboarding',
            installationId,
        })

        return (
            <StoryWithPersistentActions
                stories={stories}
                slide={currentStoryIndex}
                paused={paused}
                actions={{
                    primary: {
                        title: (
                            <FormattedMessage
                                id="onboarding.loginOrCreateAccount.createAccount"
                                defaultMessage="Create Account"
                            />
                        ),
                        onClick: () =>
                            onMsg({ type: 'on_create_account_clicked' }),
                    },
                    secondary: {
                        title: (
                            <FormattedMessage
                                id="onboarding.loginOrCreateAccount.login"
                                defaultMessage="Login"
                            />
                        ),
                        onClick: () => {
                            onMsg({
                                type: 'on_login_clicked',
                                currentArtwork: currentStoryIndex,
                            })
                        },
                    },
                }}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_stories_completed':
                            postUserEvent({
                                type: 'StoryFlowFinishedEvent',
                                name: 'onboarding',
                                installationId,
                            })
                            break
                        case 'on_next_slide_shown':
                            setCurrentStoryIndex(msg.currentSlide)
                            postUserEvent({
                                type: 'StoryFlowAdvancedEvent',
                                name: 'onboarding',
                                slideNumber: msg.currentSlide,
                                installationId,
                            })
                            break
                        case 'on_error':
                            captureError(msg.error)
                            break
                        /* istanbul ignore next */
                        default:
                            return notReachable(msg)
                    }
                }}
            />
        )
    }

    return (
        <ArtworkLayout
            header={
                <HeaderV2
                    size="large"
                    align="center"
                    title={
                        <FormattedMessage
                            id="onboarding.loginOrCreateAccount.earningPerYear"
                            defaultMessage="Earning {percent}{br}per year"
                            values={{
                                br: '\n',
                                percent: getFormattedPercentage(skyApy),
                            }}
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="onboarding.loginOrCreateAccount.trustedBy"
                            defaultMessage="Digital money markets{br}Trusted by {assets}"
                            values={{
                                br: '\n',
                                assets: (
                                    <Text color="teal40">
                                        <FormattedMessage
                                            id="onboarding.loginOrCreateAccount.amountOfAssets"
                                            defaultMessage="$10bn+ of assets"
                                        />
                                    </Text>
                                ),
                            }}
                        />
                    }
                />
            }
            artwork={{
                type: 'static',
                name: 'ticking',
                background: 'light',
            }}
            actions={
                <Column spacing={16} fill>
                    <Actions variant="default" direction="column">
                        <Button
                            variant="secondary"
                            size="regular"
                            onClick={() =>
                                onMsg({
                                    type: 'on_login_clicked',
                                    currentArtwork: 0,
                                })
                            }
                        >
                            <FormattedMessage
                                id="onboarding.loginOrCreateAccount.login"
                                defaultMessage="Login"
                            />
                        </Button>
                        <Button
                            variant="primary"
                            size="regular"
                            onClick={() =>
                                onMsg({ type: 'on_create_account_clicked' })
                            }
                        >
                            <FormattedMessage
                                id="onboarding.loginOrCreateAccount.createAccount"
                                defaultMessage="Create Account"
                            />
                        </Button>
                    </Actions>

                    <Text
                        variant="caption1"
                        weight="regular"
                        align="center"
                        color="textSecondary"
                    >
                        <FormattedMessage
                            id="password.add.info.t_and_c"
                            defaultMessage="By continuing you accept our <Terms>Terms</Terms> & <PrivacyPolicy>Privacy Policy</PrivacyPolicy>"
                            values={{
                                Terms: (message) => (
                                    <TextButton
                                        onClick={() =>
                                            openExternalURL(
                                                ZEAL_TERMS_OF_USE_URL
                                            )
                                        }
                                    >
                                        {message}
                                    </TextButton>
                                ),
                                PrivacyPolicy: (message) => (
                                    <TextButton
                                        onClick={() => {
                                            openExternalURL(
                                                ZEAL_PRIVACY_POLICY_URL
                                            )
                                        }}
                                    >
                                        {message}
                                    </TextButton>
                                ),
                            }}
                        />
                    </Text>
                </Column>
            }
            onMsg={noop}
        />
    )
}
