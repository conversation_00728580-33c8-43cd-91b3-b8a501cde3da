import { useCallback, useEffect, useState } from 'react'

import Messaging from '@react-native-firebase/messaging'

import { LoadingLayout } from '@zeal/uikit/LoadingLayout'
import { RefreshContainerState } from '@zeal/uikit/RefreshContainer'

import { noop, notReachable } from '@zeal/toolkit'
import {
    ReloadableData,
    useReloadableData,
} from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { useLiveRef } from '@zeal/toolkit/React'
import * as Web3 from '@zeal/toolkit/Web3'
import { useVisibility } from '@zeal/toolkit/Window/useVisibility'

import { Account } from '@zeal/domains/Account'
import {
    fetchAccounts,
    FetchPortfolioRequest,
    FetchPortfolioResponse,
} from '@zeal/domains/Account/api/fetchAccounts'
import { Address } from '@zeal/domains/Address'
import { ActivatedCard, CardConfig } from '@zeal/domains/Card'
import { ReferralConfig } from '@zeal/domains/Card/domains/Reward'
import { tryToGetUserCurrentCountry } from '@zeal/domains/Country/helpers/tryToGetUserCurrentCountry'
import {
    CurrencyHiddenMap,
    CurrencyId,
    currencyId,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { fetchShortStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { FIAT_DUST } from '@zeal/domains/Currency/constants'
import { isEqual } from '@zeal/domains/Currency/helpers/isEqual'
import { ConnectionMap } from '@zeal/domains/DApp/domains/ConnectionState'
import { WalletConnectInstanceLoadable } from '@zeal/domains/DApp/domains/WalletConnect/api/fetchWalletConnectInstance'
import {
    EarnTakerMetrics,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { sumEarn } from '@zeal/domains/Earn/helpers/sumEarn'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { AppRating } from '@zeal/domains/Feedback'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { InitialActiveTab, Mode } from '@zeal/domains/Main'
import { NetworkMap } from '@zeal/domains/Network'
import { PREDEFINED_NETWORKS } from '@zeal/domains/Network/constants'
import { NotificationActionsListener } from '@zeal/domains/Notification/features/NotificationActionsListener'
import { parseNotification } from '@zeal/domains/Notification/parsers/parseNotification'
import { Portfolio } from '@zeal/domains/Portfolio'
import { hasTokens } from '@zeal/domains/Portfolio/helpers/hasTokens'
import { isFunded } from '@zeal/domains/Portfolio/helpers/IsFunded'
import { portfolioToPortfolio2 } from '@zeal/domains/Portfolio/helpers/portfolioToPortfolio2'
import { unsafeGetPortfolioCache } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import {
    BrowserTabState,
    CelebrationConfig,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
    Storage,
} from '@zeal/domains/Storage'
import { keystoreToUserEventType } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { FeatureFork } from './FeatureFork'

type Props = {
    mode: Mode
    initialActiveTab: InitialActiveTab
    storage: Storage
    sessionPassword: string
    selectedAddress: string
    earnTakerMetrics: EarnTakerMetrics
    customCurrencies: CustomCurrencyMap

    walletConnectInstanceLoadable: WalletConnectInstanceLoadable
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationId: string
    installationCampaign: string | null

    connections: ConnectionMap
    networkMap: NetworkMap

    browserTabState: BrowserTabState
    isEthereumNetworkFeeWarningSeen: boolean
    earnHistoricalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    cardConfig: CardConfig
    celebrationConfig: CelebrationConfig
    appRating: AppRating
    appBrowserProviderScript: string | null
    referralConfig: ReferralConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'portfolio_loaded'
          portfolio: Portfolio
          address: Address
          fetchedAt: Date
      }
    | { type: 'account_item_clicked'; account: Account }
    | { type: 'confirm_account_delete_click'; account: Account }
    | Extract<
          MsgOf<typeof FeatureFork>,
          {
              type:
                  | 'on_card_b_reward_dissmiss_clicked'
                  | 'card_breward_claimed'
                  | 'card_brewards_updated'
                  | 'on_dismiss_add_to_wallet_banner_clicked'
                  | 'on_meta_mask_mode_changed_pupup_refresh_page_clicked'
                  | 'on_add_funds_click'
                  | 'transaction_request_completed'
                  | 'transaction_request_failed'
                  | 'transaction_request_cancelled'
                  | 'on_account_label_change_submit'
                  | 'on_lock_zeal_click'
                  | 'on_profile_change_confirm_click'
                  | 'on_recovery_kit_setup'
                  | 'on_disconnect_dapps_click'
                  | 'on_delete_all_dapps_confirm_click'
                  | 'track_wallet_clicked'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_custom_currency_delete_request'
                  | 'on_custom_currency_update_request'
                  | 'on_send_nft_click'
                  | 'on_swap_clicked'
                  | 'on_buy_clicked'
                  | 'on_bridge_clicked'
                  | 'on_send_clicked'
                  | 'bridge_completed'
                  | 'on_accounts_create_success_animation_finished'
                  | 'on_bank_transfer_selected'
                  | 'on_dismiss_kyc_button_clicked'
                  | 'on_kyc_try_again_clicked'
                  | 'on_token_pin_click'
                  | 'on_token_un_pin_click'
                  | 'on_token_hide_click'
                  | 'on_token_un_hide_click'
                  | 'transaction_submited'
                  | 'cancel_submitted'
                  | 'on_rpc_change_confirmed'
                  | 'on_select_rpc_click'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'on_add_private_key_click'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_open_fullscreen_view_click'
                  | 'transaction_request_replaced'
                  | 'on_nba_close_click'
                  | 'on_nba_cta_click'
                  | 'on_zwidget_expand_request'
                  | 'on_card_onboarded_account_state_received'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
                  | 'transaction_failure_accepted'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'transaction_cancel_failure_accepted'
                  | 'on_browser_url_change'
                  | 'on_card_transactions_fetch_success'
                  | 'on_notifications_config_changed'
                  | 'on_earn_deposit_success'
                  | 'on_earn_last_recharge_transaction_hash_loaded'
                  | 'on_external_earn_deposit_completed_close_click'
                  | 'on_get_cashback_currency_clicked'
                  | 'on_cashback_loaded'
                  | 'on_earn_updated'
                  | 'import_card_owner_clicked'
                  | 'on_gnosis_portfolio_loaded'
                  | 'on_card_tile_fetch_completed_successfully'
                  | 'on_card_disconnected'
                  | 'on_switch_card_new_card_selected'
                  | 'on_earnings_fetched'
                  | 'on_historical_taker_user_currency_rate_fetched'
                  | 'on_swaps_io_swap_requests_fetched'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_default_currency_selected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_address_scanned'
                  | 'on_address_scanned_and_add_label'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'on_add_label_to_track_only_account_during_send'
                  | 'on_ethereum_network_fee_warning_understand_clicked'
                  | 'on_switch_bank_transfer_provider_clicked'
                  | 'on_sign_in_to_gnosis_pay_error_close_clicked'
                  | 'on_gnosis_pay_not_available_accepted'
                  | 'on_get_gnosis_pay_support_clicked'
                  | 'on_gnosis_pay_profile_clicked'
                  | 'on_dismiss_bridge_widget_click'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_gnosis_pay_account_created'
                  | 'on_do_bank_transfer_clicked'
                  | 'on_dissmiss_card_kyc_onboarding_widget_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_earn_celebration_triggered'
                  | 'on_new_virtual_card_created_successfully'
                  | 'on_transaction_activities_loaded'
                  | 'on_virtual_card_order_created_animation_completed'
                  | 'on_swaps_io_pending_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_failed'
                  | 'on_swaps_io_transaction_activity_completed'
                  | 'on_swaps_io_transaction_activity_swap_started'
                  | 'on_pending_send_transaction_activity_completed'
                  | 'on_pending_send_transaction_activity_failed'
                  | 'on_pending_areward_claim_transaction_activity_completed'
                  | 'on_pending_areward_claim_transaction_activity_failed'
                  | 'on_pending_breward_claim_transaction_activity_completed'
                  | 'on_pending_breward_claim_transaction_activity_failed'
                  | 'on_dissmiss_card_kyc_onboarded_widget_clicked'
                  | 'on_a_reward_claimed_successfully'
                  | 'on_a_rewards_configured'
                  | 'on_physical_card_activated_info_screen_closed'
                  | 'on_rewards_warning_confirm_account_delete_click'
          }
      >
    | Extract<
          MsgOf<typeof NotificationActionsListener>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_get_cashback_currency_clicked'
                  | 'import_card_owner_clicked'
                  | 'on_app_rating_submitted'
                  | 'on_cashback_celebration_triggered'
                  | 'on_a_reward_claimed_successfully'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'cancel_submitted'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'recover_safe_wallet_clicked'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
          }
      >
    | {
          type: 'on_physical_card_activated_info_screen_closed_wallet_not_funded'
          address: Web3.address.Address
          card: ActivatedCard
      }

const initLoading = (
    storage: Storage,
    selectedAddress: Address,
    customCurrencies: CustomCurrencyMap,
    networkMap: NetworkMap,
    cardConfig: CardConfig,
    installationId: string,
    defaultCurrencyConfig: DefaultCurrencyConfig
): ReloadableData<
    FetchPortfolioResponse,
    FetchPortfolioRequest & { installationId: string }
> => {
    const portfolio = unsafeGetPortfolioCache({
        address: selectedAddress,
        portfolioMap: storage.portfolios,
    })
    const params = {
        address: selectedAddress,
        customCurrencies,
        forceRefresh: false,
        networkMap,
        networkRPCMap: storage.networkRPCMap,
        cardConfig,
        installationId,
        currencyHiddenMap: storage.currencyHiddenMap,
        defaultCurrencyConfig,
    }
    return portfolio
        ? {
              type: 'reloading',
              params,
              data: {
                  portfolio,
                  fetchedAt: storage.fetchedAt,
              },
          }
        : { type: 'loading', params }
}

const fetch = async (
    params: FetchPortfolioRequest & { installationId: string }
) => {
    const accounts = await fetchAccounts(params)
    // FIXME :: @max delete reporting
    try {
        const currenciesForFetch: {
            countScam: number
            currencyIds: CurrencyId[]
        } = { countScam: 0, currencyIds: [] }
        const staticCurrencies = await fetchShortStaticCurrencies()
        accounts.portfolio.tokens.forEach((token) => {
            const predefinedNetwork = PREDEFINED_NETWORKS.find(
                (item) => item.hexChainId === token.networkHexId
            )
            if (predefinedNetwork) {
                const id = currencyId({
                    network: token.networkHexId,
                    address: token.address as Web3.address.Address,
                })
                if (!staticCurrencies[id]) {
                    currenciesForFetch.currencyIds.push(id)
                    if (token.scam) {
                        currenciesForFetch.countScam++
                    }
                }
            }
        })
        postUserEvent({
            type: 'PortfolioLoadedStaticCurrencies',
            scam: currenciesForFetch.countScam,
            total: accounts.portfolio.tokens.length,
            fetch:
                currenciesForFetch.currencyIds.length -
                currenciesForFetch.countScam,
            installationId: params.installationId,
        })
    } catch (error) {
        captureError(error)
    }

    return accounts
}

const REFRESH_TIME_MS = 2000

export const PortfolioLoader = ({
    storage,
    initialActiveTab,
    selectedAddress,
    installationId,
    earnTakerMetrics,
    connections,
    browserTabState,
    installationCampaign,
    customCurrencies,
    earnHistoricalTakerUserCurrencyRateMap,
    sessionPassword,
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    celebrationConfig,
    appRating,
    mode,
    walletConnectInstanceLoadable,
    cardConfig,
    isEthereumNetworkFeeWarningSeen,
    defaultCurrencyConfig,
    appBrowserProviderScript,
    referralConfig,
    onMsg,
}: Props) => {
    const liveOnMsg = useLiveRef(onMsg)
    const liveStorage = useLiveRef(storage)
    const liveNetworkMap = useLiveRef(networkMap)
    const liveSelectedAddress = useLiveRef(selectedAddress)
    const liveCurrencyHiddenMap = useLiveRef(currencyHiddenMap)

    const [refreshContainerState, setRefreshContainerState] =
        useState<RefreshContainerState>('refreshed')

    useEffect(() => {
        switch (refreshContainerState) {
            case 'refreshing':
                const timeout = setTimeout(() => {
                    setRefreshContainerState('refreshed')
                }, REFRESH_TIME_MS)
                return () => clearTimeout(timeout)
            case 'refreshed':
                return noop

            default:
                return notReachable(refreshContainerState)
        }
    }, [refreshContainerState])

    const [loadable, setLoadable] = useReloadableData(
        fetch,
        (): ReloadableData<
            FetchPortfolioResponse,
            FetchPortfolioRequest & { installationId: string }
        > =>
            initLoading(
                storage,
                selectedAddress,
                customCurrencies,
                networkMap,
                cardConfig,
                installationId,
                defaultCurrencyConfig
            )
    )

    const refreshPortfolio = useCallback(() => {
        setLoadable((old) => {
            switch (old.type) {
                case 'reloading':
                case 'loading':
                    return old
                case 'loaded':
                case 'subsequent_failed':
                    return {
                        type: 'reloading',
                        params: old.params,
                        data: old.data,
                    }

                case 'error':
                    return {
                        type: 'loading',
                        params: old.params,
                    }
                default:
                    return notReachable(old)
            }
        })
    }, [setLoadable])

    useEffect(() => {
        switch (refreshContainerState) {
            case 'refreshing':
                refreshPortfolio()
                break

            case 'refreshed':
                break
            default:
                return notReachable(refreshContainerState)
        }
    }, [refreshContainerState, refreshPortfolio])

    useEffect(() => {
        // TODO @resetko-zeal :: this is a tricky part, you need to check if address was really changed before "re-loading"
        //         otherwise you will get infinite loop of updates,
        //         when loaded we want update storage, when storage update we want to reload loadable
        if (
            loadable.params.address !== selectedAddress ||
            loadable.params.cardConfig.type !== cardConfig.type
        ) {
            setLoadable(
                initLoading(
                    storage,
                    selectedAddress,
                    customCurrencies,
                    liveNetworkMap.current,
                    cardConfig,
                    installationId,
                    defaultCurrencyConfig
                )
            )
        }

        if (!isEqual(customCurrencies, loadable.params.customCurrencies)) {
            setLoadable({
                type: 'loading',
                params: {
                    currencyHiddenMap,
                    address: selectedAddress,
                    customCurrencies,
                    networkMap: liveNetworkMap.current,
                    networkRPCMap: storage.networkRPCMap,
                    cardConfig,
                    installationId,
                    defaultCurrencyConfig,
                },
            })
        }
    }, [
        loadable.params.address,
        loadable.params.cardConfig.type,
        storage,
        selectedAddress,
        setLoadable,
        loadable.params.customCurrencies,
        customCurrencies,
        liveNetworkMap,
        cardConfig,
        installationId,
        defaultCurrencyConfig,
        currencyHiddenMap,
    ])

    useEffect(() => {
        const keystore = getKeyStore({
            keyStoreMap: liveStorage.current.keystoreMap,
            address: loadable.params.address,
        })

        switch (loadable.type) {
            case 'loaded':
                postUserEvent({
                    type: 'PortfolioLoadedEvent',
                    installationId,
                    isFunded: isFunded({
                        portfolio: loadable.data.portfolio,
                        currencyHiddenMap: liveCurrencyHiddenMap.current,
                        defaultCurrencyConfig,
                    }),
                    dappCount: loadable.data.portfolio.apps.length,
                    nftCount: loadable.data.portfolio.nftCollections.length,
                    tokenCount: loadable.data.portfolio.tokens.filter(
                        (t) => !t.scam
                    ).length,
                    keystoreType: keystoreToUserEventType(keystore),
                    keystoreId: keystore.id,
                    tz:
                        tryToGetUserCurrentCountry().getSuccessResult()?.code ||
                        null,
                })
                break
            case 'loading':
                postUserEvent({
                    type: 'PortfolioLoadingEvent',
                    installationId,
                    keystoreId: keystore.id,
                    keystoreType: keystoreToUserEventType(keystore),
                })
                break
            case 'error':
                captureError(loadable.error)
                postUserEvent({
                    type: 'PortfolioLoadingFailedEvent',
                    installationId,
                    keystoreId: keystore.id,
                    keystoreType: keystoreToUserEventType(keystore),
                })
                break
            case 'reloading':
                break
            case 'subsequent_failed':
                captureError(loadable.error)
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [
        liveSelectedAddress,
        installationId,
        liveStorage,
        loadable,
        liveCurrencyHiddenMap,
        defaultCurrencyConfig,
    ])

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'error':
            case 'subsequent_failed':
            case 'reloading':
                break
            case 'loaded':
                liveOnMsg.current({
                    type: 'portfolio_loaded',
                    address: loadable.params.address,
                    portfolio: loadable.data.portfolio,
                    fetchedAt: loadable.data.fetchedAt,
                })
                break
            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [liveOnMsg, loadable])

    const visibility = useVisibility()
    useEffect(() => {
        switch (visibility.current) {
            case 'hidden':
                break
            case 'visible':
                switch (visibility.previous) {
                    case 'hidden':
                        refreshPortfolio()
                        break
                    case 'visible':
                        break
                    default:
                        notReachable(visibility.previous)
                }
                break
            default:
                notReachable(visibility.current)
        }
    }, [refreshPortfolio, visibility])

    useEffect(() => {
        switch (ZealPlatform.OS) {
            case 'ios':
            case 'android':
                return Messaging().onMessage((remoteMessage) => {
                    parseNotification(remoteMessage.data?.notification).tap(
                        (notification) => {
                            switch (notification.type) {
                                case 'address_receive_token_notification':
                                case 'address_receive_monerium_bank_transfer_notification':
                                    refreshPortfolio()
                                    break
                                case 'address_send_token_notification':
                                case 'card_spend':
                                case 'card_topup':
                                case 'card_refund':
                                case 'card_cashback_reward':
                                case 'intercom_reply_notification':
                                case 'user_a_claimable_reward_notification':
                                    break
                                default:
                                    notReachable(notification)
                            }
                        }
                    )
                })
            case 'web':
                break
            default:
                notReachable(ZealPlatform)
        }
    }, [refreshPortfolio])

    const account = storage.accounts[loadable.params.address]
    if (!account) {
        // as mention this is tricky part
        // if delete currently selected account, params will have current account but storage will have already new one
        // since params have current account and you already delete cashed values, this will throw
        return null
    }
    return (
        <>
            <NotificationActionsListener
                historicalTakerUserCurrencyRateMap={
                    storage.earnHistoricalTakerUserCurrencyRateMap
                }
                networkRPCMap={storage.networkRPCMap}
                referralConfig={referralConfig}
                customCurrencies={storage.customCurrencies}
                keyStoreMap={storage.keystoreMap}
                networkMap={networkMap}
                networkRpcMap={storage.networkRPCMap}
                sessionPassword={sessionPassword}
                feePresetMap={storage.feePresetMap}
                gasCurrencyPresetMap={storage.gasCurrencyPresetMap}
                portfolioMap={storage.portfolios}
                appRating={storage.appRating}
                celebrationConfig={storage.celebrationConfig}
                currencyPinMap={storage.currencyPinMap}
                accountsMap={storage.accounts}
                cardConfig={storage.cardConfig}
                currencyHiddenMap={storage.currencyHiddenMap}
                defaultCurrencyConfig={storage.defaultCurrencyConfig}
                installationId={installationId}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'import_keys_button_clicked':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_get_cashback_currency_clicked':
                        case 'import_card_owner_clicked':
                        case 'on_app_rating_submitted':
                        case 'on_cashback_celebration_triggered':
                        case 'on_a_reward_claimed_successfully':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'cancel_submitted':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'recover_safe_wallet_clicked':
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'on_swaps_io_swap_request_created':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                            onMsg(msg)
                            break

                        case 'on_earn_deposit_success':
                        case 'on_earn_configured':
                            if (
                                msg.earnOwner.address ===
                                loadable.params.address
                            ) {
                                setLoadable((old) => {
                                    switch (old.type) {
                                        case 'loaded':
                                        case 'reloading':
                                        case 'subsequent_failed':
                                            return {
                                                type: 'reloading',
                                                params: old.params,
                                                data: {
                                                    ...old.data,
                                                    portfolio: {
                                                        ...old.data.portfolio,
                                                        earn: msg.configuredEarn,
                                                    },
                                                },
                                            }

                                        case 'error':
                                        case 'loading':
                                            return {
                                                type: 'loading',
                                                params: old.params,
                                            }
                                        default:
                                            return notReachable(old)
                                    }
                                })
                            }
                            break
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_swap_success_clicked':
                        case 'transaction_submited':
                            setLoadable((old) => {
                                switch (old.type) {
                                    case 'loaded':
                                    case 'reloading':
                                    case 'subsequent_failed':
                                        return {
                                            type: 'reloading',
                                            params: old.params,
                                            data: old.data,
                                        }
                                    case 'error':
                                    case 'loading':
                                        return {
                                            type: 'loading',
                                            params: old.params,
                                        }
                                    default:
                                        return notReachable(old)
                                }
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            {(() => {
                switch (loadable.type) {
                    case 'error':
                        return (
                            <>
                                <LoadingLayout
                                    actionBar={null}
                                    onClose={null}
                                    title={null}
                                />

                                <AppErrorPopup
                                    error={parseAppError(loadable.error)}
                                    installationId={installationId}
                                    onMsg={(msg) => {
                                        switch (msg.type) {
                                            case 'close':
                                            case 'try_again_clicked':
                                                setLoadable((old) => ({
                                                    type: 'loading',
                                                    params: old.params,
                                                }))
                                                break

                                            /* istanbul ignore next */
                                            default:
                                                notReachable(msg)
                                        }
                                    }}
                                />
                            </>
                        )

                    case 'loading':
                        return (
                            <LoadingLayout
                                actionBar={null}
                                onClose={null}
                                title={null}
                            />
                        )

                    case 'loaded':
                    case 'reloading':
                    case 'subsequent_failed':
                        return (
                            <FeatureFork
                                referralConfig={referralConfig}
                                appRating={appRating}
                                installationCampaign={installationCampaign}
                                celebrationConfig={celebrationConfig}
                                refreshContainerState={refreshContainerState}
                                key={
                                    storage.defaultCurrencyConfig
                                        .defaultCurrency.id
                                }
                                defaultCurrencyConfig={
                                    storage.defaultCurrencyConfig
                                }
                                initialActiveTab={initialActiveTab}
                                totalEarningsInDefaultCurrencyMap={
                                    storage.totalEarningsInDefaultCurrencyMap
                                }
                                earnHistoricalTakerUserCurrencyRateMap={
                                    earnHistoricalTakerUserCurrencyRateMap
                                }
                                swapsIOSwapRequestsMap={
                                    storage.swapsIOSwapRequestsMap
                                }
                                transactionActivitiesCacheMap={
                                    storage.transactionActivitiesCacheMap
                                }
                                earnTakerMetrics={earnTakerMetrics}
                                isEthereumNetworkFeeWarningSeen={
                                    isEthereumNetworkFeeWarningSeen
                                }
                                notificationsConfig={
                                    storage.notificationsConfig
                                }
                                browserTabState={browserTabState}
                                cardConfig={cardConfig}
                                walletConnectInstanceLoadable={
                                    walletConnectInstanceLoadable
                                }
                                mode={mode}
                                currencyHiddenMap={currencyHiddenMap}
                                currencyPinMap={currencyPinMap}
                                networkMap={networkMap}
                                installationId={installationId}
                                customCurrencyMap={storage.customCurrencies}
                                submitedBridgesMap={storage.submitedBridges}
                                connections={connections}
                                encryptedPassword={storage.encryptedPassword}
                                sessionPassword={sessionPassword}
                                keyStoreMap={storage.keystoreMap}
                                networkRPCMap={storage.networkRPCMap}
                                transactionRequests={
                                    storage.transactionRequests
                                }
                                submittedOffRampTransactions={
                                    storage.submittedOffRampTransactions
                                }
                                portfolioLoadable={loadable}
                                account={account}
                                portfolioMap={storage.portfolios}
                                accountsMap={storage.accounts}
                                bankTransferInfo={storage.bankTransferInfo}
                                feePresetMap={storage.feePresetMap}
                                gasCurrencyPresetMap={
                                    storage.gasCurrencyPresetMap
                                }
                                appBrowserProviderScript={
                                    appBrowserProviderScript
                                }
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_tokens_refresh_pulled':
                                        case 'on_portfolio_refresh_pulled':
                                        case 'on_card_onboarded_state_refresh_pulled':
                                        case 'on_refresh_button_clicked':
                                            setRefreshContainerState(
                                                'refreshing'
                                            )
                                            break

                                        case 'reload_button_click':
                                        case 'on_earn_withdrawal_success':
                                        case 'on_onramp_success':
                                        case 'on_top_up_transaction_complete_close':
                                            refreshPortfolio()
                                            break
                                        case 'on_earn_deposit_success':
                                            if (
                                                msg.earnOwner.address ===
                                                loadable.params.address
                                            ) {
                                                setLoadable((old) => {
                                                    switch (old.type) {
                                                        case 'loaded':
                                                        case 'reloading':
                                                        case 'subsequent_failed':
                                                            return {
                                                                type: 'reloading',
                                                                params: old.params,
                                                                data: {
                                                                    ...old.data,
                                                                    portfolio: {
                                                                        ...old
                                                                            .data
                                                                            .portfolio,
                                                                        earn: msg.configuredEarn,
                                                                    },
                                                                },
                                                            }

                                                        case 'error':
                                                        case 'loading':
                                                            return {
                                                                type: 'loading',
                                                                params: old.params,
                                                            }
                                                        default:
                                                            return notReachable(
                                                                old
                                                            )
                                                    }
                                                })
                                            }
                                            onMsg(msg)
                                            break
                                        case 'on_earn_configured':
                                            if (
                                                msg.earnOwner.address ===
                                                loadable.params.address
                                            ) {
                                                setLoadable((old) => {
                                                    switch (old.type) {
                                                        case 'loaded':
                                                        case 'reloading':
                                                        case 'subsequent_failed':
                                                            return {
                                                                type: 'reloading',
                                                                params: old.params,
                                                                data: {
                                                                    ...old.data,
                                                                    portfolio: {
                                                                        ...old
                                                                            .data
                                                                            .portfolio,
                                                                        earn: msg.configuredEarn,
                                                                    },
                                                                },
                                                            }

                                                        case 'error':
                                                        case 'loading':
                                                            return {
                                                                type: 'loading',
                                                                params: old.params,
                                                            }
                                                        default:
                                                            return notReachable(
                                                                old
                                                            )
                                                    }
                                                })
                                            }
                                            break
                                        case 'track_wallet_clicked':
                                        case 'add_wallet_clicked':
                                        case 'hardware_wallet_clicked':
                                        case 'on_account_create_request':
                                        case 'on_profile_change_confirm_click':
                                        case 'account_item_clicked':
                                        case 'confirm_account_delete_click':
                                        case 'on_rewards_warning_confirm_account_delete_click':
                                        case 'transaction_request_completed':
                                        case 'transaction_request_failed':
                                        case 'on_account_label_change_submit':
                                        case 'on_lock_zeal_click':
                                        case 'on_recovery_kit_setup':
                                        case 'on_disconnect_dapps_click':
                                        case 'on_delete_all_dapps_confirm_click':
                                        case 'on_send_nft_click':
                                        case 'on_swap_clicked':
                                        case 'on_bridge_clicked':
                                        case 'on_send_clicked':
                                        case 'on_bank_transfer_selected':
                                        case 'on_dismiss_kyc_button_clicked':
                                        case 'on_kyc_try_again_clicked':
                                        case 'on_token_pin_click':
                                        case 'on_token_un_pin_click':
                                        case 'on_token_hide_click':
                                        case 'on_token_un_hide_click':
                                        case 'transaction_submited':
                                        case 'cancel_submitted':
                                        case 'on_rpc_change_confirmed':
                                        case 'on_select_rpc_click':
                                        case 'on_transaction_completed_splash_animation_screen_competed':
                                        case 'on_withdrawal_monitor_fiat_transaction_success':
                                        case 'on_add_private_key_click':
                                        case 'safe_wallet_clicked':
                                        case 'recover_safe_wallet_clicked':
                                        case 'transaction_request_replaced':
                                        case 'on_zwidget_expand_request':
                                        case 'on_card_onboarded_account_state_received':
                                        case 'on_card_imported_success_animation_complete':
                                        case 'on_onboarded_card_imported_success_animation_complete':
                                        case 'transaction_failure_accepted':
                                        case 'on_4337_auto_gas_token_selection_clicked':
                                        case 'on_4337_gas_currency_selected':
                                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                        case 'import_keys_button_clicked':
                                        case 'on_predefined_fee_preset_selected':
                                        case 'transaction_cancel_failure_accepted':
                                        case 'on_browser_url_change':
                                        case 'on_card_transactions_fetch_success':
                                        case 'on_notifications_config_changed':
                                        case 'on_card_disconnected':
                                        case 'on_earn_last_recharge_transaction_hash_loaded':
                                        case 'on_custom_currency_update_request':
                                        case 'on_custom_currency_delete_request':
                                        case 'on_open_fullscreen_view_click':
                                        case 'on_get_cashback_currency_clicked':
                                        case 'on_cashback_loaded':
                                        case 'import_card_owner_clicked':
                                        case 'on_switch_card_new_card_selected':
                                        case 'on_earnings_fetched':
                                        case 'on_historical_taker_user_currency_rate_fetched':
                                        case 'on_swaps_io_swap_requests_fetched':
                                        case 'on_swaps_io_swap_request_created':
                                        case 'on_swaps_io_transaction_activity_swap_started':
                                        case 'on_swaps_io_transaction_activity_completed':
                                        case 'on_swaps_io_transaction_activity_failed':
                                        case 'on_default_currency_selected':
                                        case 'on_card_import_on_import_keys_clicked':
                                        case 'on_create_smart_wallet_clicked':
                                        case 'on_address_scanned':
                                        case 'on_address_scanned_and_add_label':
                                        case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                        case 'on_add_funds_click':
                                        case 'on_accounts_create_success_animation_finished':
                                        case 'on_add_label_to_track_only_account_during_send':
                                        case 'on_ethereum_network_fee_warning_understand_clicked':
                                        case 'on_switch_bank_transfer_provider_clicked':
                                        case 'on_buy_clicked':
                                        case 'on_meta_mask_mode_changed_pupup_refresh_page_clicked':
                                        case 'on_dismiss_bridge_widget_click':
                                        case 'on_usd_taker_metrics_loaded':
                                        case 'on_eur_taker_metrics_loaded':
                                        case 'on_gnosis_pay_account_created':
                                        case 'on_do_bank_transfer_clicked':
                                        case 'on_dissmiss_card_kyc_onboarding_widget_clicked':
                                        case 'on_app_rating_submitted':
                                        case 'on_cashback_celebration_triggered':
                                        case 'on_earn_celebration_triggered':
                                        case 'on_new_virtual_card_created_successfully':
                                        case 'on_dismiss_add_to_wallet_banner_clicked':
                                        case 'on_transaction_activities_loaded':
                                        case 'on_virtual_card_order_created_animation_completed':
                                        case 'on_dissmiss_card_kyc_onboarded_widget_clicked':
                                        case 'on_card_b_reward_dissmiss_clicked':
                                        case 'card_breward_claimed':
                                        case 'card_brewards_updated':
                                        case 'on_pending_breward_claim_transaction_activity_failed':
                                        case 'on_pending_send_transaction_activity_failed':
                                        case 'on_pending_areward_claim_transaction_activity_completed':
                                        case 'on_pending_areward_claim_transaction_activity_failed':
                                        case 'on_a_reward_claimed_successfully':
                                        case 'on_a_rewards_configured':
                                            onMsg(msg)
                                            break

                                        case 'on_physical_card_activated_info_screen_closed':
                                            const funded =
                                                loadable.data.portfolio &&
                                                (hasTokens({
                                                    currencyHiddenMap,
                                                    portfolio:
                                                        portfolioToPortfolio2({
                                                            portfolio:
                                                                loadable.data
                                                                    .portfolio,
                                                        }),
                                                }) ||
                                                    sumEarn({
                                                        earn: loadable.data
                                                            .portfolio.earn,
                                                        defaultCurrencyConfig,
                                                    }).amount > FIAT_DUST)
                                            if (!funded) {
                                                onMsg({
                                                    type: 'on_physical_card_activated_info_screen_closed_wallet_not_funded',
                                                    address: loadable.params
                                                        .address as Web3.address.Address,
                                                    card: msg.card,
                                                })
                                                break
                                            }
                                            onMsg(msg)
                                            break
                                        case 'on_pending_send_transaction_activity_completed':
                                        case 'on_pending_breward_claim_transaction_activity_completed':
                                        case 'bridge_completed':
                                            setLoadable((old) => {
                                                switch (old.type) {
                                                    case 'loaded':
                                                    case 'reloading':
                                                    case 'subsequent_failed':
                                                        return {
                                                            type: 'reloading',
                                                            params: old.params,
                                                            data: old.data,
                                                        }
                                                    case 'error':
                                                    case 'loading':
                                                        return {
                                                            type: 'loading',
                                                            params: old.params,
                                                        }
                                                    default:
                                                        return notReachable(old)
                                                }
                                            })
                                            onMsg(msg)
                                            break

                                        case 'on_earn_recharge_configured':
                                        case 'on_reacharege_configured_with_user_preferences':
                                            setLoadable((old) => {
                                                switch (old.type) {
                                                    case 'loaded':
                                                    case 'reloading':
                                                    case 'subsequent_failed':
                                                        return {
                                                            type: 'reloading',
                                                            params: old.params,
                                                            data: {
                                                                ...old.data,
                                                                earn: msg.earn,
                                                            },
                                                        }
                                                    case 'error':
                                                    case 'loading':
                                                        return {
                                                            type: 'loading',
                                                            params: old.params,
                                                        }
                                                    default:
                                                        return notReachable(old)
                                                }
                                            })
                                            break

                                        case 'on_earn_updated':
                                            if (
                                                msg.ownerAddress ===
                                                loadable.params.address
                                            ) {
                                                setLoadable((old) => {
                                                    switch (old.type) {
                                                        case 'loaded':
                                                            return {
                                                                type: 'loaded',
                                                                params: old.params,
                                                                data: {
                                                                    ...old.data,
                                                                    portfolio: {
                                                                        ...old
                                                                            .data
                                                                            .portfolio,
                                                                        earn: msg.earn,
                                                                    },
                                                                },
                                                            }
                                                        case 'loading':
                                                        case 'reloading':
                                                        case 'subsequent_failed':
                                                        case 'error':
                                                            return old
                                                        default:
                                                            return notReachable(
                                                                old
                                                            )
                                                    }
                                                })
                                                break
                                            }
                                            onMsg(msg)
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )

                    default:
                        return notReachable(loadable)
                }
            })()}
        </>
    )
}
