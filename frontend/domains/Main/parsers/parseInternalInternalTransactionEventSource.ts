import { failure, Result, string, success } from '@zeal/toolkit/Result'

import { InternalTransactionEventSource } from '..'

const InternalTransactionEventSourceMap: Record<
    InternalTransactionEventSource,
    true
> = {
    bridge: true,
    bridgeApprove: true,
    cardAddCash: true,
    cardCashBackDeposit: true,
    earnAddTakerToHolder: true,
    earnDeposit: true,
    earnHolderDeploy: true,
    earnDisableRecharge: true,
    earnEnableRecharge: true,
    earnTakerDeploy: true,
    earnWithdrawal: true,
    kinetexApprove: true,
    kinetexNativeSwap: true,
    offramp: true,
    send: true,
    swap: true,
    swapApprove: true,
    topupCardFromEarn: true,
    topupSend: true,
    transactionRequestWidget: true,
}

export const parseInternalInternalTransactionEventSource = (
    input: unknown
): Result<unknown, InternalTransactionEventSource> =>
    string(input).andThen((str) =>
        InternalTransactionEventSourceMap[str as InternalTransactionEventSource]
            ? success(str as InternalTransactionEventSource)
            : failure(`${str} is not a valid InternalTransactionEventSource`)
    )
