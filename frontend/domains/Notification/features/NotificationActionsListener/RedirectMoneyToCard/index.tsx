import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { ConfiguredEarn, DeployedTaker } from '@zeal/domains/Earn'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { fetchData } from './api/fetchData'
import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    toAddress: Address
    sendTo:
        | {
              type: 'earn'
              earn: ConfiguredEarn
              taker: DeployedTaker
          }
        | {
              type: 'card'
          }

    recievedMoney: CryptoMoney
    cardWalletKeySore: CardSlientSignKeyStore
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig

    defaultCurrencyConfig: DefaultCurrencyConfig

    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'cancel_submitted'
                  | 'on_completed_transaction_close_click'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_swap_created_close_clicked'
                  | 'on_swaps_io_swap_request_created'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_earn_configured'
                  | 'on_earn_deposit_success'
          }
      >
export const RedirectMoneyToCard = ({
    toAddress,
    sessionPassword,
    sendTo,
    recievedMoney,
    portfolioMap,
    onMsg,
    networkRPCMap,
    networkMap,
    keyStoreMap,
    installationId,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    customCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    cardConfig,
    accountsMap,
    cardWalletKeySore,
}: Props) => {
    const account = accountsMap[toAddress]
    const [rateLoadable, _setRateLoadable] = useLoadableData(fetchRate, {
        type: 'loading',
        params: {
            cryptoCurrency: recievedMoney.currency,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
        },
    })
    const [loadable, setLoadable] = useLoadableData(fetchData, {
        type: 'loading',
        params: {
            account,
            cardConfig,
            currencyHiddenMap,
            customCurrencies,
            defaultCurrencyConfig,
            installationId,
            networkMap,
            networkRPCMap,
            cardWalletKeySore,
            sessionPassword,
            cacheKey: uuid(),
        },
    })
    const [modalState, setModalState] = useState<ModalState>({ type: 'closed' })

    return (
        <>
            <Layout
                networkMap={networkMap}
                recievedMoney={recievedMoney}
                rate={rateLoadable}
                sendTo={sendTo}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_redirect_to_card_clicked':
                            setModalState({ type: 'redirect_to_card' })
                            break
                        case 'on_redirect_to_earn_clicked':
                            setModalState({
                                type: 'redirect_to_earn',
                                earn: msg.earn,
                                taker: msg.taker,
                            })
                            break
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                recievedMoney={recievedMoney}
                sessionPassword={sessionPassword}
                portfolioMap={portfolioMap}
                customCurrencies={customCurrencies}
                defaultCurrencyConfig={defaultCurrencyConfig}
                feePresetMap={feePresetMap}
                currencyPinMap={currencyPinMap}
                gasCurrencyPresetMap={gasCurrencyPresetMap}
                initialSender={account}
                installationId={installationId}
                currencyHiddenMap={currencyHiddenMap}
                keyStoreMap={keyStoreMap}
                cardConfig={cardConfig}
                loadable={loadable}
                accountsMap={accountsMap}
                networkMap={networkMap}
                networkRPCMap={networkRPCMap}
                state={modalState}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModalState({ type: 'closed' })
                            break
                        case 'on_select_rpc_click':
                        case 'on_rpc_change_confirmed':
                        case 'import_keys_button_clicked':
                        case 'on_4337_auto_gas_token_selection_clicked':
                        case 'on_4337_gas_currency_selected':
                        case 'on_completed_safe_transaction_close_click':
                        case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                        case 'on_predefined_fee_preset_selected':
                        case 'on_transaction_cancelled_successfully_close_clicked':
                        case 'cancel_submitted':
                        case 'on_completed_transaction_close_click':
                        case 'on_transaction_completed_splash_animation_screen_competed':
                        case 'transaction_request_replaced':
                        case 'transaction_submited':
                        case 'on_swap_cancelled_close_clicked':
                        case 'on_swap_success_clicked':
                        case 'on_swap_created_close_clicked':
                        case 'on_swaps_io_swap_request_created':
                        case 'track_wallet_clicked':
                        case 'on_account_create_request':
                        case 'add_wallet_clicked':
                        case 'hardware_wallet_clicked':
                        case 'safe_wallet_clicked':
                        case 'recover_safe_wallet_clicked':
                        case 'on_earn_configured':
                        case 'on_earn_deposit_success':
                            onMsg(msg)
                            break

                        case 'on_account_selected':
                        case 'on_sender_selected':
                            setLoadable({
                                type: 'loading',
                                params: {
                                    ...loadable.params,
                                    account: msg.account,
                                },
                            })
                            break

                        case 'on_redirect_to_card_try_again_clicked':
                        case 'on_redirect_to_earn_try_again_clicked':
                            setLoadable({
                                type: 'loading',
                                params: {
                                    ...loadable.params,
                                    cacheKey: uuid(),
                                },
                            })
                            break

                        default:
                            return notReachable(msg)
                    }
                }}
            />
        </>
    )
}
