import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { TopUpFlow } from '@zeal/domains/Card/features/TopUpFlow'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Data } from '../api/fetchData'

type Props = {
    initialAmountToTopup: CryptoMoney | null
    loadable: LoadableData<Data, unknown>

    initialSender: Account

    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig

    defaultCurrencyConfig: DefaultCurrencyConfig

    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof TopUpFlow>,
          {
              type:
                  | 'close'
                  | 'on_select_rpc_click'
                  | 'on_rpc_change_confirmed'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_completed_safe_transaction_close_click'
                  | 'on_safe_4337_transaction_completed_splash_animation_screen_competed'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_transaction_cancelled_successfully_close_clicked'
                  | 'cancel_submitted'
                  | 'on_completed_transaction_close_click'
                  | 'on_transaction_completed_splash_animation_screen_competed'
                  | 'transaction_request_replaced'
                  | 'transaction_submited'
                  | 'on_swap_cancelled_close_clicked'
                  | 'on_swap_success_clicked'
                  | 'on_swap_created_close_clicked'
                  | 'on_swaps_io_swap_request_created'
                  | 'track_wallet_clicked'
                  | 'on_account_create_request'
                  | 'add_wallet_clicked'
                  | 'hardware_wallet_clicked'
                  | 'safe_wallet_clicked'
                  | 'recover_safe_wallet_clicked'
                  | 'on_sender_selected'
          }
      >
    | { type: 'close' }
    | { type: 'on_redirect_to_card_try_again_clicked' }

export const RedirectToCard = ({
    initialAmountToTopup,
    loadable,
    sessionPassword,
    portfolioMap,
    onMsg,
    networkRPCMap,
    networkMap,
    keyStoreMap,
    installationId,
    initialSender,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    customCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    cardConfig,
    accountsMap,
}: Props) => {
    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    onMsg({
                                        type: 'on_redirect_to_card_try_again_clicked',
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loaded':
            return (
                <TopUpFlow
                    initialAmountToTopup={initialAmountToTopup}
                    portfolio={loadable.data.portfolio}
                    sender={initialSender}
                    installationId={installationId}
                    supportedTopupCurrencies={
                        loadable.data.supportedTopupCurrencies
                    }
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    customCurrencies={customCurrencies}
                    sessionPassword={sessionPassword}
                    portfolioMap={portfolioMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    contractsMap={loadable.data.contractsMap}
                    networkMap={networkMap}
                    cardConfig={cardConfig}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardBalance={loadable.data.cardBalance}
                    gnosisPayAccountOnboardedState={
                        loadable.data.gnosisPayAccountOnboardedState
                    }
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    onMsg={onMsg}
                />
            )

        default:
            return notReachable(loadable)
    }
}
