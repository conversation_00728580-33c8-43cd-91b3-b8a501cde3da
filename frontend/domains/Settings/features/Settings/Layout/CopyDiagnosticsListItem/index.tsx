import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Avatar, Badge } from '@zeal/uikit/Avatar'
import { ClipBoard } from '@zeal/uikit/Icon/ClipBoard'
import { Copy } from '@zeal/uikit/Icon/Copy'
import { ListItem } from '@zeal/uikit/ListItem'

import { notReachable } from '@zeal/toolkit'
import { stringify } from '@zeal/toolkit/JSON'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'

import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { fetchDiagnostics } from '@zeal/domains/Storage/api/fetchDiagnostics'

import { Layout } from './Layout'

const fetch = async (): Promise<string> => {
    const diagnostics = await fetchDiagnostics()
    return stringify(diagnostics)
}

export const CopyDiagnosticsListItem = () => {
    const [loadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {},
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'loaded':
                break
            case 'error':
                captureError(loadable.error)
                break
            default:
                return notReachable(loadable)
        }
    }, [loadable])

    switch (loadable.type) {
        case 'loading':
        case 'error':
            return (
                <ListItem
                    size="regular"
                    aria-current={false}
                    avatar={({ size }) => (
                        <Avatar
                            size={size}
                            leftBadge={({ size }) => (
                                <Badge
                                    backgroundColor="gray100"
                                    outlineColor="gray100"
                                    size={size}
                                >
                                    <Copy size={size} color="teal40" />
                                </Badge>
                            )}
                        >
                            <ClipBoard size={size} color="teal40" />
                        </Avatar>
                    )}
                    primaryText={
                        <FormattedMessage
                            id="copy-diagnostics"
                            defaultMessage="Copy diagnostics"
                        />
                    }
                    disabled
                />
            )
        case 'loaded':
            return <Layout diagnosticsString={loadable.data} />
        default:
            return notReachable(loadable)
    }
}
