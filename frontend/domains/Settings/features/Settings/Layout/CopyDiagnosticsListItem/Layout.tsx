import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Avatar, Badge } from '@zeal/uikit/Avatar'
import { ClipBoard } from '@zeal/uikit/Icon/ClipBoard'
import { Copy } from '@zeal/uikit/Icon/Copy'
import { Tick } from '@zeal/uikit/Icon/Tick'
import { ListItem } from '@zeal/uikit/ListItem'
import { Modal } from '@zeal/uikit/Modal'
import { Toast, ToastContainer, ToastText } from '@zeal/uikit/Toast'

import { notReachable } from '@zeal/toolkit'
import { useCopyTextToClipboard } from '@zeal/toolkit/Clipboard/hooks/useCopyTextToClipboard'

import { captureError } from '@zeal/domains/Error/helpers/captureError'

type Props = {
    diagnosticsString: string
}

export const Layout = ({ diagnosticsString }: Props) => {
    const [state, setState] = useCopyTextToClipboard()

    useEffect(() => {
        switch (state.type) {
            case 'loaded':
            case 'loading':
            case 'not_asked':
                break
            case 'error':
                captureError(state.error)
                break
            default:
                notReachable(state)
        }
    }, [state])

    switch (state.type) {
        case 'not_asked':
        case 'loading':
            return (
                <ListItem
                    size="regular"
                    aria-current={false}
                    avatar={({ size }) => (
                        <Avatar
                            size={size}
                            leftBadge={({ size }) => (
                                <Badge
                                    backgroundColor="gray100"
                                    outlineColor="gray100"
                                    size={size}
                                >
                                    <Copy size={size} color="teal40" />
                                </Badge>
                            )}
                        >
                            <ClipBoard size={size} color="teal40" />
                        </Avatar>
                    )}
                    primaryText={
                        <FormattedMessage
                            id="copy-diagnostics"
                            defaultMessage="Copy diagnostics"
                        />
                    }
                    onClick={() => {
                        setState({
                            type: 'loading',
                            params: { stringToCopy: diagnosticsString },
                        })
                    }}
                    disabled={false}
                />
            )

        case 'error':
            return null

        case 'loaded':
            return (
                <>
                    <ListItem
                        size="regular"
                        aria-current={false}
                        avatar={({ size }) => (
                            <Avatar
                                size={size}
                                leftBadge={({ size }) => (
                                    <Badge
                                        backgroundColor="gray100"
                                        outlineColor="gray100"
                                        size={size}
                                    >
                                        <Copy size={size} color="teal40" />
                                    </Badge>
                                )}
                            >
                                <ClipBoard size={size} color="teal40" />
                            </Avatar>
                        )}
                        primaryText={
                            <FormattedMessage
                                id="copy-diagnostics"
                                defaultMessage="Copy diagnostics"
                            />
                        }
                        onClick={() => {
                            setState({
                                type: 'loading',
                                params: { stringToCopy: diagnosticsString },
                            })
                        }}
                        disabled={false}
                    />

                    <Modal>
                        <ToastContainer navOffset>
                            <Toast>
                                <Tick size={20} color="backgroundLight" />
                                <ToastText>
                                    <FormattedMessage
                                        id="copied-diagnostics"
                                        defaultMessage="Copied diagnostics"
                                    />
                                </ToastText>
                            </Toast>
                        </ToastContainer>
                    </Modal>
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
