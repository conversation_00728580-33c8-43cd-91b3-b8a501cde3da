import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { Group, Section } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Discord } from '@zeal/uikit/Icon/Discord'
import { Document } from '@zeal/uikit/Icon/Document'
import { Privacy } from '@zeal/uikit/Icon/Privacy'
import { Twitter } from '@zeal/uikit/Icon/Twitter'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import {
    getAppVersion,
    getEnvironment,
    isTestFlight,
} from '@zeal/toolkit/Environment'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { IntercomButton } from '@zeal/domains/Intercom/features/IntercomButton'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { Mode } from '@zeal/domains/Main'
import {
    DISCORD_URL,
    TWITTER_URL,
    ZEAL_PRIVACY_POLICY_URL,
    ZEAL_TERMS_OF_USE_URL,
} from '@zeal/domains/Main/constants'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { AccountsSection } from './AccountsSection'
import { CopyDiagnosticsListItem } from './CopyDiagnosticsListItem'
import { SettingsSection } from './SettingsSection'

type Props = {
    mode: Mode
    defaultCurrencyConfig: DefaultCurrencyConfig
    cardConfig: CardConfig
    accountsMap: AccountsMap
    selectedAccount: Account
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    keyStoreMap: KeyStoreMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof SettingsSection>
    | MsgOf<typeof AccountsSection>
    | { type: 'close' }

export const Layout = ({
    mode,
    defaultCurrencyConfig,
    cardConfig,
    accountsMap,
    currencyHiddenMap,
    portfolioMap,
    selectedAccount,
    installationId,
    keyStoreMap,
    onMsg,
}: Props) => {
    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} shrink fill>
                <ActionBar
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                    right={
                        <IntercomButton
                            variant="icon_button"
                            installationId={installationId}
                            location="settings"
                        />
                    }
                />

                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={16}>
                        <AccountsSection
                            keyStoreMap={keyStoreMap}
                            installationId={installationId}
                            accountsMap={accountsMap}
                            currencyHiddenMap={currencyHiddenMap}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            portfolioMap={portfolioMap}
                            selectedAccount={selectedAccount}
                            onMsg={onMsg}
                        />

                        <SettingsSection
                            mode={mode}
                            cardConfig={cardConfig}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            onMsg={onMsg}
                        />

                        <Section>
                            <Group variant="default">
                                <ListItem
                                    size="regular"
                                    aria-current={false}
                                    avatar={({ size }) => (
                                        <Discord
                                            size={size}
                                            color="iconAccent2"
                                        />
                                    )}
                                    primaryText={
                                        <FormattedMessage
                                            id="settings.discord"
                                            defaultMessage="Discord"
                                        />
                                    }
                                    onClick={() => {
                                        openExternalURL(DISCORD_URL)
                                    }}
                                />
                                <ListItem
                                    size="regular"
                                    aria-current={false}
                                    avatar={({ size }) => (
                                        <Twitter
                                            size={size}
                                            color="iconAccent2"
                                        />
                                    )}
                                    primaryText={
                                        <FormattedMessage
                                            id="settings.twitter"
                                            defaultMessage="𝕏 / Twitter"
                                        />
                                    }
                                    onClick={() => {
                                        openExternalURL(TWITTER_URL)
                                    }}
                                />
                            </Group>
                        </Section>

                        <Section>
                            <Group variant="default">
                                <ListItem
                                    size="regular"
                                    aria-current={false}
                                    avatar={({ size }) => (
                                        <Privacy
                                            size={size}
                                            color="iconAccent2"
                                        />
                                    )}
                                    primaryText={
                                        <FormattedMessage
                                            id="settings.privacyPolicy"
                                            defaultMessage="Privacy Policy"
                                        />
                                    }
                                    onClick={() => {
                                        openExternalURL(ZEAL_PRIVACY_POLICY_URL)
                                    }}
                                />

                                <ListItem
                                    size="regular"
                                    aria-current={false}
                                    avatar={({ size }) => (
                                        <Document
                                            size={size}
                                            color="iconAccent2"
                                        />
                                    )}
                                    primaryText={
                                        <FormattedMessage
                                            id="settings.termsOfUse"
                                            defaultMessage="Terms of Use"
                                        />
                                    }
                                    onClick={() => {
                                        openExternalURL(ZEAL_TERMS_OF_USE_URL)
                                    }}
                                />
                                <CopyDiagnosticsListItem />
                            </Group>
                        </Section>

                        <Row spacing={0} alignX="center">
                            <Text
                                variant="caption1"
                                weight="regular"
                                color="textSecondary"
                                align="center"
                            >
                                <FormattedMessage
                                    id="settings.version"
                                    defaultMessage="Version {version} env: {env}"
                                    values={{
                                        version: getAppVersion(),
                                        env: isTestFlight
                                            ? 'TestFlight'
                                            : getEnvironment(),
                                    }}
                                />
                            </Text>
                        </Row>
                    </Column>
                </ScrollContainer>
            </Column>
        </Screen>
    )
}
