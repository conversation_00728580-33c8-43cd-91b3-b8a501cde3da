import {
    arrayOf,
    match,
    nullableOf,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    ValidObject,
} from '@zeal/toolkit/Result'

import { parseFiatMoneyFromStorage } from '@zeal/domains/Money/helpers/parse'
import { parseNetworkHexId } from '@zeal/domains/Network/helpers/parse'
import { parseToken2 } from '@zeal/domains/Token/helpers/parse'

import {
    App2,
    AppNft,
    AppProtocol2,
    CommonProtocol2,
    Lending2,
    LockedToken2,
    UnknownProtocol2,
    Vesting2,
} from '..'

const parseCommon = (input: ValidObject): Result<unknown, CommonProtocol2> => {
    return shape({
        type: match(input.type, 'CommonAppProtocol' as const),

        priceInDefaultCurrency: nullableOf(
            input.priceInDefaultCurrency,
            parseFiatMoneyFromStorage
        ),

        suppliedTokens: arrayOf(input.suppliedTokens, parseToken2),
        netValue: parseFiatMoneyFromStorage(input.netValue),
        borrowedTokens: arrayOf(input.borrowedTokens, parseToken2),
        rewardTokens: arrayOf(input.rewardTokens, parseToken2),
        category: string(input.category),
        description: nullableOf(input.description, string),
    })
}

const parseLocked2 = (input: ValidObject): Result<unknown, LockedToken2> => {
    return shape({
        type: match(input.type, 'LockedTokenAppProtocol' as const),
        priceInDefaultCurrency: nullableOf(
            input.priceInDefaultCurrency,
            parseFiatMoneyFromStorage
        ),
        netValue: parseFiatMoneyFromStorage(input.netValue),
        lockedTokens: arrayOf(input.lockedTokens, parseToken2),
        rewardTokens: arrayOf(input.rewardTokens, parseToken2),
        unlockAt: number(input.unlockAt),
        category: string(input.category),
        description: nullableOf(input.description, string),
    })
}

export const parseLending2 = (i: ValidObject): Result<unknown, Lending2> => {
    return shape({
        type: match(i.type, 'LendingAppProtocol' as const),
        netValue: parseFiatMoneyFromStorage(i.netValue),
        priceInDefaultCurrency: nullableOf(
            i.priceInDefaultCurrency,
            parseFiatMoneyFromStorage
        ),
        suppliedTokens: arrayOf(i.suppliedTokens, parseToken2),
        borrowedTokens: arrayOf(i.borrowedTokens, parseToken2),
        rewardTokens: arrayOf(i.rewardTokens, parseToken2),
        category: string(i.category),
        healthFactor: number(i.healthFactor),
    })
}

export const parseVesting2 = (i: ValidObject): Result<unknown, Vesting2> => {
    return shape({
        type: match(i.type, 'VestingAppProtocol' as const),
        netValue: parseFiatMoneyFromStorage(i.netValue),
        priceInDefaultCurrency: nullableOf(
            i.priceInDefaultCurrency,
            parseFiatMoneyFromStorage
        ),
        vestedToken: parseToken2(i.vestedToken),
        claimableToken: parseToken2(i.claimableToken),
        category: string(i.category),
    })
}

const parseNFT = (i: unknown): Result<unknown, AppNft> => {
    return object(i).andThen((i) => {
        return shape({
            tokenId: string(i.tokenId),
            name: nullableOf(i.name, string),
            amount: string(i.amount),
            decimals: number(i.decimals),
            priceInDefaultCurrency: nullableOf(
                i.priceInDefaultCurrency,
                parseFiatMoneyFromStorage
            ),
            uri: nullableOf(i.uri, string),
        })
    })
}

export const parseUnknownProtocol2 = (
    i: ValidObject
): Result<unknown, UnknownProtocol2> => {
    return shape({
        type: match(i.type, 'UnknownAppProtocol' as const),
        netValue: parseFiatMoneyFromStorage(i.netValue),
        priceInDefaultCurrency: nullableOf(
            i.priceInDefaultCurrency,
            parseFiatMoneyFromStorage
        ),
        tokens: arrayOf(i.tokens, parseToken2),
        nfts: arrayOf(i.nfts, parseNFT),
        category: string(i.category),
    })
}

const parseAppProtocol = (i: unknown): Result<unknown, AppProtocol2> => {
    return object(i).andThen((i) => {
        return oneOf(i, [
            parseCommon(i),
            parseLocked2(i),
            parseVesting2(i),
            parseLending2(i),
            parseUnknownProtocol2(i),
        ])
    })
}

export const parseApp2FromStorage = (input: unknown): Result<unknown, App2> => {
    return object(input).andThen((i) =>
        shape({
            name: string(i.name),
            icon: nullableOf(i.icon, string),
            networkHexId: parseNetworkHexId(i.networkHexId),
            priceInDefaultCurrency: nullableOf(
                i.priceInDefaultCurrency,
                parseFiatMoneyFromStorage
            ),
            priceInUsd: parseFiatMoneyFromStorage(i.priceInUsd),
            url: nullableOf(i.url, string),
            protocols: arrayOf(i.protocols, parseAppProtocol),
        })
    )
}
