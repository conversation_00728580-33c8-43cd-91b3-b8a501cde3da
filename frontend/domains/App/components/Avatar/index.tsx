import { ComponentPropsWithoutRef } from 'react'

import { Avatar as UIAvatar, AvatarSize } from '@zeal/uikit/Avatar'
import { QuestionCircle } from '@zeal/uikit/Icon/QuestionCircle'
import { Img } from '@zeal/uikit/Img'

import { App2 } from '@zeal/domains/App'

type Props = {
    app: App2
    size: AvatarSize
    badge?: ComponentPropsWithoutRef<typeof UIAvatar>['rightBadge']
}

export const Avatar = ({ app, size, badge }: Props) => {
    return (
        <UIAvatar size={size} rightBadge={badge}>
            {app.icon ? (
                <Img size={size} src={app.icon} />
            ) : (
                <QuestionCircle size={size} color="iconDefault" />
            )}
        </UIAvatar>
    )
}
