import { keys } from '@zeal/toolkit/Object'
import * as Web3 from '@zeal/toolkit/Web3'
import { staticFromString } from '@zeal/toolkit/Web3/address'

import {
    CryptoCurrency,
    CurrencyId,
    currencyId,
    FiatCurrency,
    FiatCurrencyCode,
} from '@zeal/domains/Currency'
import { NetworkHexId } from '@zeal/domains/Network'
import {
    ARBITRUM,
    AURORA,
    AVALANCHE,
    BASE,
    BLAST,
    BSC,
    CRONOS,
    ETHEREUM,
    FANTOM,
    GNOSIS,
    LINEA,
    MANTLE,
    OPBNB,
    OPTIMISM,
    POLYGON,
    POLYGON_ZKEVM,
    ZKSYNC,
} from '@zeal/domains/Network/constants'

export const CURRENCY_STORAGE_CACHE_KEY = 'currencyCache'

export const SOCKET_NATIVE_TOKEN_ADDRESS = staticFromString(
    '******************************************'
)

export const FIAT_DUST: bigint = 10000000000000000n // fromFixedWithFraction('0.01', FIAT_CURRENCIES.USD.fraction) We use a fraction = 18

export const FIAT_CURRENCIES: Record<FiatCurrencyCode, FiatCurrency> = {
    GBP: {
        type: 'FiatCurrency',
        id: 'GBP',
        symbol: '£',
        code: 'GBP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'British Pound',
    },
    EUR: {
        type: 'FiatCurrency',
        id: 'EUR',
        symbol: '€',
        code: 'EUR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Euro',
    },
    NGN: {
        type: 'FiatCurrency',
        id: 'NGN',
        symbol: '₦',
        code: 'NGN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Nigerian Naira',
    },
    PLN: {
        type: 'FiatCurrency',
        id: 'PLN',
        symbol: 'zł',
        code: 'PLN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Polish Zloty',
    },
    USD: {
        type: 'FiatCurrency',
        id: 'USD',
        symbol: '$',
        code: 'USD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'USD',
    },
    ZWL: {
        type: 'FiatCurrency',
        id: 'ZWL',
        symbol: 'ZWL$',
        code: 'ZWL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Zimbabwean Dollar',
    },
    BMD: {
        type: 'FiatCurrency',
        id: 'ZWL',
        symbol: '$',
        code: 'BMD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Bermudian Dollar',
    },
    ZMK: {
        type: 'FiatCurrency',
        id: 'ZMK',
        symbol: 'ZK',
        code: 'ZMK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Zambian Kwacha',
    },
    ZAR: {
        type: 'FiatCurrency',
        id: 'ZAR',
        symbol: 'R',
        code: 'ZAR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'South African Rand',
    },
    YER: {
        type: 'FiatCurrency',
        id: 'YER',
        symbol: 'YR',
        code: 'YER',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Yemeni Rial',
    },
    XOF: {
        type: 'FiatCurrency',
        id: 'XOF',
        symbol: 'CFA',
        code: 'XOF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'CFA Franc BCEAO',
    },
    XAF: {
        type: 'FiatCurrency',
        id: 'XAF',
        symbol: 'FCFA',
        code: 'XAF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'CFA Franc BEAC',
    },
    VND: {
        type: 'FiatCurrency',
        id: 'VND',
        symbol: '₫',
        code: 'VND',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Vietnamese Dong',
    },
    VEF: {
        type: 'FiatCurrency',
        id: 'VEF',
        symbol: 'Bs.F',
        code: 'VEF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Venezuelan Bolívar',
    },
    UZS: {
        type: 'FiatCurrency',
        id: 'UZS',
        symbol: 'UZS',
        code: 'UZS',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Uzbekistan Som',
    },
    UYU: {
        type: 'FiatCurrency',
        id: 'UYU',
        symbol: '$U',
        code: 'UYU',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Uruguayan Peso',
    },
    UGX: {
        type: 'FiatCurrency',
        id: 'UGX',
        symbol: 'USh',
        code: 'UGX',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Ugandan Shilling',
    },
    UAH: {
        type: 'FiatCurrency',
        id: 'UAH',
        symbol: '₴',
        code: 'UAH',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Ukrainian Hryvnia',
    },
    TZS: {
        type: 'FiatCurrency',
        id: 'TZS',
        symbol: 'TSh',
        code: 'TZS',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Tanzanian Shilling',
    },
    TWD: {
        type: 'FiatCurrency',
        id: 'TWD',
        symbol: 'NT$',
        code: 'TWD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'New Taiwan Dollar',
    },
    TTD: {
        type: 'FiatCurrency',
        id: 'TTD',
        symbol: 'TT$',
        code: 'TTD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Trinidad and Tobago Dollar',
    },
    TRY: {
        type: 'FiatCurrency',
        id: 'TRY',
        symbol: '₺',
        code: 'TRY',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Turkish Lira',
    },
    TOP: {
        type: 'FiatCurrency',
        id: 'TOP',
        symbol: 'T$',
        code: 'TOP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Tongan Paʻanga',
    },
    TND: {
        type: 'FiatCurrency',
        id: 'TND',
        symbol: 'DT',
        code: 'TND',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Tunisian Dinar',
    },
    THB: {
        type: 'FiatCurrency',
        id: 'THB',
        symbol: '฿',
        code: 'THB',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Thai Baht',
    },
    SYP: {
        type: 'FiatCurrency',
        id: 'SYP',
        symbol: 'SY£',
        code: 'SYP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Syrian Pound',
    },
    SOS: {
        type: 'FiatCurrency',
        id: 'SOS',
        symbol: 'Ssh',
        code: 'SOS',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Somali Shilling',
    },
    SGD: {
        type: 'FiatCurrency',
        id: 'SGD',
        symbol: 'S$',
        code: 'SGD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Singapore Dollar',
    },
    SEK: {
        type: 'FiatCurrency',
        id: 'SEK',
        symbol: 'kr',
        code: 'SEK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Swedish Krona',
    },
    SDG: {
        type: 'FiatCurrency',
        id: 'SDG',
        symbol: 'SDG',
        code: 'SDG',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Sudanese Pound',
    },
    SAR: {
        type: 'FiatCurrency',
        id: 'SAR',
        symbol: 'SR',
        code: 'SAR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Saudi Riyal',
    },
    RWF: {
        type: 'FiatCurrency',
        id: 'RWF',
        symbol: 'RWF',
        code: 'RWF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Rwandan Franc',
    },
    RUB: {
        type: 'FiatCurrency',
        id: 'RUB',
        symbol: '₽',
        code: 'RUB',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Russian Ruble',
    },
    RSD: {
        type: 'FiatCurrency',
        id: 'RSD',
        symbol: 'din.',
        code: 'RSD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Serbian Dinar',
    },
    RON: {
        type: 'FiatCurrency',
        id: 'RON',
        symbol: 'RON',
        code: 'RON',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Romanian Leu',
    },
    QAR: {
        type: 'FiatCurrency',
        id: 'QAR',
        symbol: 'QR',
        code: 'QAR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Qatari Rial',
    },
    PYG: {
        type: 'FiatCurrency',
        id: 'PYG',
        symbol: '₲',
        code: 'PYG',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Paraguayan Guarani',
    },
    PKR: {
        type: 'FiatCurrency',
        id: 'PKR',
        symbol: '₨',
        code: 'PKR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Pakistani Rupee',
    },
    PHP: {
        type: 'FiatCurrency',
        id: 'PHP',
        symbol: '₱',
        code: 'PHP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Philippine Peso',
    },
    PEN: {
        type: 'FiatCurrency',
        id: 'PEN',
        symbol: 'S/.',
        code: 'PEN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Peruvian Nuevo Sol',
    },
    PAB: {
        type: 'FiatCurrency',
        id: 'PAB',
        symbol: 'B/.',
        code: 'PAB',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Panamanian Balboa',
    },
    OMR: {
        type: 'FiatCurrency',
        id: 'OMR',
        symbol: 'OMR',
        code: 'OMR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Omani Rial',
    },
    NZD: {
        type: 'FiatCurrency',
        id: 'NZD',
        symbol: 'NZ$',
        code: 'NZD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'New Zealand Dollar',
    },
    NPR: {
        type: 'FiatCurrency',
        id: 'NPR',
        symbol: 'NPRs',
        code: 'NPR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Nepalese Rupee',
    },
    NOK: {
        type: 'FiatCurrency',
        id: 'NOK',
        symbol: 'kr',
        code: 'NOK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Norwegian Krone',
    },
    NIO: {
        type: 'FiatCurrency',
        id: 'NIO',
        symbol: 'C$',
        code: 'NIO',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Nicaraguan Córdoba',
    },
    NAD: {
        type: 'FiatCurrency',
        id: 'NAD',
        symbol: 'N$',
        code: 'NAD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Namibian Dollar',
    },
    MZN: {
        type: 'FiatCurrency',
        id: 'MZN',
        symbol: 'MTn',
        code: 'MZN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Mozambican Metical',
    },
    MYR: {
        type: 'FiatCurrency',
        id: 'MYR',
        symbol: 'RM',
        code: 'MYR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Malaysian Ringgit',
    },
    MXN: {
        type: 'FiatCurrency',
        id: 'MXN',
        symbol: 'MX$',
        code: 'MXN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Mexican Peso',
    },
    MUR: {
        type: 'FiatCurrency',
        id: 'MUR',
        symbol: 'MURs',
        code: 'MUR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Mauritian Rupee',
    },
    MOP: {
        type: 'FiatCurrency',
        id: 'MOP',
        symbol: 'MOP$',
        code: 'MOP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Macanese Pataca',
    },
    MMK: {
        type: 'FiatCurrency',
        id: 'MMK',
        symbol: 'K',
        code: 'MMK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Myanma Kyat',
    },
    MKD: {
        type: 'FiatCurrency',
        id: 'MKD',
        symbol: 'MKD',
        code: 'MKD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Macedonian Denar',
    },
    MGA: {
        type: 'FiatCurrency',
        id: 'MGA',
        symbol: 'MGA',
        code: 'MGA',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Malagasy Ariary',
    },
    MDL: {
        type: 'FiatCurrency',
        id: 'MDL',
        symbol: 'MDL',
        code: 'MDL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Moldovan Leu',
    },
    MAD: {
        type: 'FiatCurrency',
        id: 'MAD',
        symbol: 'MAD',
        code: 'MAD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Moroccan Dirham',
    },
    LYD: {
        type: 'FiatCurrency',
        id: 'LYD',
        symbol: 'LD',
        code: 'LYD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Libyan Dinar',
    },
    LVL: {
        type: 'FiatCurrency',
        id: 'LVL',
        symbol: 'Ls',
        code: 'LVL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Latvian Lats',
    },
    LTL: {
        type: 'FiatCurrency',
        id: 'LTL',
        symbol: 'Lt',
        code: 'LTL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Lithuanian Litas',
    },
    LKR: {
        type: 'FiatCurrency',
        id: 'LKR',
        symbol: 'Rs',
        code: 'LKR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Sri Lankan Rupee',
    },
    LBP: {
        type: 'FiatCurrency',
        id: 'LBP',
        symbol: 'L.L.',
        code: 'LBP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Lebanese Pound',
    },
    KZT: {
        type: 'FiatCurrency',
        id: 'KZT',
        symbol: 'KZT',
        code: 'KZT',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Kazakhstani Tenge',
    },
    KWD: {
        type: 'FiatCurrency',
        id: 'KWD',
        symbol: 'KD',
        code: 'KWD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Kuwaiti Dinar',
    },
    KRW: {
        type: 'FiatCurrency',
        id: 'KRW',
        symbol: '₩',
        code: 'KRW',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'South Korean Won',
    },
    KMF: {
        type: 'FiatCurrency',
        id: 'KMF',
        symbol: 'CF',
        code: 'KMF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Comorian Franc',
    },
    KHR: {
        type: 'FiatCurrency',
        id: 'KHR',
        symbol: 'KHR',
        code: 'KHR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Cambodian Riel',
    },
    KES: {
        type: 'FiatCurrency',
        id: 'KES',
        symbol: 'Ksh',
        code: 'KES',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Kenyan Shilling',
    },
    JPY: {
        type: 'FiatCurrency',
        id: 'JPY',
        symbol: '¥',
        code: 'JPY',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Japanese Yen',
    },
    JOD: {
        type: 'FiatCurrency',
        id: 'JOD',
        symbol: 'JD',
        code: 'JOD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Jordanian Dinar',
    },
    JMD: {
        type: 'FiatCurrency',
        id: 'JMD',
        symbol: 'J$',
        code: 'JMD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Jamaican Dollar',
    },
    ISK: {
        type: 'FiatCurrency',
        id: 'ISK',
        symbol: 'Ikr',
        code: 'ISK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Icelandic Króna',
    },
    IRR: {
        type: 'FiatCurrency',
        id: 'IRR',
        symbol: 'IRR',
        code: 'IRR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Iranian Rial',
    },
    IQD: {
        type: 'FiatCurrency',
        id: 'IQD',
        symbol: 'IQD',
        code: 'IQD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Iraqi Dinar',
    },
    INR: {
        type: 'FiatCurrency',
        id: 'INR',
        symbol: '₹',
        code: 'INR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Indian Rupee',
    },
    ILS: {
        type: 'FiatCurrency',
        id: 'ILS',
        symbol: '₪',
        code: 'ILS',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Israeli New Sheqel',
    },
    IDR: {
        type: 'FiatCurrency',
        id: 'IDR',
        symbol: 'Rp',
        code: 'IDR',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Indonesian Rupiah',
    },
    HUF: {
        type: 'FiatCurrency',
        id: 'HUF',
        symbol: 'Ft',
        code: 'HUF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Hungarian Forint',
    },
    HRK: {
        type: 'FiatCurrency',
        id: 'HRK',
        symbol: 'kn',
        code: 'HRK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Croatian Kuna',
    },
    HNL: {
        type: 'FiatCurrency',
        id: 'HNL',
        symbol: 'HNL',
        code: 'HNL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Honduran Lempira',
    },
    HKD: {
        type: 'FiatCurrency',
        id: 'HKD',
        symbol: 'HK$',
        code: 'HKD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Hong Kong Dollar',
    },
    GTQ: {
        type: 'FiatCurrency',
        id: 'GTQ',
        symbol: 'GTQ',
        code: 'GTQ',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Guatemalan Quetzal',
    },
    GNF: {
        type: 'FiatCurrency',
        id: 'GNF',
        symbol: 'FG',
        code: 'GNF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Guinean Franc',
    },
    GHS: {
        type: 'FiatCurrency',
        id: 'GHS',
        symbol: 'GH₵',
        code: 'GHS',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Ghanaian Cedi',
    },
    GEL: {
        type: 'FiatCurrency',
        id: 'GEL',
        symbol: '₾',
        code: 'GEL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Georgian Lari',
    },
    ETB: {
        type: 'FiatCurrency',
        id: 'ETB',
        symbol: 'Br',
        code: 'ETB',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Ethiopian Birr',
    },
    ERN: {
        type: 'FiatCurrency',
        id: 'ERN',
        symbol: 'Nfk',
        code: 'ERN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Eritrean Nakfa',
    },
    EGP: {
        type: 'FiatCurrency',
        id: 'EGP',
        symbol: 'EGP',
        code: 'EGP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Egyptian Pound',
    },
    EEK: {
        type: 'FiatCurrency',
        id: 'EEK',
        symbol: 'Ekr',
        code: 'EEK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Estonian Kroon',
    },
    DZD: {
        type: 'FiatCurrency',
        id: 'DZD',
        symbol: 'DA',
        code: 'DZD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Algerian Dinar',
    },
    DOP: {
        type: 'FiatCurrency',
        id: 'DOP',
        symbol: 'RD$',
        code: 'DOP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Dominican Peso',
    },
    DKK: {
        type: 'FiatCurrency',
        id: 'DKK',
        symbol: 'kr',
        code: 'DKK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Danish Krone',
    },
    DJF: {
        type: 'FiatCurrency',
        id: 'DJF',
        symbol: 'Fdj',
        code: 'DJF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Djiboutian Franc',
    },
    CZK: {
        type: 'FiatCurrency',
        id: 'CZK',
        symbol: 'Kč',
        code: 'CZK',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Czech Republic Koruna',
    },
    CVE: {
        type: 'FiatCurrency',
        id: 'CVE',
        symbol: 'CV$',
        code: 'CVE',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Cape Verdean Escudo',
    },
    CRC: {
        type: 'FiatCurrency',
        id: 'CRC',
        symbol: '₡',
        code: 'CRC',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Costa Rican Colón',
    },
    COP: {
        type: 'FiatCurrency',
        id: 'COP',
        symbol: 'CO$',
        code: 'COP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Colombian Peso',
    },
    CNY: {
        type: 'FiatCurrency',
        id: 'CNY',
        symbol: '¥',
        code: 'CNY',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Chinese Yuan',
    },
    CLP: {
        type: 'FiatCurrency',
        id: 'CLP',
        symbol: 'CLP$',
        code: 'CLP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Chilean Peso',
    },
    CHF: {
        type: 'FiatCurrency',
        id: 'CHF',
        symbol: 'Fr.',
        code: 'CHF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Swiss Franc',
    },
    CDF: {
        type: 'FiatCurrency',
        id: 'CDF',
        symbol: 'CDF',
        code: 'CDF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Congolese Franc',
    },
    BZD: {
        type: 'FiatCurrency',
        id: 'BZD',
        symbol: 'BZ$',
        code: 'BZD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Belize Dollar',
    },
    BYN: {
        type: 'FiatCurrency',
        id: 'BYN',
        symbol: 'Br',
        code: 'BYN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Belarusian Ruble',
    },
    BWP: {
        type: 'FiatCurrency',
        id: 'BWP',
        symbol: 'BWP',
        code: 'BWP',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Botswanan Pula',
    },
    BRL: {
        type: 'FiatCurrency',
        id: 'BRL',
        symbol: 'R$',
        code: 'BRL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Brazilian Real',
    },
    BOB: {
        type: 'FiatCurrency',
        id: 'BOB',
        symbol: 'Bs',
        code: 'BOB',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Bolivian Boliviano',
    },
    BND: {
        type: 'FiatCurrency',
        id: 'BND',
        symbol: 'BN$',
        code: 'BND',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Brunei Dollar',
    },
    BIF: {
        type: 'FiatCurrency',
        id: 'BIF',
        symbol: 'FBu',
        code: 'BIF',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Burundian Franc',
    },
    BHD: {
        type: 'FiatCurrency',
        id: 'BHD',
        symbol: 'BD',
        code: 'BHD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Bahraini Dinar',
    },
    BGN: {
        type: 'FiatCurrency',
        id: 'BGN',
        symbol: 'BGN',
        code: 'BGN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Bulgarian Lev',
    },
    BDT: {
        type: 'FiatCurrency',
        id: 'BDT',
        symbol: '৳',
        code: 'BDT',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Bangladeshi Taka',
    },
    BAM: {
        type: 'FiatCurrency',
        id: 'BAM',
        symbol: 'KM',
        code: 'BAM',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Bosnia-Herzegovina Convertible Mark',
    },
    AZN: {
        type: 'FiatCurrency',
        id: 'AZN',
        symbol: 'man.',
        code: 'AZN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Azerbaijani Manat',
    },
    AUD: {
        type: 'FiatCurrency',
        id: 'AUD',
        symbol: 'A$',
        code: 'AUD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Australian Dollar',
    },
    ARS: {
        type: 'FiatCurrency',
        id: 'ARS',
        symbol: '$',
        code: 'ARS',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Argentine Peso',
    },
    AMD: {
        type: 'FiatCurrency',
        id: 'AMD',
        symbol: 'AMD',
        code: 'AMD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Armenian Dram',
    },
    ALL: {
        type: 'FiatCurrency',
        id: 'ALL',
        symbol: 'ALL',
        code: 'ALL',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Albanian Lek',
    },
    AFN: {
        type: 'FiatCurrency',
        id: 'AFN',
        symbol: 'Af',
        code: 'AFN',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Afghan Afghani',
    },
    AED: {
        type: 'FiatCurrency',
        id: 'AED',
        symbol: 'DH',
        code: 'AED',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'United Arab Emirates Dirham',
    },
    CAD: {
        type: 'FiatCurrency',
        id: 'CAD',
        symbol: 'CA$',
        code: 'CAD',
        fraction: 18,
        rateFraction: 18,
        icon: 'TODO',
        name: 'Canadian Dollar',
    },
}

export const COIN_GECKO_SUPPORTED_FIAT_CURRENCIES: Set<FiatCurrencyCode> =
    new Set([
        'AED',
        'ARS',
        'AUD',
        'BDT',
        'BHD',
        'BMD',
        'BRL',
        'CAD',
        'CHF',
        'CLP',
        'CNY',
        'CZK',
        'DKK',
        'EUR',
        'GBP',
        'GEL',
        'HKD',
        'HUF',
        'IDR',
        'ILS',
        'INR',
        'JPY',
        'KRW',
        'KWD',
        'LKR',
        'MMK',
        'MXN',
        'MYR',
        'NGN',
        'NOK',
        'NZD',
        'PHP',
        'PKR',
        'PLN',
        'RON',
        'RUB',
        'SAR',
        'SEK',
        'SGD',
        'THB',
        'TRY',
        'TWD',
        'UAH',
        'USD',
        'VEF',
        'VND',
        'ZAR',
    ])

export const INITIAL_DEFAULT_CURRENCY = FIAT_CURRENCIES.USD

// Natives

// Wrapped natives

export const ETHEREUM_WETH: CryptoCurrency = {
    symbol: 'WETH',
    fraction: 18,
    name: 'Wrapped Ether',
    marketCapRank: 2,
    type: 'CryptoCurrency',
    rateFraction: 18,
    id: 'Ethereum|******************************************',
    networkHexChainId: ETHEREUM.hexChainId,
    address: staticFromString('******************************************'),
    code: 'WETH',
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
}

export const BSC_WBNB: CryptoCurrency = {
    symbol: 'WBNB',
    fraction: 18,
    name: 'Wrapped BNB',
    marketCapRank: null,
    type: 'CryptoCurrency',
    rateFraction: 18,
    id: 'BSC|******************************************',
    networkHexChainId: BSC.hexChainId,
    address: staticFromString('******************************************'),
    code: 'WBNB',
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/BSC|******************************************',
}

export const OPBNB_WBNB: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'OPBNB|******************************************',
    symbol: 'WBNB',
    code: 'WBNB',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/OPBNB|******************************************',
    name: 'Wrapped BNB',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: OPBNB.hexChainId,
}

export const POLYGON_WMATIC: CryptoCurrency = {
    symbol: 'WMATIC',
    fraction: 18,
    name: 'Wrapped Matic',
    marketCapRank: null,
    type: 'CryptoCurrency',
    rateFraction: 18,
    id: 'Polygon|******************************************',
    networkHexChainId: POLYGON.hexChainId,
    address: staticFromString('******************************************'),
    code: 'WMATIC',
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Polygon|******************************************',
}

export const GNOSIS_WXDAI: CryptoCurrency = {
    symbol: 'WXDAI',
    fraction: 18,
    name: 'Wrapped XDAI',
    marketCapRank: null,
    type: 'CryptoCurrency',
    rateFraction: 18,
    id: 'Gnosis|******************************************',
    networkHexChainId: GNOSIS.hexChainId,
    address: staticFromString('******************************************'),
    code: 'WXDAI',
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
}

export const ARBITRUM_WETH: CryptoCurrency = {
    symbol: 'WETH',
    fraction: 18,
    name: 'Wrapped Ether',
    marketCapRank: 2,
    type: 'CryptoCurrency',
    rateFraction: 18,
    id: 'Arbitrum|******************************************',
    networkHexChainId: ARBITRUM.hexChainId,
    address: staticFromString('******************************************'),
    code: 'WETH',
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Arbitrum|******************************************',
}

export const OPTIMISM_WETH: CryptoCurrency = {
    symbol: 'WETH',
    fraction: 18,
    name: 'Wrapped Ether',
    marketCapRank: 2,
    type: 'CryptoCurrency',
    rateFraction: 18,
    id: 'Optimism|******************************************',
    networkHexChainId: OPTIMISM.hexChainId,
    address: staticFromString('******************************************'),
    code: 'WETH',
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Optimism|******************************************',
}

// Stables

export const GNOSIS_EURE: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'EURe',
    code: 'EURe',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Monerium EUR emoney',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_EURE_V2: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'EURe',
    code: 'EURe',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Monerium EUR emoney',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const AVALANCHE_WAVAX: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Avalanche|******************************************',
    symbol: 'WAVAX',
    code: 'WAVAX',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Avalanche|******************************************',
    name: 'Wrapped AVAX',
    address: staticFromString('******************************************'),
    marketCapRank: 17,
    networkHexChainId: AVALANCHE.hexChainId,
}

export const BLAST_WETH: CryptoCurrency = {
    symbol: 'WETH',
    fraction: 18,
    name: 'Wrapped Ether',
    marketCapRank: 2,
    type: 'CryptoCurrency',
    rateFraction: 18,
    id: 'Blast|******************************************',
    networkHexChainId: OPTIMISM.hexChainId,
    address: staticFromString('******************************************'),
    code: 'WETH',
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Blast|******************************************',
}

export const BASE_WETH: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Base|******************************************',
    symbol: 'WETH',
    code: 'WETH',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Base|******************************************',
    name: 'Wrapped Ether',
    address: staticFromString('******************************************'),
    marketCapRank: 2,
    networkHexChainId: BASE.hexChainId,
}

export const GNOSIS_GBPE: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'GBPe',
    code: 'GBPe',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Monerium GBP emoney',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_GBPE_V2: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'GBPe',
    code: 'GBPe',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Monerium GBP emoney',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'USDC.e',
    code: 'USDC.e',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'USD//C on xDai',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_USDCE: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'USDC.e',
    code: 'USDC.e',
    fraction: 6,
    rateFraction: 6,
    marketCapRank: 112,
    name: 'USDC.e (Bridged)',
    address: staticFromString('******************************************'),
    networkHexChainId: GNOSIS.hexChainId,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
}

export const OPTIMISM_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Optimism|******************************************',
    symbol: 'USDC',
    code: 'USDC',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Optimism|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 6,
    networkHexChainId: OPTIMISM.hexChainId,
}

export const OPTIMISM_USDC_E: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Optimism|******************************************',
    symbol: 'USDC.e',
    code: 'USDC.e',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Optimism|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 522,
    networkHexChainId: OPTIMISM.hexChainId,
}

export const ETHEREUM_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Ethereum|******************************************',
    symbol: 'USDT',
    code: 'USDT',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
    name: 'Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: 3,
    networkHexChainId: ETHEREUM.hexChainId,
}

export const POLYGON_USDT_POS: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Polygon|******************************************',
    symbol: 'USDT',
    code: 'USDT',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Polygon|******************************************',
    name: '(PoS) Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: POLYGON.hexChainId,
}

export const LINEA_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Linea|******************************************',
    symbol: 'USDT',
    code: 'USDT',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Linea|******************************************',
    name: 'Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: 1108,
    networkHexChainId: LINEA.hexChainId,
}
const GNOSIS_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'USDT',
    code: 'USDT',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: 162,
    networkHexChainId: GNOSIS.hexChainId,
}

export const BSC_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'BSC|******************************************',
    symbol: 'USDT',
    code: 'USDT',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/BSC|******************************************',
    name: 'Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: BSC.hexChainId,
}

const BSC_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'BSC|******************************************',
    symbol: 'USDC',
    code: 'USDC',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://static.debank.com/image/coin/logo_url/usdc/e87790bfe0b3f2ea855dc29069b38818.png',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    networkHexChainId: BSC.hexChainId,
    marketCapRank: null,
}

export const OPTIMISM_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Optimism|******************************************',
    symbol: 'USDT',
    code: 'USDT',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Optimism|******************************************',
    name: 'Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: OPTIMISM.hexChainId,
}
export const ARBITRUM_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Arbitrum|******************************************',
    symbol: 'USDT',
    code: 'USDT',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Arbitrum|******************************************',
    name: 'Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: ARBITRUM.hexChainId,
}
export const AURORA_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Aurora|******************************************',
    symbol: 'USDT.e',
    code: 'USDT.e',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Aurora|******************************************',
    name: 'Tether USD',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: AURORA.hexChainId,
}
export const MANTLE_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Mantle|******************************************',
    symbol: 'USDC',
    code: 'USDC',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Mantle|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: MANTLE.hexChainId,
}
export const ETHEREUM_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Ethereum|******************************************',
    symbol: 'USDC',
    code: 'USDC',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 6,
    networkHexChainId: ETHEREUM.hexChainId,
}

export const ARBITRUM_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Arbitrum|******************************************',
    symbol: 'USDC',
    code: 'USDC',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Arbitrum|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 6,
    networkHexChainId: ARBITRUM.hexChainId,
}

export const ARBITRUM_USDC_E: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Arbitrum|******************************************',
    symbol: 'USDC.e',
    code: 'USDC.e',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Arbitrum|******************************************',
    name: 'USDC.e (Bridged)',
    address: staticFromString('******************************************'),
    marketCapRank: 219,
    networkHexChainId: ARBITRUM.hexChainId,
}

export const BASE_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Base|******************************************',
    symbol: 'USDC',
    code: 'USDC',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Base|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 6,
    networkHexChainId: BASE.hexChainId,
}

const BASE_USDBC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Base|******************************************',
    symbol: 'USDbC',
    code: 'USDbC',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Base|******************************************',
    name: 'USD Base Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 664,
    networkHexChainId: BASE.hexChainId,
}

const BASE_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Base|******************************************',
    address: staticFromString('******************************************'),
    symbol: 'USDT',
    code: 'USDT',
    fraction: 6,
    rateFraction: 6,
    marketCapRank: null,
    icon: '',
    name: 'Tether USD',
    networkHexChainId: BASE.hexChainId,
}

export const POLYGON_USDC: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Polygon|******************************************',
    symbol: 'USDC',
    code: 'USDC',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Polygon|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 6,
    networkHexChainId: POLYGON.hexChainId,
}
export const POLYGON_USDC_E: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Polygon|******************************************',
    symbol: 'USDC.e',
    code: 'USDC.e',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Polygon|******************************************',
    name: 'USD Coin (PoS)',
    address: staticFromString('******************************************'),
    marketCapRank: 152,
    networkHexChainId: POLYGON.hexChainId,
}

const CRONOS_USDC_E: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Cronos|******************************************',
    symbol: 'USDC.e',
    code: 'USDC.e',
    fraction: 6,
    rateFraction: 6,
    icon: '',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: CRONOS.hexChainId,
}

const AVALANCHE_USDC_E: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Avalanche|******************************************',
    symbol: 'USDC.e',
    code: 'USDC.e',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Avalanche|******************************************',
    name: 'USD Coin',
    address: staticFromString('******************************************'),
    marketCapRank: 551,
    networkHexChainId: AVALANCHE.hexChainId,
}

const AVALANCHE_USDT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Avalanche|******************************************',
    symbol: 'USDt',
    code: 'USDt',
    fraction: 6,
    rateFraction: 6,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Avalanche|******************************************',
    name: 'TetherToken',
    address: staticFromString('******************************************'),
    marketCapRank: 3,
    networkHexChainId: AVALANCHE.hexChainId,
}
// Savings

export const GNOSIS_AAVE_EURE: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'aGnoEURe',
    code: 'aGnoEURe',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Aave Gnosis EURe',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_SDAI: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'sDAI',
    code: 'sDAI',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Savings xDAI',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_WSTETH: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'wstETH',
    code: 'wstETH',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Wrapped liquid staked Ether 2.0 from Mainnet',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_WETH: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'WETH',
    code: 'WETH',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
    name: 'Ethereum (Wrapped)',
    address: staticFromString('******************************************'),
    marketCapRank: 155,
    networkHexChainId: GNOSIS.hexChainId,
}

// Other

export const GNOSIS_GNO: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'GNO',
    code: 'GNO',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Gnosis',
    address: staticFromString('******************************************'),
    marketCapRank: 150,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_SP500: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'bCSPX',
    code: 'bCSPX',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'SP500 Index',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const GNOSIS_BITCOIN: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'WBTC',
    code: 'WBTC',
    fraction: 8,
    rateFraction: 8,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Bitcoin',
    address: staticFromString('******************************************'),
    marketCapRank: 2004,
    networkHexChainId: GNOSIS.hexChainId,
}

export const ERC20_ABI = [
    {
        inputs: [
            { internalType: 'address', name: 'to', type: 'address' },
            { internalType: 'uint256', name: 'amount', type: 'uint256' },
        ],
        name: 'transfer',
        outputs: [{ internalType: 'bool', name: 'ok', type: 'bool' }],
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        inputs: [{ internalType: 'address', name: 'account', type: 'address' }],
        name: 'balanceOf',
        outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }],
        stateMutability: 'view',
        type: 'function',
    },
    {
        constant: false,
        inputs: [
            { name: '_spender', type: 'address' },
            { name: '_value', type: 'uint256' },
        ],
        name: 'approve',
        outputs: [{ name: '', type: 'bool' }],
        payable: false,
        stateMutability: 'nonpayable' as const,
        type: 'function' as const,
    },
    {
        constant: true,
        inputs: [
            { name: '_owner', type: 'address' },
            { name: '_spender', type: 'address' },
        ],
        name: 'allowance',
        outputs: [{ name: '', type: 'uint256' }],
        payable: false,
        stateMutability: 'view',
        type: 'function',
    },
    {
        constant: true,
        inputs: [],
        name: 'symbol',
        outputs: [
            {
                name: '',
                type: 'string',
            },
        ],
        payable: false,
        stateMutability: 'view',
        type: 'function',
    },
    {
        constant: true,
        inputs: [],
        name: 'name',
        outputs: [
            {
                name: '',
                type: 'string',
            },
        ],
        payable: false,
        stateMutability: 'view',
        type: 'function',
    },
    {
        constant: true,
        inputs: [],
        name: 'decimals',
        outputs: [
            {
                name: '',
                type: 'uint8',
            },
        ],
        payable: false,
        stateMutability: 'view',
        type: 'function',
    },
] as const

export const ERC20_NATIVE_WRAPPER_ABI = [
    {
        constant: false,
        inputs: [{ name: 'wad', type: 'uint256' }],
        name: 'withdraw',
        outputs: [],
        payable: false,
        stateMutability: 'nonpayable',
        type: 'function',
    },
    {
        constant: false,
        inputs: [],
        name: 'deposit',
        outputs: [],
        payable: true,
        stateMutability: 'payable',
        type: 'function',
    },
]

const FANTOM_WFNT: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Fantom|******************************************',
    symbol: 'FTM',
    code: 'FTM',
    fraction: 18,
    rateFraction: 18,
    icon: '',
    name: 'Wrapped Fantom',
    address: staticFromString('******************************************'),
    marketCapRank: 2,
    networkHexChainId: FANTOM.hexChainId,
}

const CRONOS_WCRO: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Cronos|******************************************',
    symbol: 'CRO',
    code: 'CRO',
    fraction: 18,
    rateFraction: 18,
    icon: '',
    name: 'Cronos',
    marketCapRank: 2,
    networkHexChainId: CRONOS.hexChainId,
    address: staticFromString('******************************************'),
}

const MANTLE_MTL: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Mantle|******************************************',
    symbol: 'MNT',
    code: 'MNT',
    fraction: 18,
    rateFraction: 18,
    icon: '',
    name: 'MNT',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: MANTLE.hexChainId,
}

const ETHEREUM_MTL: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Ethereum|******************************************',
    symbol: 'MNT',
    code: 'MNT',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
    name: 'Mantle',
    address: staticFromString('******************************************'),
    marketCapRank: 46,
    networkHexChainId: ETHEREUM.hexChainId,
}

const ETHEREUM_AAVE: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Ethereum|******************************************',
    symbol: 'AAVE',
    code: 'AAVE',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Ethereum|******************************************',
    name: 'Aave Token',
    address: staticFromString('******************************************'),
    marketCapRank: 47,
    networkHexChainId: ETHEREUM.hexChainId,
}

const GNOSIS_AAVE: CryptoCurrency = {
    type: 'CryptoCurrency',
    id: 'Gnosis|******************************************',
    symbol: 'AAVE',
    code: 'AAVE',
    fraction: 18,
    rateFraction: 18,
    icon: 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/wallet/image/currency/Gnosis|******************************************',
    name: 'Aave Token on xDai',
    address: staticFromString('******************************************'),
    marketCapRank: null,
    networkHexChainId: GNOSIS.hexChainId,
}

export const SWAPS_IO_SUPPORTED_CURRENCIES_MAP: Record<
    NetworkHexId,
    Web3.address.Address[]
> = {
    // ===== Ethereum (chainId = 0x1) =====
    ['0x1' as NetworkHexId]: [
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
    ],

    // ===== Optimism (chainId = 0xa) =====
    ['0xa' as NetworkHexId]: [
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
    ],
    // ===== BSC (chainId = 56 -> 0x38) =====
    ['0x38' as NetworkHexId]: [
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('0x7130d2a12b9bcbfae4f2634d864a1ee1ce3ead9c'),
    ],

    // ===== Gnosis (chainId = 0x64) =====
    ['0x64' as NetworkHexId]: [
        staticFromString('0x44fa8e6f47987339850636f88629646662444217'),
        GNOSIS_EURE_V2.address as Web3.address.Address,
        GNOSIS_GBPE_V2.address as Web3.address.Address,
        GNOSIS_USDCE.address as Web3.address.Address,
        staticFromString('******************************************'),
        staticFromString('0xca5d8f8a8d49439357d3cf46ca2e720702f132b8'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('0xa1fa064a85266e2ca82dee5c5ccec84df445760e'),
        staticFromString('0x7a5c3860a77a8dc1b225bd46d0fb2ac1c6d191bc'),
        staticFromString('0x23e4e76d01b2002be436ce8d6044b0aa2f68b68a'),
        staticFromString('******************************************'),
        staticFromString('0xf490c80aae5f2616d3e3bda2483e30c4cb21d1a0'),
        staticFromString('******************************************'),
        staticFromString('0xea50f402653c41cadbafd1f788341db7b7f37816'),
        staticFromString('******************************************'),
    ],

    // ===== Polygon (chainId = 0x89) =====
    ['0x89' as NetworkHexId]: [
        staticFromString('0x0000000000000000000000000000000000001010'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('0x1bfd67037b42cf73acf2047067bd4f2c47d9bfd6'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
    ],

    // ===== PolygonZkevm (chainId = 1101 -> 0x44d) =====
    ['0x44d' as NetworkHexId]: [
        staticFromString('0x37eaa0ef3549a5bb7d431be78a3d99bd360d19e5'),
        staticFromString('0x1e4a5963abfd975d8c9021ce480b42188849d41d'),
        staticFromString('0x4f9a0e7fd2bf6067db6994cf12e4495df938e6e9'),
    ],

    // ===== Base (chainId = 0x2105) =====
    ['0x2105' as NetworkHexId]: [
        staticFromString('******************************************'),
        staticFromString('0x940181a94a35a4569e4529a3cdfb74e38fd98631'),
        staticFromString('******************************************'),
        staticFromString('0xd4a0e0b9149bcee3c920d2e00b5de09138fd8bb7'),
        staticFromString('0x7c307e128efa31f540f2e2d976c995e0b65f51f6'),
        staticFromString('0x99cbc45ea5bb7ef3a5bc08fb1b7e56bb2442ef0d'),
        staticFromString('0xcbb7c0000ab88b473b1f5afd9ef808440eed33bf'),
        staticFromString('0x04c0599ae5a44757c0af6f9ec3b93da8976c150a'),
    ],

    // ===== Arbitrum (chainId = 0xa4b1) =====
    ['0xa4b1' as NetworkHexId]: [
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('0x223738a747383d6f9f827d95964e4d8e8ac754ce'),
    ],

    // ===== Avalanche (chainId = 0xa86a) =====
    ['0xa86a' as NetworkHexId]: [
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
        staticFromString('******************************************'),
    ],

    // ===== Linea (chainId = 59144 -> 0xe708) =====
    ['0xe708' as NetworkHexId]: [
        staticFromString('******************************************'),
        staticFromString('******************************************'),
    ],
}

export const SWAPS_IO_SUPPORTED_CURRENICES = [
    ...keys(SWAPS_IO_SUPPORTED_CURRENCIES_MAP).flatMap((network) =>
        SWAPS_IO_SUPPORTED_CURRENCIES_MAP[network].map((address) =>
            currencyId({ address, network })
        )
    ),
]

// TODO @resetko-zeal convert to Record<PredefinedNetwork['name'], [from,to]>, it will be more typesafe, and then derive this array from it
export const WRAPPED_NATIVE_TOKENS: [CryptoCurrency, CryptoCurrency][] = [
    [POLYGON.nativeCurrency, POLYGON_WMATIC],
    [ETHEREUM.nativeCurrency, ETHEREUM_WETH],
    [GNOSIS.nativeCurrency, GNOSIS_WXDAI],
    [ARBITRUM.nativeCurrency, ARBITRUM_WETH],
    [OPTIMISM.nativeCurrency, OPTIMISM_WETH],
    [BSC.nativeCurrency, BSC_WBNB],
    [OPBNB.nativeCurrency, OPBNB_WBNB],
    [BASE.nativeCurrency, BASE_WETH],
    [AVALANCHE.nativeCurrency, AVALANCHE_WAVAX],
    [BLAST.nativeCurrency, BLAST_WETH],
]

export const MONERIUM_V1_TOKENS = new Set([
    currencyId({
        network: GNOSIS.hexChainId,
        address: staticFromString('******************************************'), // GBP
    }),
    currencyId({
        network: GNOSIS.hexChainId,
        address: staticFromString('******************************************'), // EURO
    }),
    currencyId({
        network: POLYGON.hexChainId,
        address: staticFromString('******************************************'), // GBP
    }),
    currencyId({
        network: POLYGON.hexChainId,
        address: staticFromString('******************************************'), // GBP
    }),
])

export const MONERIUM_V2_TOKENS = new Set<CurrencyId>([
    currencyId({
        network: GNOSIS.hexChainId,
        address: staticFromString('******************************************'), // EURO
    }),
    currencyId({
        network: POLYGON.hexChainId,
        address: staticFromString('******************************************'), // EURO
    }),
    currencyId({
        network: GNOSIS.hexChainId,
        address: staticFromString('******************************************'), // GBP
    }),
    currencyId({
        network: POLYGON.hexChainId,
        address: staticFromString('******************************************'), // GBP
    }),
])

export const MONERIUM_GNOSIS_V1_TO_V2_MAP: Record<CurrencyId, CurrencyId> = {
    [GNOSIS_EURE.id]: GNOSIS_EURE_V2.id,
    [GNOSIS_GBPE.id]: GNOSIS_GBPE_V2.id,
}

export const DEFAULT_CURRENCIES_LIST: FiatCurrency[] = Array.from(
    COIN_GECKO_SUPPORTED_FIAT_CURRENCIES
).reduce((acc, item) => {
    acc.push(FIAT_CURRENCIES[item])
    return acc
}, [] as FiatCurrency[])

export const STABLE_COIN_TO_FIAT_CURRENCY_MAP: Record<
    CryptoCurrency['id'],
    FiatCurrency
> = {
    [GNOSIS_EURE_V2.id]: FIAT_CURRENCIES.EUR,
    [GNOSIS_EURE.id]: FIAT_CURRENCIES.EUR,
    [GNOSIS_GBPE.id]: FIAT_CURRENCIES.GBP,
    [GNOSIS_GBPE_V2.id]: FIAT_CURRENCIES.GBP,
    [GNOSIS.nativeCurrency.id]: FIAT_CURRENCIES.USD,
    [GNOSIS_WXDAI.id]: FIAT_CURRENCIES.USD,
    [GNOSIS_USDC.id]: FIAT_CURRENCIES.USD,
    [GNOSIS_USDCE.id]: FIAT_CURRENCIES.USD,
    [GNOSIS_USDT.id]: FIAT_CURRENCIES.USD,
    [GNOSIS_AAVE_EURE.id]: FIAT_CURRENCIES.EUR,

    [OPTIMISM_USDC.id]: FIAT_CURRENCIES.USD,
    [OPTIMISM_USDT.id]: FIAT_CURRENCIES.USD,
    [OPTIMISM_USDC_E.id]: FIAT_CURRENCIES.USD,

    [ETHEREUM_USDT.id]: FIAT_CURRENCIES.USD,
    [ETHEREUM_USDC.id]: FIAT_CURRENCIES.USD,

    [POLYGON_USDT_POS.id]: FIAT_CURRENCIES.USD,
    [POLYGON_USDC.id]: FIAT_CURRENCIES.USD,
    [POLYGON_USDC_E.id]: FIAT_CURRENCIES.USD,

    [LINEA_USDT.id]: FIAT_CURRENCIES.USD,

    [BSC_USDT.id]: FIAT_CURRENCIES.USD,
    [BSC_USDC.id]: FIAT_CURRENCIES.USD,

    [BASE_USDT.id]: FIAT_CURRENCIES.USD,
    [BASE_USDBC.id]: FIAT_CURRENCIES.USD,

    [ARBITRUM_USDT.id]: FIAT_CURRENCIES.USD,
    [ARBITRUM_USDC.id]: FIAT_CURRENCIES.USD,
    [ARBITRUM_USDC_E.id]: FIAT_CURRENCIES.USD,

    [AURORA_USDT.id]: FIAT_CURRENCIES.USD,

    [MANTLE_USDC.id]: FIAT_CURRENCIES.USD,

    [BASE_USDC.id]: FIAT_CURRENCIES.USD,

    [CRONOS_USDC_E.id]: FIAT_CURRENCIES.USD,

    [AVALANCHE_USDC_E.id]: FIAT_CURRENCIES.USD,
    [AVALANCHE_USDT.id]: FIAT_CURRENCIES.USD,
}

// Coingecko does not support some of the currencies, so we map them to other ones
// Also it does not work well with natives, so we map them with wrapped ones
const COIN_GECKO_QUIRKS: { from: CryptoCurrency; to: CryptoCurrency }[] = [
    { from: GNOSIS_EURE_V2, to: GNOSIS_EURE },
    { from: GNOSIS_GBPE_V2, to: GNOSIS_GBPE },
    { from: LINEA.nativeCurrency, to: ETHEREUM_WETH },
    { from: ZKSYNC.nativeCurrency, to: ETHEREUM_WETH },
    { from: POLYGON_ZKEVM.nativeCurrency, to: ETHEREUM_WETH },
    { from: GNOSIS_AAVE, to: ETHEREUM_AAVE },
    ...WRAPPED_NATIVE_TOKENS.map(([from, to]) => ({ from, to })),
    { from: MANTLE_MTL, to: ETHEREUM_MTL },
    { from: GNOSIS_WETH, to: ETHEREUM_WETH },
    { from: FANTOM.nativeCurrency, to: FANTOM_WFNT },
    { from: CRONOS.nativeCurrency, to: CRONOS_WCRO },
]

export const DIRECT_QUIRKS_MAP: Record<CryptoCurrency['id'], CryptoCurrency> =
    COIN_GECKO_QUIRKS.reduce(
        (hash, item) => {
            hash[item.from.id] = item.to
            return hash
        },
        {} as Record<CryptoCurrency['id'], CryptoCurrency>
    )

export const PAYMASTER_ADDRESS = Web3.address.staticFromString(
    '******************************************'
)

// TODO @resetko-zeal remove native currency from map ZEAL-3783
export const PAYMASTER_MAP: Record<NetworkHexId, CryptoCurrency['id'][]> = {
    [LINEA.hexChainId]: [
        LINEA.nativeCurrency.id,
        'Linea|******************************************',
        'Linea|******************************************',
        'Linea|******************************************',
    ],
    [BLAST.hexChainId]: [
        BLAST.nativeCurrency.id,
        'Blast|******************************************',
        'Blast|******************************************',
        'Blast|******************************************',
    ],
    [BSC.hexChainId]: [
        BSC.nativeCurrency.id,
        'BSC|******************************************',
        'BSC|******************************************',
        'BSC|******************************************',
        'BSC|******************************************',
        'BSC|******************************************',
        'BSC|******************************************',
        'BSC|******************************************',
        'BSC|0x1af3f329e8be154074d8769d1ffa4ee058b1dbc3',
        'BSC|0x4b0f1812e5df2a09796481ff14017e6005508003',
        'BSC|0x1d2f0da169ceb9fc7b3144628db156f3f6c60dbe',
        'BSC|0x14016e85a25aeb13065688cafb43044c2ef86784',
        'BSC|0xcc42724c6683b7e57334c4e856f4c9965ed682bd',
        'BSC|0xbf5140a22578168fd562dccf235e5d43a02ce9b1',
        'BSC|0xf8a0bf9cf54bb92f17374d9e9a321e6a111a51bd',
        'BSC|0xf7de7e8a6bd59ed41a4b5fe50278b3b7f31384df',
        'BSC|0x7ddc52c4de30e94be3a6a0a2b259b2850f421989',
        'BSC|0x9c65ab58d8d978db963e63f2bfb7121627e3a739',
        'BSC|0xad29abb318791d579433d831ed122afeaf29dcfe',
        'BSC|0xaec945e04baf28b135fa7c640f624f8d90f1c3a6',
        'BSC|0x715d400f88c167884bbcc41c5fea407ed4d2f8a0',
        'BSC|0xd17479997f34dd9156deef8f95a52d81d265be9c',
        'BSC|0x90c97f71e18723b0cf0dfa30ee176ab653e89f40',
        'BSC|0x965f527d9159dce6288a2219db51fc6eef120dd1',
        'BSC|0xfe56d5892bdffc7bf58f2e84be1b2c32d21c308b',
        'BSC|0x4691937a7508860f876c9c0a2a617e7d9e945d4b',
        'BSC|0x16939ef78684453bfdfb47825f8a5f714f12623a',
        'BSC|0xb86abcb37c3a4b64f74f59301aff131a1becc787',
        'BSC|0x3203c9e46ca618c8c1ce5dc67e7e9d75f5da2377',
        'BSC|0x8f0528ce5ef7b51152a59745befdd91d97091d2f',
        'BSC|0xca3f508b8e4dd382ee878a314789373d80a5190a',
        'BSC|0x762539b45a1dcce3d36d080f74d1aed37844b878',
        'BSC|0x67ee3cb086f8a16f34bee3ca72fad36f7db929e2',
        'BSC|0xfd7b3a77848f1c2d67e05e54d78d174a0c850335',
        'BSC|0x1fa4a73a3f0133f0025378af00236f3abdee5d63',
        'BSC|0x250632378e573c6be1ac2f97fcdf00515d0aa91b',
        'BSC|0x0eb3a705fc54725037cc9e008bdede697f62f335',
        'BSC|0x0d8ce2a99bb6e3b7db580ed848240e4a0f9ae153',
        'BSC|0x8ff795a6f4d97e7887c79bea79aba5cc76444adf',
        'BSC|0xb59490ab09a0f526cc7305822ac65f2ab12f9723',
        'BSC|0x4338665cbb7b2485a8855a139b75d5e34ab0db94',
        'BSC|0x56b6fb708fc5732dec1afc8d8556423a2edccbd6',
        'BSC|0xa2b726b1145a4773f68593cf171187d8ebe4d495',
        'BSC|0xa1faa113cbe53436df28ff0aee54275c13b40975',
        'BSC|0xf21768ccbc73ea5b6fd3c687208a7c2def2d966e',
        'BSC|0x52ce071bd9b1c4b00a0b92d298c512478cad67e8',
        'BSC|0x47bead2563dcbf3bf2c9407fea4dc236faba485a',
        'BSC|0x88f1a5ae2a3bf98aeaf342d26b30a79438c9142e',
        'BSC|0xcf6bb5389c92bdda8a3747ddb454cb7a64626c63',
        'BSC|0x3ee2200efb3400fabb9aacf31297cbdd1d435d47',
        'BSC|0xad6caeb32cd2c308980a548bd0bc5aa4306c6c18',
        'BSC|0x4bd17003473389a42daf6a0a729f6fdb328bbbd7',
        'BSC|0x7083609fce4d1d8dc0c979aab8c869ea2c873402',
        'BSC|0xa184088a740c695e156f91f5cc086a06bb78b827',
        'BSC|0x570a5d26f7765ecb712c0924e4de545b89fd43df',
        'BSC|0xfe19f0b51438fd612f6fd59c1dbb3ea319f433ba',
        'BSC|0xba2ae424d960c26247dd6c32edc70b295c744c43',
        'BSC|0x2ed9a5c8c13b93955103b9a7c167b67ef4d568a3',
        'BSC|0x7130d2a12b9bcbfae4f2634d864a1ee1ce3ead9c',
        'BSC|0x23396cf899ca06c4472205fc903bdb4de249d6fc',
        'BSC|0xd41fdb03ba84762dd66a0af1a6c8540ff1ba5dfb',
        'BSC|0xc9849e6fdb743d08faee3e34dd2d1bc69ea11a51',
        'BSC|0x111111111117dc0aa78b770fa6a738034120c302',
        'BSC|0xe02df9e3e622debdd69fb838bb799e3f168902c5',
        'BSC|0x603c7f932ed1fc6575303d8fb018fdcbb0f39a95',
        'BSC|0x101d82428437127bf1608f699cd651e6abf9766e',
        'BSC|0xf307910a4c7bbc79691fd374889b36d8531b08e3',
        'BSC|0x799e1cf88a236e42b4a87c544a22a94ae95a6910',
    ],
    [BASE.hexChainId]: [
        BASE.nativeCurrency.id,
        'Base|0xc1cba3fcea344f92d9239c08c0568f6f2f0ee452',
        'Base|******************************************',
        'Base|******************************************',
        'Base|******************************************',
        'Base|0x2ae3f1ec7f1f5012cfeab0185bfc7aa3cf0dec22',
        'Base|0x9eaf8c1e34f05a589eda6bafdf391cf6ad3cb239',
        'Base|0xfa980ced6895ac314e7de34ef1bfae90a5add21b',
        'Base|0x28fe69ff6864c1c218878bdca01482d36b9d57b1',
        'Base|0xfd4330b0312fdeec6d4225075b82e00493ff2e3f',
        'Base|0xb6fe221fe9eef5aba221c348ba20a1bf5e73624c',
        'Base|******************************************',
    ],
    [GNOSIS.hexChainId]: [], // for now it is sponsered for all transactions so we don't really care
    [OPTIMISM.hexChainId]: [
        OPTIMISM.nativeCurrency.id,
        'Optimism|******************************************', // USDC
        'Optimism|******************************************', // USDT
        'Optimism|******************************************', // DAI
        'Optimism|******************************************', // WBTC
        'Optimism|******************************************', // SNX
        'Optimism|******************************************', // LINK
        'Optimism|******************************************', // SUSD
        'Optimism|******************************************', // PERP
        'Optimism|******************************************', // OP
        'Optimism|******************************************', // WSTETH
        'Optimism|******************************************', // THALES
        'Optimism|******************************************', // LYRA
        'Optimism|******************************************', // VELO
        'Optimism|******************************************', // DOLA
        'Optimism|******************************************', // KWENTA
        'Optimism|******************************************', // RETH
        'Optimism|******************************************', // MAI
        'Optimism|******************************************', // USDC.e
    ],
    [AVALANCHE.hexChainId]: [
        AVALANCHE.nativeCurrency.id,
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|0x6e84a6216ea6dacc71ee8e6b0a5b7322eebc0fdd',
        'Avalanche|0xce1bffbd5374dac86a2893119683f4911a2f7814',
        'Avalanche|0x214db107654ff987ad859f34125307783fc8e387',
        'Avalanche|0x2147efff675e4a4ee1c2f918d181cdbd7a8e208f',
        'Avalanche|0x5947bb275c521040051d82396192181b413227a3',
        'Avalanche|0x37b608519f91f70f2eeb0e5ed9af4061722e4f76',
        'Avalanche|0x130966628846bfd36ff31a822705796e8cb8c18d',
        'Avalanche|0xd24c2ad096400b6fbcd2ad8b24e7acbc21a1da64',
        'Avalanche|0x62edc0692bd897d2295872a9ffcac5425011c661',
        'Avalanche|0x60781c2586d68229fde47564546784ab3faca982',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
        'Avalanche|******************************************',
    ],

    [ETHEREUM.hexChainId]: [
        ETHEREUM.nativeCurrency.id,
        ETHEREUM_USDC.id,
        ETHEREUM_USDT.id,
        'Ethereum|******************************************',
        'Ethereum|******************************************',
        'Ethereum|******************************************',
    ],

    [POLYGON.hexChainId]: [
        POLYGON.nativeCurrency.id,
        POLYGON_WMATIC.id,
        POLYGON_USDC.id,
        POLYGON_USDT_POS.id,
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|0x1bfd67037b42cf73acf2047067bd4f2c47d9bfd6',
        'Polygon|0x9a71012b13ca4d3d0cdc72a177df3ef03b0e76a3',
        'Polygon|0x229b1b6c23ff8953d663c4cbb519717e323a0a84',
        'Polygon|0x172370d5cd63279efa6d502dab29171933a610af',
        'Polygon|0xe7804d91dfcde7f776c90043e03eaa6df87e6395',
        'Polygon|0xc168e40227e4ebd8c1cae80f7a55a4f0e6d66c97',
        'Polygon|0xef938b6da8576a896f6e0321ef80996f4890f9c4',
        'Polygon|0xdab625853c2b35d0a9c6bd8e5a097a664ef4ccfb',
        'Polygon|0xd99bafe5031cc8b345cb2e8c80135991f12d7130',
        'Polygon|0x385eeac5cb85a38a9a07a70c73e0a3271cfb54a7',
        'Polygon|0xe06bd4f5aac8d0aa337d13ec88db6defc6eaeefe',
        'Polygon|0x4e3decbb3645551b8a19f0ea1678079fcb33fb4c',
        'Polygon|0x4e78011ce80ee02d2c3e649fb657e45898257815',
        'Polygon|0xc3c7d422809852031b44ab29eec9f1eff2a58756',
        'Polygon|0xee9a352f6aac4af1a5b9f467f6a93e0ffbe9dd35',
        'Polygon|0xfa68fb4628dff1028cfec22b4162fccd0d45efb6',
        'Polygon|0xee7666aacaefaa6efeef62ea40176d3eb21953b9',
        'Polygon|0xa3fa99a148fa48d14ed51d610c367c61876997f1',
        'Polygon|0x614389eaae0a6821dc49062d56bda3d9d45fa2ff',
        'Polygon|0x42d61d766b85431666b39b89c43011f24451bff6',
        'Polygon|0x431cd3c9ac9fc73644bf68bf5691f4b83f9e104f',
        'Polygon|0xc17c30e98541188614df99239cabd40280810ca3',
        'Polygon|0x1d734a02ef1e1f5886e66b0673b71af5b53ffa94',
        'Polygon|0x62f594339830b90ae4c084ae7d223ffafd9658a7',
        'Polygon|0x3a58a54c066fdc0f2d55fc9c89f0415c92ebf3c4',
        'Polygon|0xa1428174f516f527fafdd146b883bb4428682737',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
        'Polygon|******************************************',
    ],
    [ARBITRUM.hexChainId]: [
        ARBITRUM.nativeCurrency.id,
        'Arbitrum|******************************************', // USDT
        'Arbitrum|******************************************', // WBTC
        'Arbitrum|******************************************', // DAI
        'Arbitrum|******************************************', // WSTETH
        'Arbitrum|******************************************', // ARB
        'Arbitrum|******************************************', // USDC
        'Arbitrum|******************************************', // USDC.e
        'Arbitrum|******************************************', // GMX
        'Arbitrum|******************************************', // UNI
        'Arbitrum|******************************************', // DPX
        'Arbitrum|******************************************', // JOE
        'Arbitrum|******************************************', // BAL
        'Arbitrum|******************************************', // LINK
        'Arbitrum|******************************************', // MCB
        'Arbitrum|******************************************', // PREMIA
        'Arbitrum|******************************************', // SUSHI
        'Arbitrum|******************************************', // WETH
        'Arbitrum|******************************************', // SPELL
        'Arbitrum|******************************************', // FRAX
        'Arbitrum|******************************************', // STG
        'Arbitrum|******************************************', // LIQD
        'Arbitrum|******************************************', // USDS
        'Arbitrum|******************************************', // AGEUR
        'Arbitrum|******************************************', // LDO
        'Arbitrum|******************************************', // VELA
        'Arbitrum|******************************************', // REUNI
        'Arbitrum|******************************************', // RETH
        'Arbitrum|******************************************', // RDNT
    ],
    [OPBNB.hexChainId]: [
        OPBNB.nativeCurrency.id,
        'OPBNB|******************************************',
        'OPBNB|******************************************',
        'OPBNB|******************************************',
        'OPBNB|******************************************',
    ],
}
