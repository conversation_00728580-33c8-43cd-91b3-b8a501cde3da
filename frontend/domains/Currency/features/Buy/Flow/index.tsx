import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { toFixedWithFraction } from '@zeal/toolkit/BigInt'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { PricesMap } from '@zeal/domains/Currency/api/fetchPriceChange'
import {
    SwapsIOContractsMap,
    SwapsIOQuote,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { SwapsIOQuoteRequest } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchQuote'
import { SubmitSwap } from '@zeal/domains/Currency/domains/SwapsIO/features/SubmitSwap'
import { calculateInitialFromCurrency } from '@zeal/domains/Currency/helpers/calculateInitialFromCurrency'
import { RatesMap } from '@zeal/domains/FXRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio } from '@zeal/domains/Portfolio'
import { OrderBuySignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'

type Props = {
    portfolio: ServerPortfolio
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    contractsMap: SwapsIOContractsMap
    currencies: CryptoCurrency[]
    ratesMap: RatesMap
    pricesMap: PricesMap
    fromAccount: Account
    toCurrency: CryptoCurrency
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_buy_success' }
    | Extract<
          MsgOf<typeof SubmitSwap>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'import_keys_button_clicked'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_swaps_io_swap_request_created'
          }
      >
    | Extract<MsgOf<typeof Form>, { type: 'close' }>

type State =
    | { type: 'form'; initialRequest: SwapsIOQuoteRequest }
    | {
          type: 'submit'
          quote: SwapsIOQuote
          simulation: OrderBuySignMessage
      }

export const Flow = ({
    onMsg,
    keyStoreMap,
    networkMap,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    networkRPCMap,
    sessionPassword,
    installationId,
    defaultCurrencyConfig,
    portfolio,
    contractsMap,
    currencies,
    ratesMap,
    pricesMap,
    fromAccount,
    toCurrency,
    currencyHiddenMap,
    currencyPinMap,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'form',
        initialRequest: {
            amount: null,
            sender: fromAccount,
            receiver: fromAccount.address as Web3.address.Address,
            toCurrency,
            networkRPCMap,
            contractsMap,
            networkMap,
            defaultCurrencyConfig,
            fromCurrency: calculateInitialFromCurrency({
                portfolio,
                toCurrency,
                currencies,
                networkMap,
            }),
        },
    })
    switch (state.type) {
        case 'form':
            return (
                <Form
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    currencies={currencies}
                    ratesMap={ratesMap}
                    pricesMap={pricesMap}
                    keyStoreMap={keyStoreMap}
                    installationId={installationId}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    initialRequest={state.initialRequest}
                    fromAccountPortfolio={portfolio}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_form_submitted':
                                setState({
                                    type: 'submit',
                                    quote: msg.quote,
                                    simulation: {
                                        type: 'OrderBuySignMessage',
                                        quote: msg.quote,
                                    },
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'submit':
            return (
                <SubmitSwap
                    source="buy"
                    simulation={state.simulation}
                    quote={state.quote}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    installationId={installationId}
                    keyStoreMap={keyStoreMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_swap_cancelled_close_clicked':
                                setState({
                                    type: 'form',
                                    initialRequest: {
                                        amount: toFixedWithFraction(
                                            state.quote.from.amount,
                                            state.quote.from.currency.fraction
                                        ),
                                        fromCurrency: state.quote.from.currency,
                                        toCurrency: state.quote.to.currency,
                                        sender: state.quote.sender,
                                        receiver: state.quote.receiver,

                                        networkRPCMap,
                                        networkMap,
                                        contractsMap,
                                        defaultCurrencyConfig,
                                    },
                                })
                                break
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_swaps_io_swap_request_created':
                                onMsg(msg)
                                break
                            case 'on_swap_success_clicked':
                            case 'on_swap_created_close_clicked':
                                onMsg({
                                    type: 'on_buy_success',
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    sessionPassword={sessionPassword}
                    senderPortfolio={portfolio}
                />
            )

        default:
            return notReachable(state)
    }
}
