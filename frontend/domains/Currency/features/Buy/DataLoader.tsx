import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import {
    ReloadableData,
    useReloadableData,
} from '@zeal/toolkit/LoadableData/ReloadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { PricesMap } from '@zeal/domains/Currency/api/fetchPriceChange'
import { SwapsIOContractsMap } from '@zeal/domains/Currency/domains/SwapsIO'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { RatesMap } from '@zeal/domains/FXRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio } from '@zeal/domains/Portfolio'
import { fetchServerPortfolio } from '@zeal/domains/Portfolio/api/fetchPortfolio'
import { unsafeGetPortfolioCache } from '@zeal/domains/Portfolio/helpers/unsafeGetPortfolioCache'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Flow } from './Flow'

type Props = {
    keyStoreMap: KeyStoreMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencies: CryptoCurrency[]
    ratesMap: RatesMap
    pricesMap: PricesMap
    portfolioMap: PortfolioMap
    fromAddress: Web3.address.Address
    toCurrency: CryptoCurrency
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    sessionPassword: string
    contractsMap: SwapsIOContractsMap
    currencyPinMap: CurrencyPinMap
    currencyHiddenMap: CurrencyHiddenMap
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof Flow>,
    {
        type:
            | 'close'
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
            | 'import_keys_button_clicked'
            | 'on_predefined_fee_preset_selected'
            | 'on_swaps_io_swap_request_created'
            | 'on_buy_success'
    }
>

const calculateInitialLoadingState = ({
    portfolioMap,
    fromAddress,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    installationId,
    currencyHiddenMap,
}: {
    currencyHiddenMap: CurrencyHiddenMap
    portfolioMap: PortfolioMap
    fromAddress: Web3.address.Address
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
}): ReloadableData<
    ServerPortfolio,
    {
        address: Web3.address.Address
        signal?: AbortSignal
        forceRefresh: boolean
        defaultCurrencyConfig: DefaultCurrencyConfig
        networkMap: NetworkMap

        currencyHiddenMap: CurrencyHiddenMap
        networkRPCMap: NetworkRPCMap
        installationId: string
    }
> => {
    const portfolio = unsafeGetPortfolioCache({
        portfolioMap,
        address: fromAddress,
    })
    if (portfolio) {
        return {
            type: 'loaded',
            params: {
                address: fromAddress,
                forceRefresh: false,
                currencyHiddenMap,
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                installationId,
            },
            data: portfolio,
        }
    }
    return {
        type: 'loading',
        params: {
            address: fromAddress,
            forceRefresh: false,
            currencyHiddenMap,
            networkMap,
            networkRPCMap,
            defaultCurrencyConfig,
            installationId,
        },
    }
}

export const DataLoader = ({
    toCurrency,
    fromAddress,
    portfolioMap,
    defaultCurrencyConfig,
    networkMap,

    installationId,
    keyStoreMap,
    gasCurrencyPresetMap,
    currencies,
    pricesMap,
    ratesMap,
    accountsMap,
    feePresetMap,
    networkRPCMap,
    sessionPassword,
    contractsMap,
    currencyHiddenMap,
    currencyPinMap,

    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useReloadableData(
        fetchServerPortfolio,
        calculateInitialLoadingState({
            currencyHiddenMap,
            fromAddress,
            portfolioMap,
            defaultCurrencyConfig,
            networkMap,
            installationId,
            networkRPCMap,
        })
    )
    const fromAccount = accountsMap[fromAddress]
    switch (loadable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            return (
                <Flow
                    ratesMap={ratesMap}
                    pricesMap={pricesMap}
                    currencies={currencies}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    keyStoreMap={keyStoreMap}
                    portfolio={loadable.data}
                    networkMap={networkMap}
                    accountsMap={accountsMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    installationId={installationId}
                    sessionPassword={sessionPassword}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    contractsMap={contractsMap}
                    fromAccount={fromAccount}
                    toCurrency={toCurrency}
                    currencyPinMap={currencyPinMap}
                    currencyHiddenMap={currencyHiddenMap}
                    onMsg={onMsg}
                />
            )

        case 'loading':
            return (
                <LoadingLayout
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    title={null}
                    onClose={() => {
                        onMsg({ type: 'close' })
                    }}
                />
            )

        case 'error':
            return (
                <>
                    <LoadingLayout
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        title={null}
                        onClose={() => {
                            onMsg({ type: 'close' })
                        }}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({ type: 'close' })
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )

        default:
            return notReachable(loadable)
    }
}
