import { useCallback } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap, KnownCurrencies } from '@zeal/domains/Currency'
import {
    BridgeRequest,
    BridgeSubmitted,
} from '@zeal/domains/Currency/domains/Bridge'
import { BRIDGE_SPONSOR_NETWORKS } from '@zeal/domains/Currency/domains/Bridge/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import {
    FetchSimulationByRequest,
    fetchSimulationByRequest,
} from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchSimulation'
import {
    FetchTransactionResultByRequest,
    fetchTransactionResultByRequest,
} from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'

import { SubmitSourceTransactionsEOA } from './SubmitSourceTransactionsEOA'

type Props = {
    request: BridgeRequest
    sessionPassword: string
    account: Account
    accountMap: AccountsMap
    serverPortfolio: ServerPortfolio2
    keystoreMap: KeyStoreMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'source_transaction_submitted'; request: BridgeSubmitted }
    | MsgOf<typeof SubmitSourceTransactionsEOA>
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_cancel_confirm_transaction_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_safe_transaction_failure_accepted'
                  | 'on_wrong_network_accepted'
          }
      >

export const SubmitSourceTransactions = ({
    request,
    sessionPassword,
    account,
    accountMap,
    keystoreMap,
    installationId,
    networkMap,
    networkRPCMap,
    feePresetMap,
    serverPortfolio,
    onMsg,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
}: Props) => {
    const keyStore = getKeyStore({
        keyStoreMap: keystoreMap,
        address: account.address,
    })

    const fetchBridgeSimulation = useCallback<FetchSimulationByRequest>(
        async ({ network, requestToSimulate }) => {
            const resp = await fetchSimulationByRequest({
                network,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                requestToSimulate,
                dApp: null,
            })
            switch (resp.type) {
                case 'failed':
                case 'not_supported':
                    return resp
                case 'simulated':
                    return {
                        ...resp,
                        simulation: {
                            ...resp.simulation,
                            currencies: {
                                ...resp.simulation.currencies,
                                ...[
                                    request.route.from.currency,
                                    request.route.to.currency,
                                    defaultCurrencyConfig.defaultCurrency,
                                ].reduce((acc, currency) => {
                                    acc[currency.id] = currency
                                    return acc
                                }, {} as KnownCurrencies),
                            },
                            transaction: {
                                type: 'BridgeTrx' as const,
                                bridgeRoute: request.route,
                            },
                        },
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(resp)
            }
        },
        [defaultCurrencyConfig, request.route, networkMap, networkRPCMap]
    )

    const fetchBridgeTxResult = useCallback<FetchTransactionResultByRequest>(
        async ({ network, request: resultRequest, signal }) => {
            const resp = await fetchTransactionResultByRequest({
                network,
                request: resultRequest,
                signal,
            })
            return {
                ...resp,
                transaction: {
                    type: 'BridgeTrx' as const,
                    bridgeRoute: request.route,
                },
                currencies: {
                    ...resp.currencies,
                    ...[
                        request.route.from.currency,
                        request.route.to.currency,
                        defaultCurrencyConfig.defaultCurrency,
                    ].reduce((acc, currency) => {
                        acc[currency.id] = currency
                        return acc
                    }, {} as KnownCurrencies),
                },
            }
        },
        [defaultCurrencyConfig.defaultCurrency, request.route]
    )

    switch (keyStore.type) {
        case 'safe_4337':
            const transactionsToBundle: NonEmptyArray<EthSendTransaction> =
                request.route.approvalTransaction
                    ? [
                          request.route.approvalTransaction,
                          request.route.sourceTransaction,
                      ]
                    : [request.route.sourceTransaction]
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    fetchSimulationByRequest={fetchBridgeSimulation}
                    fetchTransactionResultByRequest={fetchBridgeTxResult}
                    network={findNetworkByHexChainId(
                        request.route.from.currency.networkHexChainId,
                        networkMap
                    )}
                    networksToSponsor={BRIDGE_SPONSOR_NETWORKS}
                    networkRPCMap={networkRPCMap}
                    account={account}
                    networkMap={networkMap}
                    sessionPassword={sessionPassword}
                    portfolio={serverPortfolio}
                    state={{ type: 'maximised' }}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    actionSource={{
                        type: 'internal',
                        transactionEventSource: 'bridge',
                        dAppSiteInfo: {
                            title: request.route.displayName,
                            avatar: request.route.icon,
                            hostname: '',
                        },
                    }}
                    sendTransactionRequests={transactionsToBundle}
                    accounts={accountMap}
                    keystores={keystoreMap}
                    feePresetMap={feePresetMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'import_keys_button_clicked':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'cancel_submitted':
                            case 'on_completed_transaction_close_click':
                            case 'transaction_failure_accepted':
                            case 'transaction_submited':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'transaction_request_replaced':
                                noop() // Not relevant to smart wallet
                                break
                            case 'on_minimize_click':
                                onMsg({ type: 'close' })
                                break
                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in bridge $${msg.type}`
                                    )
                                )
                                break

                            case 'on_user_operation_bundled':
                                noop()
                                break

                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                                onMsg({
                                    type: 'source_transaction_submitted',
                                    request: {
                                        type: 'bridge_submitted',
                                        sourceTransactionHash:
                                            msg.userOperation
                                                .bundleTransactionHash,
                                        route: request.route,
                                        submittedAtMS: Date.now(),
                                        fromAddress:
                                            account.address as Web3.address.Address,
                                    },
                                })
                                break
                            case 'on_completed_safe_transaction_close_click':
                                onMsg({
                                    type: 'source_transaction_submitted',
                                    request: {
                                        type: 'bridge_submitted',
                                        sourceTransactionHash:
                                            msg.completedTransaction
                                                .bundleTransactionHash,
                                        route: request.route,
                                        submittedAtMS: Date.now(),
                                        fromAddress:
                                            account.address as Web3.address.Address,
                                    },
                                })
                                break
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_safe_transaction_failure_accepted':
                            case 'on_wrong_network_accepted':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return (
                <SubmitSourceTransactionsEOA
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    fetchBridgeSimulation={fetchBridgeSimulation}
                    fetchBridgeTxResult={fetchBridgeTxResult}
                    request={request}
                    sessionPassword={sessionPassword}
                    account={account}
                    accountMap={accountMap}
                    serverPortfolio={serverPortfolio}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    installationId={installationId}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
