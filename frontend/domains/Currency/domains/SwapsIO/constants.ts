import * as Web3 from '@zeal/toolkit/Web3'
import { staticFromString } from '@zeal/toolkit/Web3/address'

export const SWAPS_IO_NATIVE_CURRENCY_ADDRESS = staticFromString(
    '0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'
)
export const SWAPS_IO_DIAMOND_CONTRACTS = new Set([
    Web3.address.staticFromString('0x8271becad4c7114488461bed1b9193d4a5126797'), // gnosis
    Web3.address.staticFromString('******************************************'), // bsc
    Web3.address.staticFromString('******************************************'), // pol
    Web3.address.staticFromString('******************************************'), // avax
    Web3.address.staticFromString('******************************************'), // arb
    Web3.address.staticFromString('******************************************'), // base
    Web3.address.staticFromString('******************************************'), // eth
    Web3.address.staticFromString('******************************************'), // op
])

export const SWAPS_IO_NATIVE_SWAP_GAS_AMOUNT = 500_000n
