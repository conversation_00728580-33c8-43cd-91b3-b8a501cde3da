import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { SwapsIOERC20SwapQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import { FAKE_SWAPS_IO_DAPP } from '@zeal/domains/DApp/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio } from '@zeal/domains/Portfolio'
import { EthSignTypedDataV4 } from '@zeal/domains/RPCRequest'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { Sign } from '@zeal/domains/RPCRequest/features/Sign'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { SwapsioEventSource } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap

    installationId: string
    sessionPassword: string
    senderPortfolio: ServerPortfolio
    network: Network
    request: EthSignTypedDataV4
    quote: SwapsIOERC20SwapQuote
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage
    defaultCurrencyConfig: DefaultCurrencyConfig
    source: SwapsioEventSource
    onMsg: (msg: Msg) => void
}

type Msg = Extract<
    MsgOf<typeof Sign>,
    {
        type:
            | 'close'
            | 'on_4337_auto_gas_token_selection_clicked'
            | 'on_4337_gas_currency_selected'
            | 'import_keys_button_clicked'
            | 'message_signed'
    }
>

export const SignSwap = ({
    quote,
    keyStoreMap,
    networkMap,
    senderPortfolio,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    networkRPCMap,
    sessionPassword,
    installationId,
    network,
    simulation,
    request,
    defaultCurrencyConfig,
    source,
    onMsg,
}: Props) => {
    return (
        <Sign
            defaultCurrencyConfig={defaultCurrencyConfig}
            sessionPassword={sessionPassword}
            keyStore={getKeyStore({
                address: quote.sender.address,
                keyStoreMap,
            })}
            request={request}
            state={{
                type: 'maximised',
            }}
            account={quote.sender}
            network={network}
            networkMap={networkMap}
            actionSource={{
                type: 'internal_sign',
                dAppSiteInfo: FAKE_SWAPS_IO_DAPP,
                transactionEventSource: 'swapsioSign',
            }}
            installationId={installationId}
            keyStoreMap={keyStoreMap}
            fetchSimulatedSignMessage={async () => ({
                type: 'simulated',
                simulationResponse: {
                    checks: [],
                    message: simulation,
                    currencies: {},
                },
            })}
            onMsg={(msg) => {
                switch (msg.type) {
                    case 'drag':
                    case 'on_expand_request':
                    case 'on_cancel_confirm_transaction_clicked':
                        captureError(
                            new ImperativeError(
                                `impossible messages during sigin msg in swaps IO preparation $${msg.type}`
                            )
                        )
                        onMsg({ type: 'close' })
                        break
                    case 'cancel_button_click':
                        postUserEvent({
                            type: 'SwapsioSignatureEvent',
                            action: 'cancel',
                            installationId,
                            source,
                        })
                        onMsg({ type: 'close' })
                        break
                    case 'close':
                    case 'on_minimize_click':
                    case 'on_safe_transaction_failure_accepted':
                    case 'on_wrong_network_accepted':
                    case 'on_safe_deployemnt_cancelled':
                    case 'on_safe_deployment_error_popup_cancel_clicked':
                        onMsg({ type: 'close' })
                        break
                    case 'import_keys_button_clicked':
                    case 'on_4337_auto_gas_token_selection_clicked':
                    case 'on_4337_gas_currency_selected':
                        onMsg(msg)
                        break
                    case 'message_signed':
                        postUserEvent({
                            type: 'SwapsioSignatureEvent',
                            action: 'accept',
                            installationId,
                            source,
                        })
                        onMsg(msg)
                        break

                    /* istanbul ignore next */
                    default:
                        notReachable(msg)
                }
            }}
            networkRPCMap={networkRPCMap}
            accountsMap={accountsMap}
            feePresetMap={feePresetMap}
            gasCurrencyPresetMap={gasCurrencyPresetMap}
            portfolio={senderPortfolio}
        />
    )
}
