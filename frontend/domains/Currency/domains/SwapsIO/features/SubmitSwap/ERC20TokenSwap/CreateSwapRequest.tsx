import { useEffect } from 'react'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import {
    SwapsIOERC20SwapQuote,
    SwapsIOSwapRequest,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { createSwapWithRetries } from '@zeal/domains/Currency/domains/SwapsIO/api/createSwap'
import { fetchSwapMsgToSignWithRetries } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchSwapMsgToSign'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap } from '@zeal/domains/Network'
import { EthSignTypedDataV4 } from '@zeal/domains/RPCRequest'

type Props = {
    onMsg: (msg: Msg) => void
    quote: SwapsIOERC20SwapQuote
    networkMap: NetworkMap
    installationId: string
}

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'on_swap_requet_created'
          request: SwapsIOSwapRequest
          msgToSign: EthSignTypedDataV4
      }

const fetch = async ({
    quote,
    networkMap,
    signal,
}: {
    quote: SwapsIOERC20SwapQuote
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<{
    request: SwapsIOSwapRequest
    msgToSign: EthSignTypedDataV4
}> => {
    const request = await createSwapWithRetries({ quote, networkMap, signal })
    const msgToSign = await fetchSwapMsgToSignWithRetries({
        request,
        networkMap,
        signal,
    })

    return {
        request,
        msgToSign,
    }
}
export const CreateSwapRequest = ({
    installationId,
    quote,
    networkMap,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            quote,
            networkMap,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_swap_requet_created',
                    request: loadable.data.request,
                    msgToSign: loadable.data.msgToSign,
                })
                break
            case 'loading':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [liveOnMsg, loadable])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error': {
            const error = parseAppError(loadable.error)

            return (
                <AppErrorPopup
                    error={error}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg({ type: 'close' })
                                break
                            case 'try_again_clicked':
                                setLoadable({
                                    type: 'loading',
                                    params: loadable.params,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
