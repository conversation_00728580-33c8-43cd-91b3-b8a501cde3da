import { useEffect } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useCurrentTimestamp } from '@zeal/toolkit/Date/useCurrentTimestamp'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import { SwapsIOSwapRequest } from '@zeal/domains/Currency/domains/SwapsIO'
import { submitSwap } from '@zeal/domains/Currency/domains/SwapsIO/api/submitSwap'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap } from '@zeal/domains/Network'
import { EthSignTypedDataV4 } from '@zeal/domains/RPCRequest'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { SwapsioEventSource } from '@zeal/domains/UserEvents'

import { Layout } from './Layout'

type Props = {
    installationId: string
    request: SwapsIOSwapRequest
    signature: Hexadecimal
    msgToSign: EthSignTypedDataV4
    sender: Account
    source: SwapsioEventSource
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_swap_submitted'
          request: SwapsIOSwapRequest
      }

export const SendSwap = ({
    request,
    installationId,
    signature,
    msgToSign,
    sender,
    simulation,
    source,
    networkMap,
    onMsg,
}: Props) => {
    const now = useCurrentTimestamp({ refreshIntervalMs: 1000 })
    const [loadable, setLoadable] = useLoadableData(submitSwap, {
        type: 'loading',
        params: { request, signature },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_swap_submitted',
                    request: loadable.data,
                })
                break
            case 'loading':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [installationId, liveOnMsg, loadable, source])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <Layout
                    networkMap={networkMap}
                    now={now}
                    request={msgToSign}
                    account={sender}
                    onMsg={onMsg}
                    simulation={simulation}
                />
            )
        case 'error': {
            const error = parseAppError(loadable.error)

            return (
                <>
                    <Layout
                        networkMap={networkMap}
                        now={now}
                        request={msgToSign}
                        account={sender}
                        simulation={simulation}
                        onMsg={onMsg}
                    />
                    <AppErrorPopup
                        error={error}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg({ type: 'close' })
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
