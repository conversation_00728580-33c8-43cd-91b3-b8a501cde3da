import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Content as UiKitContent } from '@zeal/uikit/Content'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { OrderBuyHeader } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderBuyHeader'
import { OrderCardTopUpHeader } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderCardTopUpHeader'
import { OrderEarnDepositBridgeHeader } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderEarnDepositBridgeHeader'
import { NetworkMap } from '@zeal/domains/Network'
import { SignMessageRequest } from '@zeal/domains/RPCRequest'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { Content } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/components/Content'

type Props = {
    now: number
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage
    account: Account
    request: SignMessageRequest
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const Layout = ({
    networkMap,
    request,
    account,
    simulation,
    now,
    onMsg,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} shrink alignY="stretch">
                <UIActionBar
                    left={<ActionBarAccountIndicator account={account} />}
                    size="small"
                    right={null}
                />
                <UiKitContent
                    header={(() => {
                        switch (simulation.type) {
                            case 'OrderCardTopupSignMessage':
                                return (
                                    <OrderCardTopUpHeader
                                        simulationMessage={simulation}
                                    />
                                )
                            case 'OrderEarnDepositBridge':
                                return (
                                    <OrderEarnDepositBridgeHeader
                                        simulationMessage={simulation}
                                    />
                                )
                            case 'OrderBuySignMessage':
                                return (
                                    <OrderBuyHeader
                                        simulationMessage={simulation}
                                    />
                                )

                            /* istanbul ignore next */
                            default:
                                notReachable(simulation)
                        }
                    })()}
                >
                    <Content
                        networkMap={networkMap}
                        result={{
                            type: 'simulated',
                            simulationResponse: {
                                checks: [],
                                message: simulation,
                                currencies: {},
                            },
                        }}
                        request={request}
                        nowTimestampMs={now}
                    />
                </UiKitContent>
                <Row spacing={0}>
                    <Button
                        disabled
                        size="regular"
                        variant="secondary"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        <FormattedMessage
                            id="action.close"
                            defaultMessage="Close"
                        />
                    </Button>
                </Row>
            </Column>
        </Screen>
    )
}
