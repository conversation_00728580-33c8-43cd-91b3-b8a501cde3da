import { useEffect } from 'react'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { useLiveRef } from '@zeal/toolkit/React'

import {
    SwapsIONativeSwapQuote,
    SwapsIOSwapRequest,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { createSwapWithRetries } from '@zeal/domains/Currency/domains/SwapsIO/api/createSwap'
import { manualReceiveWithRetries } from '@zeal/domains/Currency/domains/SwapsIO/api/manualReceive'
import { submitSwapWithoutSignature } from '@zeal/domains/Currency/domains/SwapsIO/api/submitSwap'
import { SWAPS_IO_DIAMOND_CONTRACTS } from '@zeal/domains/Currency/domains/SwapsIO/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { NetworkMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'

type Props = {
    onMsg: (msg: Msg) => void
    networkMap: NetworkMap
    quote: SwapsIONativeSwapQuote
    installationId: string
}

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'on_swap_request_created'
          request: SwapsIOSwapRequest
          transaction: EthSendTransaction
      }

const fetch = async ({
    quote,
    networkMap,
    signal,
}: {
    quote: SwapsIONativeSwapQuote
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<{
    request: SwapsIOSwapRequest
    transaction: EthSendTransaction
}> => {
    const request = await createSwapWithRetries({ quote, networkMap, signal })

    await submitSwapWithoutSignature({
        request,
        signal,
    })

    const data = await manualReceiveWithRetries({
        request,
        signal,
    })

    if (
        !SWAPS_IO_DIAMOND_CONTRACTS.has(data.toAddress) ||
        data.fromAddress !== request.sender ||
        data.value !== request.from.amount
    ) {
        throw new ImperativeError(`Swaps.io native swap data mismatched`, {
            data: data,
            request: request,
        })
    }

    const swapTransaction: EthSendTransaction = {
        id: generateRandomNumber(),
        jsonrpc: '2.0',
        method: 'eth_sendTransaction',
        params: [
            {
                from: data.fromAddress,
                to: data.toAddress,
                data: data.data,
                value: fromBigInt(data.value),
            },
        ],
    }

    return {
        request,
        transaction: swapTransaction,
    }
}
export const CreateSwapRequest = ({
    installationId,
    networkMap,
    quote,
    onMsg,
}: Props) => {
    const [loadable, setLoadable] = useLoadableData(fetch, {
        type: 'loading',
        params: {
            quote,
            networkMap,
        },
    })

    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'loaded':
                liveOnMsg.current({
                    type: 'on_swap_request_created',
                    request: loadable.data.request,
                    transaction: loadable.data.transaction,
                })
                break
            case 'loading':
            case 'error':
                break
            /* istanbul ignore next */
            default:
                notReachable(loadable)
        }
    }, [liveOnMsg, loadable])

    switch (loadable.type) {
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error': {
            const error = parseAppError(loadable.error)

            return (
                <AppErrorPopup
                    error={error}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg({ type: 'close' })
                                break
                            case 'try_again_clicked':
                                setLoadable({
                                    type: 'loading',
                                    params: loadable.params,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        }

        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
