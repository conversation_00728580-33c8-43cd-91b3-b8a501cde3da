import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import {
    SwapsIONativeSwapQuote,
    SwapsIOSwapRequest,
} from '@zeal/domains/Currency/domains/SwapsIO'
import { FAKE_SWAPS_IO_DAPP } from '@zeal/domains/DApp/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { ActionSource2 } from '@zeal/domains/Main'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio } from '@zeal/domains/Portfolio'
import { serverPortfolioToServerPortfolio2 } from '@zeal/domains/Portfolio/helpers/portfolioToPortfolio2'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { SendTransaction } from '@zeal/domains/RPCRequest/features/SendTransaction'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchTransactionResultByRequest } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fetchTransactionResult'
import { SwapsioEventSource } from '@zeal/domains/UserEvents'

import { CreateSwapRequest } from './CreateSwapRequest'

import { Monitor } from '../Monitor'

type Props = {
    quote: SwapsIONativeSwapQuote
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage

    keyStoreMap: KeyStoreMap
    networkMap: NetworkMap
    accountsMap: AccountsMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    sessionPassword: string
    senderPortfolio: ServerPortfolio
    defaultCurrencyConfig: DefaultCurrencyConfig
    source: SwapsioEventSource
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<
          MsgOf<typeof SendTransaction>,
          {
              type:
                  | 'on_predefined_fee_preset_selected'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
          }
      >
    | Extract<MsgOf<typeof CreateSwapRequest>, { type: 'close' }>
    | MsgOf<typeof Monitor>
    | {
          type: 'on_swaps_io_swap_request_created'
          request: SwapsIOSwapRequest
      }

type State =
    | {
          type: 'create_swap_request'
      }
    | {
          type: 'send_transaction'
          transaction: EthSendTransaction
          request: SwapsIOSwapRequest
      }
    | {
          type: 'monitor'
          request: SwapsIOSwapRequest
      }

const SWAPS_IO_NATIVE_SWAP_ACTION_SOURCE: ActionSource2 = {
    type: 'internal',
    transactionEventSource: 'kinetexNativeSwap',
    dAppSiteInfo: FAKE_SWAPS_IO_DAPP,
}

export const NativeTokenSwap = ({
    quote,
    keyStoreMap,
    networkMap,
    senderPortfolio,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    networkRPCMap,
    onMsg,
    sessionPassword,
    simulation,
    installationId,
    defaultCurrencyConfig,
    source,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'create_swap_request' })
    const network = findNetworkByHexChainId(
        quote.from.currency.networkHexChainId,
        networkMap
    )

    switch (state.type) {
        case 'create_swap_request':
            return (
                <CreateSwapRequest
                    installationId={installationId}
                    networkMap={networkMap}
                    quote={quote}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_swap_request_created':
                                setState({
                                    type: 'send_transaction',
                                    request: msg.request,
                                    transaction: msg.transaction,
                                })
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'send_transaction':
            return (
                <SendTransaction
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    sendTransactionRequests={[state.transaction]}
                    account={quote.sender}
                    network={network}
                    networkRPCMap={networkRPCMap}
                    sessionPassword={sessionPassword}
                    portfolio={serverPortfolioToServerPortfolio2({
                        serverPortfolio: senderPortfolio,
                    })}
                    accounts={accountsMap}
                    keystores={keyStoreMap}
                    networkMap={networkMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    fetchSimulationByRequest={async () => ({
                        type: 'simulated',
                        simulation: {
                            transaction: {
                                type: 'swaps_io_native_token_swap',
                                swapsIOQuote: quote,
                            },
                            currencies: {},
                            checks: [],
                        },
                    })}
                    fetchTransactionResultByRequest={
                        fetchTransactionResultByRequest
                    }
                    state={{ type: 'maximised' }}
                    actionSource={SWAPS_IO_NATIVE_SWAP_ACTION_SOURCE}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_minimize_click':
                            case 'on_cancel_confirm_transaction_clicked':
                            case 'on_wrong_network_accepted':
                            case 'transaction_failure_accepted':
                            case 'on_sign_cancel_button_clicked':
                            case 'on_close_transaction_status_not_found_modal':
                            case 'on_transaction_cancelled_successfully_close_clicked':
                            case 'transaction_cancel_failure_accepted':
                            case 'on_safe_transaction_failure_accepted':
                                onMsg({ type: 'close' })
                                break

                            case 'cancel_submitted':
                            case 'transaction_submited':
                            case 'transaction_request_replaced':
                            case 'on_user_operation_bundled':
                                noop()
                                break
                            case 'on_safe_4337_transaction_completed_splash_animation_screen_competed':
                            case 'on_transaction_completed_splash_animation_screen_competed':
                            case 'on_completed_transaction_close_click':
                            case 'on_completed_safe_transaction_close_click':
                                setState({
                                    type: 'monitor',
                                    request: state.request,
                                })
                                break

                            case 'drag':
                            case 'on_expand_request':
                                captureError(
                                    new ImperativeError(
                                        `impossible messages during sending transactions in swaps IO native send $${msg.type}`
                                    )
                                )
                                onMsg({ type: 'close' })
                                break

                            case 'on_predefined_fee_preset_selected':
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                                onMsg(msg)
                                break

                            /* istanbul ignore next */
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            )
        case 'monitor':
            return (
                <Monitor
                    installationId={installationId}
                    source={source}
                    simulation={simulation}
                    sender={quote.sender}
                    networkMap={networkMap}
                    request={state.request}
                    onMsg={onMsg}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
