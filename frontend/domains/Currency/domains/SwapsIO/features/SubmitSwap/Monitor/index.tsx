import { useEffect, useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { useCurrentTimestamp } from '@zeal/toolkit/Date/useCurrentTimestamp'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import { SwapsIOSwapRequest } from '@zeal/domains/Currency/domains/SwapsIO'
import { fetchSwapStateWithRetries } from '@zeal/domains/Currency/domains/SwapsIO/api/fetchSwapState'
import { useCaptureErrorOnce } from '@zeal/domains/Error/hooks/useCaptureErrorOnce'
import { NetworkMap } from '@zeal/domains/Network'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { SwapsioEventSource } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Layout } from './Layout'

type Props = {
    sender: Account
    request: SwapsIOSwapRequest
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage
    networkMap: NetworkMap
    installationId: string
    source: SwapsioEventSource
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Layout>

export const Monitor = ({
    simulation,
    sender,
    request,
    networkMap,
    installationId,
    source,
    onMsg,
}: Props) => {
    const [startedAt] = useState<number>(Date.now())
    const now = useCurrentTimestamp({ refreshIntervalMs: 1000 })

    const [pollable] = useLoadedPollableData(
        fetchSwapStateWithRetries,
        { type: 'loaded', params: { request }, data: request },
        {
            pollIntervalMilliseconds: 5000,
            stopIf: (loadable) => {
                switch (loadable.type) {
                    case 'loaded': {
                        switch (loadable.data.state) {
                            case 'completed_liq_sent':
                            case 'cancelled_no_slash':
                            case 'cancelled_slashed':
                            case 'completed_sent':
                                return true

                            case 'cancelled_awaiting_slash':
                            case 'awaiting_receive':
                            case 'awaiting_liq_send':
                            case 'awaiting_signature':
                            case 'awaiting_send':
                                return false

                            /* istanbul ignore next */
                            default:
                                return notReachable(loadable.data)
                        }
                    }
                    case 'reloading':
                    case 'subsequent_failed':
                        return false

                    /* istanbul ignore next */
                    default:
                        return notReachable(loadable)
                }
            },
        }
    )

    const captureError = useCaptureErrorOnce()

    const onLiveMsg = useLiveRef(onMsg)
    useEffect(() => {
        switch (pollable.type) {
            case 'loaded': {
                switch (pollable.data.state) {
                    case 'completed_liq_sent':
                    case 'completed_sent':
                        postUserEvent({
                            type: 'SwapsioCompletedEvent',
                            source,
                            installationId,
                        })
                        break

                    case 'cancelled_awaiting_slash':
                    case 'awaiting_receive':
                    case 'awaiting_liq_send':
                    case 'awaiting_signature':
                    case 'awaiting_send':
                        break

                    case 'cancelled_no_slash':
                    case 'cancelled_slashed':
                        postUserEvent({
                            type: 'SwapsioFailedEvent',
                            source,
                            installationId,
                        })
                        break
                    /* istanbul ignore next */
                    default:
                        notReachable(pollable.data)
                        break
                }
                break
            }
            case 'reloading':
            case 'subsequent_failed':
                break

            /* istanbul ignore next */
            default:
                notReachable(pollable)
                break
        }
    }, [installationId, pollable, source, onLiveMsg, request.state])

    useEffect(() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
                break
            case 'subsequent_failed':
                captureError(pollable.error)
                break

            default:
                return notReachable(pollable)
        }
    }, [pollable, captureError])

    return (
        <Layout
            networkMap={networkMap}
            startedAt={startedAt}
            now={now}
            simulation={simulation}
            swapRequest={pollable.data}
            account={sender}
            onMsg={onMsg}
        />
    )
}
