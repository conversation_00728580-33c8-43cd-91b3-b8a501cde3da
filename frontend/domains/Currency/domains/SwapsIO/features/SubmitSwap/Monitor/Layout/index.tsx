import React from 'react'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { Content as UiKitContent } from '@zeal/uikit/Content'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { SwapsIOSwapRequest } from '@zeal/domains/Currency/domains/SwapsIO'
import { OrderBuyHeader } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderBuyHeader'
import { OrderBuyView } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderBuyView'
import { OrderCardTopUpHeader } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderCardTopUpHeader'
import { OrderCardTopUpView } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderCardTopUpView'
import { OrderEarnDepositBridgeHeader } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderEarnDepositBridgeHeader'
import { OrderEarnDepositBridgeView } from '@zeal/domains/Currency/domains/SwapsIO/components/OrderEarnDepositBridgeView'
import { NetworkMap } from '@zeal/domains/Network'
import {
    OrderBuySignMessage,
    OrderCardTopupSignMessage,
    OrderEarnDepositBridge,
} from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

import { Actions } from './Actions'
import { Footer } from './Footer'

type Props = {
    swapRequest: SwapsIOSwapRequest
    simulation:
        | OrderCardTopupSignMessage
        | OrderEarnDepositBridge
        | OrderBuySignMessage
    account: Account
    now: number
    startedAt: number
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Actions> | { type: 'close' }

export const Layout = ({
    simulation,
    account,
    swapRequest,
    now,
    startedAt,
    networkMap,
    onMsg,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} shrink alignY="stretch">
                <UIActionBar
                    left={<ActionBarAccountIndicator account={account} />}
                    size="small"
                    right={null}
                />
                <UiKitContent
                    header={(() => {
                        switch (simulation.type) {
                            case 'OrderCardTopupSignMessage':
                                return (
                                    <OrderCardTopUpHeader
                                        simulationMessage={simulation}
                                    />
                                )
                            case 'OrderEarnDepositBridge':
                                return (
                                    <OrderEarnDepositBridgeHeader
                                        simulationMessage={simulation}
                                    />
                                )
                            case 'OrderBuySignMessage':
                                return (
                                    <OrderBuyHeader
                                        simulationMessage={simulation}
                                    />
                                )

                            /* istanbul ignore next */
                            default:
                                notReachable(simulation)
                        }
                    })()}
                    footer={
                        <Footer
                            swapRequest={swapRequest}
                            now={now}
                            startedAt={startedAt}
                        />
                    }
                >
                    {(() => {
                        switch (simulation.type) {
                            case 'OrderCardTopupSignMessage':
                                return (
                                    <OrderCardTopUpView
                                        networkMap={networkMap}
                                        simulatedSignMessage={simulation}
                                    />
                                )
                            case 'OrderEarnDepositBridge':
                                return (
                                    <OrderEarnDepositBridgeView
                                        simulatedSignMessage={simulation}
                                    />
                                )
                            case 'OrderBuySignMessage':
                                return (
                                    <OrderBuyView
                                        networkMap={networkMap}
                                        simulatedSignMessage={simulation}
                                    />
                                )
                            default:
                                return notReachable(simulation)
                        }
                    })()}
                </UiKitContent>
                <Row spacing={0}>
                    <Actions swapRequest={swapRequest} onMsg={onMsg} />
                </Row>
            </Column>
        </Screen>
    )
}
