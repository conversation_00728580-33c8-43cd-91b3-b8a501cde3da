import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { NetworkHexId } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'

export type SwapsIONativeSwapQuote = {
    type: 'swaps_io_quote_native_swap'
    from: CryptoMoney
    to: CryptoMoney
    fee: FiatMoney | null
    fromInDefaultCurrency: FiatMoney | null
    toInDefaultCurrency: FiatMoney | null
    serviceTimeMs: number

    sender: Account
    receiver: Web3.address.Address | null
}

export type SwapsIOERC20SwapQuote = {
    type: 'swaps_io_quote_erc20_swap'
    from: CryptoMoney
    to: CryptoMoney
    fee: FiatMoney | null
    fromInDefaultCurrency: FiatMoney | null
    toInDefaultCurrency: FiatMoney | null
    serviceTimeMs: number

    sender: Account
    receiver: Web3.address.Address | null
    approvalTransaction: EthSendTransaction | null
}

export type SwapsIOQuote = SwapsIOERC20SwapQuote | SwapsIONativeSwapQuote

export type SwapsIOSwapCommon = {
    from: CryptoMoney
    to: CryptoMoney
    sender: Web3.address.Address
    receiver: Web3.address.Address
    hash: Hexadecimal
    createdAt: Date
}
export type SwapsIOSwapRequest = SwapsIOSwapCommon &
    (
        | { state: 'cancelled_no_slash' }
        | { state: 'cancelled_awaiting_slash' }
        | { state: 'cancelled_slashed' }
        | { state: 'awaiting_signature' }
        | { state: 'awaiting_receive' }
        | {
              state: 'awaiting_send'
              fromTransactionHash: Hexadecimal
              fromTransactionCreatedAt: Date
          }
        | {
              state: 'completed_sent'
              fromTransaction: {
                  hash: Hexadecimal
                  createdAt: Date
              } | null
              toTransactionHash: Hexadecimal
              toTransactionCreatedAt: Date
          }
        | {
              state: 'awaiting_liq_send'
              fromTransactionHash: Hexadecimal
              fromTransactionCreatedAt: Date
          }
        | {
              state: 'completed_liq_sent'
              fromTransaction: {
                  hash: Hexadecimal
                  createdAt: Date
              } | null
              toTransactionHash: Hexadecimal
              toTransactionCreatedAt: Date
          }
    )

export type SwapsIOContractsMap = Record<NetworkHexId, Web3.address.Address>

export type SwapsIOSwapRequestsMap = Record<
    Web3.address.Address,
    SwapsIOSwapRequest[] | null
>
