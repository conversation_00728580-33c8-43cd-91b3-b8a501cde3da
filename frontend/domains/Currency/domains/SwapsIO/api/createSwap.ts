import { post } from '@zeal/api/swapsIOApi'

import { withRetries } from '@zeal/toolkit/Function'
import * as Web3 from '@zeal/toolkit/Web3'

import { NetworkMap } from '@zeal/domains/Network'

import { SwapsIOQuote, SwapsIOSwapRequest } from '..'
import { getSwapsIOTokenAddress } from '../helpers/getSwapsIOTokenAddress'
import { parseSwapRequest } from '../parsers/parseSwapRequest'
import { parseSwapsIOSwapRequest } from '../parsers/parseSwapsIOSwapRequest'

const NUM_RETRIES = 2
const RETRY_DELAY_MS = 2000

const createSwap = async ({
    quote,
    networkMap,
    signal,
}: {
    quote: SwapsIOQuote
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<SwapsIOSwapRequest> => {
    const fromCurrency = quote.from.currency
    const toCurrency = quote.to.currency

    const {
        sender,
        receiver,
        from: { amount },
    } = quote

    const response = await post(
        '/swaps',
        {
            body: {
                from_actor: sender.address as Web3.address.Address,
                from_actor_receiver:
                    receiver || (sender.address as Web3.address.Address),
                from_amount: amount.toString(10),
                from_chain_id: Number(fromCurrency.networkHexChainId).toString(
                    10
                ),
                from_token_address: getSwapsIOTokenAddress({
                    cryptoCurrency: fromCurrency,
                    networkMap,
                }),

                to_chain_id: Number(toCurrency.networkHexChainId).toString(10),
                to_token_address: getSwapsIOTokenAddress({
                    cryptoCurrency: toCurrency,
                    networkMap,
                }),
            },
        },
        signal
    )

    const rawRequest = parseSwapRequest(response).getSuccessResultOrThrow(
        'Failed to parse raw swaps.io swap request'
    )

    const swapsIOSwapRequest = parseSwapsIOSwapRequest({
        rawSwapRequest: rawRequest,
        knownCurrencies: {
            [toCurrency.id]: toCurrency,
            [fromCurrency.id]: fromCurrency,
        },
    }).getSuccessResultOrThrow(`Failed to parse swaps.io swap request`)

    return swapsIOSwapRequest
}

export const createSwapWithRetries = withRetries(
    createSwap,
    NUM_RETRIES,
    RETRY_DELAY_MS
)
