import { post } from '@zeal/api/swapsIOApi'

import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { Result } from '@zeal/toolkit/Result'

import { SwapsIOSwapRequest } from '..'
import { parseSwapRequest } from '../parsers/parseSwapRequest'
import { parseSwapsIOSwapRequest } from '../parsers/parseSwapsIOSwapRequest'

export const submitSwap = async ({
    request,
    signature,
    signal,
}: {
    request: SwapsIOSwapRequest
    signature: Hexadecimal.Hexadecimal
    signal?: AbortSignal
}): Promise<SwapsIOSwapRequest> => {
    const response = await post(
        `/swaps/${request.hash}/submit`,
        {
            body: { signature },
        },
        signal
    )

    return parse(response, request).getSuccessResultOrThrow(
        'Failed to parse raw swaps.io swap request'
    )
}

export const submitSwapWithoutSignature = async ({
    request,
    signal,
}: {
    request: SwapsIOSwapRequest
    signal?: AbortSignal
}): Promise<SwapsIOSwapRequest> => {
    const response = await post(
        `/swaps/${request.hash}/submit`,
        { body: {} },
        signal
    )

    return parse(response, request).getSuccessResultOrThrow(
        'Failed to parse raw swaps.io swap request'
    )
}

const parse = (
    response: unknown,
    request: SwapsIOSwapRequest
): Result<unknown, SwapsIOSwapRequest> => {
    return parseSwapRequest(response).andThen((rawSwapRequest) => {
        const { from, to } = request
        return parseSwapsIOSwapRequest({
            rawSwapRequest,
            knownCurrencies: {
                [from.currency.id]: from.currency,
                [to.currency.id]: to.currency,
            },
        })
    })
}
