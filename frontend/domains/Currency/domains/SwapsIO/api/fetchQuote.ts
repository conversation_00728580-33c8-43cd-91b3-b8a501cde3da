import { get } from '@zeal/api/swapsIOApi'

import { fromFixedWithFraction, mulByNumber } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import {
    bigint,
    failure,
    match,
    number,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { fetchAllowance } from '@zeal/domains/Currency/api/fetchAllowance'
import { createApprovalTransaction } from '@zeal/domains/Currency/helpers/createApprovalTransaction'
import { parseHttpError } from '@zeal/domains/Error/parsers/parseHttpError'
import { fetchRatesForDefaultCurrency } from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { NetworkHexId, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { parseNetworkHexId } from '@zeal/domains/Network/helpers/parse'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { SwapsIOContractsMap, SwapsIOQuote } from '..'
import { getSwapsIOTokenAddress } from '../helpers/getSwapsIOTokenAddress'

export type SwapsIOQuoteRequest = {
    amount: string | null
    fromCurrency: CryptoCurrency
    toCurrency: CryptoCurrency
    sender: Account
    receiver: Web3.address.Address | null

    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    contractsMap: SwapsIOContractsMap
    defaultCurrencyConfig: DefaultCurrencyConfig
}

type AboveMaximumError = {
    type: 'above_maximum'
    max: CryptoMoney | null // TODO @resetko-zeal currently it's nullable for our weak parsing
}

type BelowMinimumError = {
    type: 'below_minimum'
    min: CryptoMoney | null // TODO @resetko-zeal currently it's nullable for our weak parsing
}

type CurrencyNotSupportedError = {
    type: 'currency_not_supported'
}

export type SwapsIOQuoteValidationsErrors =
    | AboveMaximumError
    | BelowMinimumError
    | CurrencyNotSupportedError
    | { type: 'amount_required' }

type RawQuoteData = {
    from_amount: bigint
    from_chain_id: NetworkHexId
    from_token_address: Web3.address.Address
    time_estimate: number // seconds
    to_amount: bigint
    to_chain_id: NetworkHexId
    to_token_address: Web3.address.Address
}

const MAX_MSG_REGEXP = /^Maximal swap size is ([\d.]+)\s.*$/im
const MIN_MSG_REGEXP = /^Minimal swap size is ([\d.]+)\s.*$/im

const parseAboveMaximumError = ({
    error,
    currency,
}: {
    error: unknown
    currency: CryptoCurrency
}): Result<unknown, AboveMaximumError> =>
    parseHttpError(error)
        .andThen((errObj) => object(errObj.responseBody))
        .andThen((errObj) => object(errObj.extra_data))
        .andThen((errObj) =>
            shape({
                amount: string(errObj.message).map((msg) => {
                    const match = msg.match(MAX_MSG_REGEXP)?.[1] || null

                    if (match) {
                        try {
                            const max = fromFixedWithFraction(
                                match,
                                currency.fraction
                            )
                            /**
                             * TODO @negriienko
                             * Temporary fix
                             * There are currently rounding issues on the swaps.io side.
                             * Remove this when they fix it.
                             */
                            return mulByNumber(max, 0.95)
                        } catch {
                            return null
                        }
                    }

                    return null
                }),
                error_type: match(errObj.error_type, 'user'),
                error_code: match(errObj.error_code, 'maximal_swap_size'),
            })
        )
        .map(({ amount }) => ({
            type: 'above_maximum',
            max: amount ? { amount, currency } : null,
        }))

const parseCurrencyNotSupportedError = ({
    error,
}: {
    error: unknown
}): Result<unknown, CurrencyNotSupportedError> =>
    parseHttpError(error)
        .andThen((errObj) => object(errObj.responseBody))
        .andThen((errObj) => object(errObj.extra_data))
        .andThen((errObj) =>
            shape({
                error_type: match(errObj.error_type, 'user'),
                error_code: match(errObj.error_code, 'crypto_not_supported'),
            })
        )
        .map(() => ({
            type: 'currency_not_supported',
        }))

const parseBelowMinimumError = ({
    error,
    currency,
}: {
    error: unknown
    currency: CryptoCurrency
}): Result<unknown, BelowMinimumError> =>
    parseHttpError(error)
        .andThen((errObj) => object(errObj.responseBody))
        .andThen((errObj) => object(errObj.extra_data))
        .andThen((errObj) =>
            shape({
                amount: string(errObj.message).map((msg) => {
                    const match = msg.match(MIN_MSG_REGEXP)?.[1] || null

                    if (match) {
                        try {
                            const min = fromFixedWithFraction(
                                match,
                                currency.fraction
                            )
                            /**
                             * TODO @negriienko
                             * Temporary fix
                             * There are currently rounding issues on the swaps.io side.
                             * Remove this when they fix it.
                             */
                            return mulByNumber(min, 1.05)
                        } catch {
                            return null
                        }
                    }

                    return null
                }),
                error_type: match(errObj.error_type, 'user'),
                error_code: match(errObj.error_code, 'minimal_swap_size'),
            })
        )
        .map(({ amount }) => ({
            type: 'below_minimum',
            min: amount ? { amount, currency } : null,
        }))

const parseRawQuote = (input: unknown): Result<unknown, RawQuoteData> =>
    object(input).andThen((obj) =>
        shape({
            from_amount: bigint(obj.from_amount),
            from_chain_id: parseNetworkHexId(obj.from_chain_id),
            from_token_address: Web3.address.parse(obj.from_token_address),
            time_estimate: number(obj.time_estimate),
            to_amount: bigint(obj.to_amount),
            to_chain_id: parseNetworkHexId(obj.to_chain_id),
            to_token_address: Web3.address.parse(obj.to_token_address),
            to_actor: Web3.address.parse(obj.to_actor),
        })
    )

export const fetchQuote = async ({
    amount,
    fromCurrency,
    toCurrency,
    receiver,
    sender,
    networkMap,
    networkRPCMap,
    contractsMap,
    defaultCurrencyConfig,
    signal,
}: SwapsIOQuoteRequest & { signal?: AbortSignal }): Promise<
    Result<SwapsIOQuoteValidationsErrors, SwapsIOQuote>
> => {
    const amountBigint = fromFixedWithFraction(amount, fromCurrency.fraction)
    if (amountBigint <= 0n) {
        return failure({ type: 'amount_required' })
    }

    const toTokenAddress = getSwapsIOTokenAddress({
        cryptoCurrency: toCurrency,
        networkMap,
    })

    const fromTokenAddress = getSwapsIOTokenAddress({
        cryptoCurrency: fromCurrency,
        networkMap,
    })

    try {
        const response = await get(
            '/quote',
            {
                query: {
                    from_amount: amountBigint.toString(10),

                    from_chain_id: Number(
                        fromCurrency.networkHexChainId
                    ).toString(10),
                    from_token_address: fromTokenAddress,
                    to_chain_id: Number(toCurrency.networkHexChainId).toString(
                        10
                    ),
                    to_token_address: toTokenAddress,
                },
            },
            signal
        )

        const rawQuote = parseRawQuote(response).getSuccessResultOrThrow(
            'Failed to parse swapsio raw quote'
        )

        if (
            rawQuote.from_amount !== amountBigint ||
            rawQuote.from_chain_id !== fromCurrency.networkHexChainId ||
            rawQuote.from_token_address !== fromTokenAddress ||
            rawQuote.to_chain_id !== toCurrency.networkHexChainId ||
            rawQuote.to_token_address !== toTokenAddress
        ) {
            throw new ImperativeError(
                'Quote response is not matching with request parameters',
                {
                    amount,
                    fromCurrency,
                    toCurrency,
                    rawQuote,
                }
            )
        }

        const from = {
            amount: rawQuote.from_amount,
            currency: fromCurrency, // We keep it same as in params
        }

        const to = { amount: rawQuote.to_amount, currency: toCurrency }

        const fromNetwork = findNetworkByHexChainId(
            from.currency.networkHexChainId,
            networkMap
        )

        const rates = await fetchRatesForDefaultCurrency({
            defaultCurrencyConfig,
            cryptoCurrencies: [from.currency, to.currency],
            networkMap,
            networkRPCMap,
        })

        const fromRate = rates[from.currency.id] || null
        const toRate = rates[to.currency.id] || null

        const fromInDefaultCurrency = fromRate
            ? applyRate2({
                  baseAmount: from,
                  rate: fromRate,
              })
            : null

        const toInDefaultCurrency = toRate
            ? applyRate2({
                  baseAmount: to,
                  rate: toRate,
              })
            : null

        const fee =
            fromInDefaultCurrency && toInDefaultCurrency
                ? fromInDefaultCurrency.amount > toInDefaultCurrency.amount
                    ? sub2(fromInDefaultCurrency, toInDefaultCurrency)
                    : {
                          amount: 0n,
                          currency: fromInDefaultCurrency.currency,
                      }
                : null

        if (fromNetwork.nativeCurrency.id === fromCurrency.id) {
            return success({
                type: 'swaps_io_quote_native_swap',
                from,
                to,
                fee,
                receiver,
                sender,
                fromInDefaultCurrency,
                toInDefaultCurrency,
                serviceTimeMs: rawQuote.time_estimate * 1000,
            })
        }

        const contract = contractsMap[fromNetwork.hexChainId] || null

        if (!contract) {
            throw new ImperativeError(
                'swaps.io contract not found for network',
                { contractsMap, fromNetwork: fromNetwork.hexChainId }
            )
        }

        const amountToCheckAllowance: CryptoMoney = {
            currency: fromCurrency,
            amount: from.amount,
        }

        const currentAllowance = await fetchAllowance({
            currency: amountToCheckAllowance.currency,
            networkMap,
            networkRPCMap,
            owner: sender.address as Web3.address.Address,
            spender: contract,
        })

        const approvalTransaction: EthSendTransaction | null =
            from.amount > currentAllowance.amount
                ? createApprovalTransaction({
                      amount: amountToCheckAllowance,
                      owner: sender.address as Web3.address.Address,
                      spender: contract,
                  })
                : null

        return success({
            type: 'swaps_io_quote_erc20_swap',
            from: from,
            to,
            fee,
            receiver,
            sender,
            fromInDefaultCurrency,
            toInDefaultCurrency,
            serviceTimeMs: rawQuote.time_estimate * 1000,
            approvalTransaction,
        })
    } catch (error) {
        const validationError =
            oneOf(error, [
                parseAboveMaximumError({
                    error,
                    currency: fromCurrency,
                }),
                parseBelowMinimumError({
                    error,
                    currency: fromCurrency,
                }),
                parseCurrencyNotSupportedError({ error }),
            ]).getSuccessResult() || null

        if (validationError) {
            return failure(validationError)
        }

        throw error
    }
}
