import { get } from '@zeal/api/swapsIOApi'

import { withRetries } from '@zeal/toolkit/Function'
import {
    Hexadecimal,
    parse as parseHexadecimal,
} from '@zeal/toolkit/Hexadecimal'
import { bigint, object, Result, shape } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { SwapsIOSwapRequest } from '..'

const NUM_RETRIES = 2
const RETRY_DELAY_MS = 2000

type Data = {
    data: Hexadecimal
    value: bigint
    toAddress: Web3.address.Address
    fromAddress: Web3.address.Address
}

const manualReceive = async ({
    request,
    signal,
}: {
    request: SwapsIOSwapRequest
    signal?: AbortSignal
}): Promise<Data> => {
    const response = await get(
        `/swaps/${request.hash}/manual_receive`,
        {},
        signal
    )

    const data = parseManualReceiveResponse(response).getSuccessResultOrThrow(
        `Failed to parse manual receive response`
    )

    return data
}

export const manualReceiveWithRetries = withRetries(
    manualReceive,
    NUM_RETRIES,
    RETRY_DELAY_MS
)

const parseManualReceiveResponse = (response: unknown): Result<unknown, Data> =>
    object(response).andThen((obj) =>
        shape({
            data: parseHexadecimal(obj.data),
            value: bigint(obj.value),
            toAddress: Web3.address.parse(obj.to_address),
            fromAddress: Web3.address.parse(obj.from_address),
        })
    )
