import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { GroupHeader, Section } from '@zeal/uikit/Group'
import { Text } from '@zeal/uikit/Text'

import { CryptoMoneyListItem } from '@zeal/domains/Money/components/CryptoMoneyListItem'
import { NetworkMap } from '@zeal/domains/Network'
import { OrderBuySignMessage as OrderBuySignMessageSimulation } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

type Props = {
    simulatedSignMessage: OrderBuySignMessageSimulation
    networkMap: NetworkMap
}

export const OrderBuyView = ({ networkMap, simulatedSignMessage }: Props) => (
    <Column spacing={16}>
        <Section>
            <GroupHeader
                left={({ color, textVariant, textWeight }) => (
                    <Text
                        color={color}
                        variant={textVariant}
                        weight={textWeight}
                    >
                        <FormattedMessage
                            id="send.titile"
                            defaultMessage="Send"
                        />
                    </Text>
                )}
                right={null}
            />

            <CryptoMoneyListItem
                networkMap={networkMap}
                size="large"
                balance={simulatedSignMessage.quote.from}
                priceInDefaultCurrency={
                    simulatedSignMessage.quote.fromInDefaultCurrency
                }
                sign="-"
            />
        </Section>

        <Section>
            <GroupHeader
                left={({ color, textVariant, textWeight }) => (
                    <Text
                        color={color}
                        variant={textVariant}
                        weight={textWeight}
                    >
                        <FormattedMessage id="to.titile" defaultMessage="To" />
                    </Text>
                )}
                right={null}
            />
            <CryptoMoneyListItem
                networkMap={networkMap}
                size="large"
                balance={simulatedSignMessage.quote.to}
                priceInDefaultCurrency={
                    simulatedSignMessage.quote.toInDefaultCurrency
                }
                sign="+"
            />
        </Section>
    </Column>
)
