import { FormattedMessage } from 'react-intl'

import { Content } from '@zeal/uikit/Content'

import { OrderBuySignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

type Props = {
    simulationMessage: OrderBuySignMessage
}

export const OrderBuyHeader = ({ simulationMessage: _ }: Props) => (
    <Content.Header
        title={
            <FormattedMessage
                id="simulatedTransaction.OrderBuySignMessage.title"
                defaultMessage="Buy"
            />
        }
        subtitle={
            <FormattedMessage
                id="rpc.OrderBuySignMessage.subtitle"
                defaultMessage="Using Swaps.IO"
            />
        }
    />
)
