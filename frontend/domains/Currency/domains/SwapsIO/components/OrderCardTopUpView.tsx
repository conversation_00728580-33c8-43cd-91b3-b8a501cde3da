import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Column } from '@zeal/uikit/Column'
import { GroupHeader, Section } from '@zeal/uikit/Group'
import { ZealCard } from '@zeal/uikit/Icon/ZealCard'
import { ListItem } from '@zeal/uikit/ListItem'
import { Text } from '@zeal/uikit/Text'

import { formatCardNumber } from '@zeal/domains/Card/helpers/formatCardNumber'
import { CryptoMoneyListItem } from '@zeal/domains/Money/components/CryptoMoneyListItem'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { NetworkMap } from '@zeal/domains/Network'
import { OrderCardTopupSignMessage as OrderCardTopupSignMessageSimulation } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

type Props = {
    simulatedSignMessage: OrderCardTopupSignMessageSimulation
    networkMap: NetworkMap
}

export const OrderCardTopUpView = ({
    simulatedSignMessage,
    networkMap,
}: Props) => {
    return (
        <Column spacing={16}>
            <Section>
                <GroupHeader
                    left={({ color, textVariant, textWeight }) => (
                        <Text
                            color={color}
                            variant={textVariant}
                            weight={textWeight}
                        >
                            <FormattedMessage
                                id="send.titile"
                                defaultMessage="Send"
                            />
                        </Text>
                    )}
                    right={null}
                />

                <CryptoMoneyListItem
                    networkMap={networkMap}
                    size="large"
                    balance={simulatedSignMessage.from}
                    priceInDefaultCurrency={
                        simulatedSignMessage.fromInDefaultCurrency
                    }
                    sign="-"
                />
            </Section>

            <Section>
                <GroupHeader
                    left={({ color, textVariant, textWeight }) => (
                        <Text
                            color={color}
                            variant={textVariant}
                            weight={textWeight}
                        >
                            <FormattedMessage
                                id="to.titile"
                                defaultMessage="To"
                            />
                        </Text>
                    )}
                    right={null}
                />
                <ListItem
                    size="large"
                    aria-current={false}
                    avatar={({ size }) => (
                        <Avatar variant="rounded" size={size}>
                            <ZealCard size={size} />
                        </Avatar>
                    )}
                    primaryText={
                        <FormattedMessage
                            id="card.list_item.title"
                            defaultMessage="Card"
                        />
                    }
                    shortText={
                        simulatedSignMessage.card?.lastFourDigits
                            ? formatCardNumber(
                                  simulatedSignMessage.card.lastFourDigits
                              )
                            : null
                    }
                    side={{
                        title: (
                            <FormattedMoneyPrecise
                                withSymbol={false}
                                sign="+"
                                money={simulatedSignMessage.to}
                            />
                        ),
                        subtitle: simulatedSignMessage.toInDefaultCurrency && (
                            <FormattedMoneyPrecise
                                withSymbol
                                sign="+"
                                money={simulatedSignMessage.toInDefaultCurrency}
                            />
                        ),
                    }}
                />
            </Section>
        </Column>
    )
}
