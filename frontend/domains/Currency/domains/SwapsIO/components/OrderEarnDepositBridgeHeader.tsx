import { FormattedMessage } from 'react-intl'

import { Content } from '@zeal/uikit/Content'

import { OrderEarnDepositBridge } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

type Props = {
    simulationMessage: OrderEarnDepositBridge
}

export const OrderEarnDepositBridgeHeader = ({
    simulationMessage: _,
}: Props) => (
    <Content.Header
        title={
            <FormattedMessage
                id="simulatedTransaction.OrderEarnDepositBridge.title"
                defaultMessage="Deposit into Earn"
            />
        }
    />
)
