import { FormattedMessage } from 'react-intl'

import { Content } from '@zeal/uikit/Content'

import { OrderCardTopupSignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

type Props = {
    simulationMessage: OrderCardTopupSignMessage
}

export const OrderCardTopUpHeader = ({ simulationMessage: _ }: Props) => (
    <Content.Header
        title={
            <FormattedMessage
                id="simulatedTransaction.OrderCardTopupSignMessage.title"
                defaultMessage="Add to card"
            />
        }
        subtitle={
            <FormattedMessage
                id="rpc.OrderCardTopupSignMessage.subtitle"
                defaultMessage="Using Swaps.IO"
            />
        }
    />
)
