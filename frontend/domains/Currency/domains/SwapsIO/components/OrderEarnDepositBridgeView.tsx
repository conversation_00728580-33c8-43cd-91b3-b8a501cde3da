import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { GroupHeader, Section } from '@zeal/uikit/Group'
import { ListItem } from '@zeal/uikit/ListItem'
import { Text } from '@zeal/uikit/Text'

import { Avatar } from '@zeal/domains/Currency/components/Avatar'
import { TakerAPYLabel } from '@zeal/domains/Earn/components/TakerAPYLabel'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerTitle } from '@zeal/domains/Earn/components/TakerTitle'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { OrderEarnDepositBridge as OrderEarnDepositBridgeMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

type Props = {
    simulatedSignMessage: OrderEarnDepositBridgeMessage
}

export const OrderEarnDepositBridgeView = ({ simulatedSignMessage }: Props) => {
    const { taker, takerApyMap, takerPortfolioMap, swapsIOQuote } =
        simulatedSignMessage

    const toAmountInUserCurrency = applyRate2({
        baseAmount: swapsIOQuote.to,
        rate: takerPortfolioMap[taker.type].userCurrencyRate,
    })

    return (
        <Column spacing={16}>
            <Section>
                <GroupHeader
                    left={({ color, textVariant, textWeight }) => (
                        <Text
                            color={color}
                            variant={textVariant}
                            weight={textWeight}
                        >
                            <FormattedMessage
                                id="order-earn-deposit-bridge.deposit"
                                defaultMessage="Deposit"
                            />
                        </Text>
                    )}
                    right={null}
                />
                <ListItem
                    aria-current={false}
                    size="regular"
                    primaryText={swapsIOQuote.from.currency.code}
                    avatar={({ size }) => (
                        <Avatar
                            currency={swapsIOQuote.from.currency}
                            size={size}
                        />
                    )}
                    side={{
                        title: (
                            <>
                                <FormattedMoneyPrecise
                                    withSymbol={false}
                                    sign="-"
                                    money={swapsIOQuote.from}
                                />
                            </>
                        ),
                    }}
                />
            </Section>
            <Section>
                <GroupHeader
                    left={({ color, textVariant, textWeight }) => (
                        <Text
                            color={color}
                            variant={textVariant}
                            weight={textWeight}
                        >
                            <FormattedMessage
                                id="order-earn-deposit-bridge.into"
                                defaultMessage="Into"
                            />
                        </Text>
                    )}
                    right={null}
                />
                {taker && (
                    <ListItem
                        avatar={({ size }) => (
                            <TakerAvatar takerType={taker.type} size={size} />
                        )}
                        primaryText={<TakerTitle takerType={taker.type} />}
                        shortText={
                            <TakerAPYLabel
                                taker={taker}
                                takerApyMap={takerApyMap}
                            />
                        }
                        side={{
                            title: (
                                <FormattedMoneyPrecise
                                    withSymbol={false}
                                    sign="+"
                                    money={toAmountInUserCurrency}
                                />
                            ),
                            subtitle: swapsIOQuote.toInDefaultCurrency && (
                                <FormattedMoneyPrecise
                                    withSymbol
                                    sign="+"
                                    money={swapsIOQuote.toInDefaultCurrency}
                                />
                            ),
                        }}
                        size="large"
                        aria-current={false}
                    />
                )}
            </Section>
        </Column>
    )
}
