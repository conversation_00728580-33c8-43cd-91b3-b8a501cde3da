import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoCurrency } from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'

import { SWAPS_IO_NATIVE_CURRENCY_ADDRESS } from '../constants'

export const getSwapsIOTokenAddress = ({
    cryptoCurrency,
    networkMap,
}: {
    cryptoCurrency: CryptoCurrency
    networkMap: NetworkMap
}): Web3.address.Address => {
    const network = findNetworkByHexChainId(
        cryptoCurrency.networkHexChainId,
        networkMap
    )

    switch (network.type) {
        case 'predefined':
            if (cryptoCurrency.id === network.nativeCurrency.id) {
                return SWAPS_IO_NATIVE_CURRENCY_ADDRESS
            }
            return cryptoCurrency.address as Web3.address.Address

        case 'custom':
        case 'testnet':
            throw new ImperativeError(`Unsupported network type in swapsIO`, {
                network: network.name,
            })
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
