import { fetchStaticData } from '@zeal/api/fetchStaticData'

import * as Func from '@zeal/toolkit/Function'
import * as Json from '@zeal/toolkit/JSON'
import {
    array,
    boolean,
    combine,
    object,
    oneOf,
    recordOf,
    recordStrict,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Storage from '@zeal/toolkit/Storage'

import {
    CryptoCurrency,
    CurrencyId,
    KnownCurrencies,
    ShortKnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import { parseShortKnownCryptoCurrencies } from '@zeal/domains/Currency/helpers/parse'
import { NetworkHexId } from '@zeal/domains/Network'
import { parse as parseNetwork } from '@zeal/domains/Network/helpers/parse'

import {
    CURRENCY_STORAGE_CACHE_KEY,
    INITIAL_DEFAULT_CURRENCY,
} from '../constants'
import { getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies } from '../helpers/getShortKnownCryptoCurrenciesfromKnownCryptoCurrencies'

export type CurrenciesMatrixItem = {
    from: CurrencyId[]
    to: CurrencyId[]
    canRefuel: boolean
}

export type CurrenciesMatrix = {
    knownCurrencies: KnownCurrencies
    currencies: Record<NetworkHexId, ToMatrix | null>
}

export type ToMatrix = Record<NetworkHexId, CurrenciesMatrixItem | null>

const parseCurrenciesMatrixItem = (
    input: unknown
): Result<unknown, CurrenciesMatrixItem> =>
    object(input).andThen((obj) =>
        shape({
            from: array(obj.from).andThen((fromArr) =>
                combine(fromArr.map(string))
            ),
            to: array(obj.to).andThen((toArr) => combine(toArr.map(string))),
            canRefuel: boolean(obj.canRefuel),
        })
    )

const parseStaticMatrix = (
    input: unknown
): Result<unknown, Record<NetworkHexId, ToMatrix>> =>
    object(input).andThen((obj) =>
        object(obj.supportedCurrencies).andThen((currObj) =>
            recordStrict(currObj, {
                keyParser: parseNetwork,
                valueParser: (value: unknown) =>
                    recordOf(value, {
                        keyParser: parseNetwork,
                        valueParser: parseCurrenciesMatrixItem,
                    }),
            })
        )
    )

export const fetchCurrenciesMatrix = async (): Promise<CurrenciesMatrix> => {
    const [currencies, knownCurrencies] = await Promise.all([
        fetchStaticMatrix('@zeal/assets/data/matrix2.json'),
        fetchShortStaticCurrencies(),
    ])

    return {
        currencies,
        knownCurrencies: {
            ...getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies(
                knownCurrencies
            ),
            USD: INITIAL_DEFAULT_CURRENCY,
        },
    }
}

const parseCurrencyCache = (
    input: string | null
): Result<unknown, ShortKnownCryptoCurrencies> => {
    return oneOf(null, [
        string(input)
            .andThen(Json.parse)
            .andThen(parseShortKnownCryptoCurrencies),
        success({}),
    ])
}

const fetchStaticCurrenciesFromStorage =
    async (): Promise<ShortKnownCryptoCurrencies> => {
        // TODO @resetko-zeal we needto figureout a way for toolkit to work on node
        const isNode = process?.versions?.node // TODO @resetko-zeal should we have a helper for this?
        if (isNode) {
            return {} as ShortKnownCryptoCurrencies
        }

        const data = await Storage.local.getChunked(CURRENCY_STORAGE_CACHE_KEY)
        return parseCurrencyCache(data).getSuccessResultOrThrow(
            'cannot parse cached currencies'
        )
    }

const currencyCache = Func.queue({ fetcher: fetchStaticCurrenciesFromStorage })

export const writeCurrenciesCache = async (
    knownCurrencies: ShortKnownCryptoCurrencies
): Promise<ShortKnownCryptoCurrencies> => {
    // TODO @resetko-zeal we needto figureout a way for toolkit to work on node
    const isNode = process?.versions?.node // TODO @resetko-zeal should we have a helper for this?
    if (isNode) {
        return knownCurrencies
    }

    return currencyCache.write(async (cached) => {
        const merged = {
            ...cached,
            ...knownCurrencies,
        }

        await Storage.local.setChunked(
            CURRENCY_STORAGE_CACHE_KEY,
            JSON.stringify(merged)
        )
        return merged
    })
}

export const fetchStaticCurrencies = async (): Promise<
    Record<CurrencyId, CryptoCurrency>
> => {
    const short = await fetchShortStaticCurrencies()
    return getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies(short)
}

export const fetchShortStaticCurrencies = async () => {
    const [fromFile, fromStorage] = await Promise.all([
        fetchStaticData(
            '@zeal/assets/data/currencies2.json',
            parseShortKnownCryptoCurrencies
        ),
        (() => {
            // TODO @resetko-zeal we needto figureout a way for toolkit to work on node
            const isNode = process?.versions?.node // TODO @resetko-zeal should we have a helper for this?
            return isNode
                ? ({} as ShortKnownCryptoCurrencies)
                : currencyCache.fetch()
        })(),
    ])
    return {
        ...fromFile,
        ...fromStorage,
    }
}
const fetchStaticMatrix = async (
    path: '@zeal/assets/data/matrix2.json'
): Promise<Record<NetworkHexId, ToMatrix>> =>
    fetchStaticData(path, parseStaticMatrix)
