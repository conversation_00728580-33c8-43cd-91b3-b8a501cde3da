import { notReachable } from '@zeal/toolkit'
import { fromBigInt } from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoCurrency, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { mulByNumber } from '@zeal/domains/Money/helpers/mul'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { sumMoney } from '@zeal/domains/Money/helpers/sum'
import {
    Network,
    NetworkHexId,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { fetchGasEstimate } from '@zeal/domains/Transactions/api/fetchGasEstimate'
import { fetchFeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchFeeForecast'
import { getSuggestedGasLimit } from '@zeal/domains/Transactions/helpers/getSuggestedGasLimit'
import { OperationType } from '@zeal/domains/UserOperation'
import { fetchGasAbstractionTransactionFeesForMetaTransactions } from '@zeal/domains/UserOperation/api/fetchGasAbstractionTransactionFeesForMetaTransactions'
import { calculateSelectedGasCurrency } from '@zeal/domains/UserOperation/helpers/calculateSelectedGasCurrency'
import { ethSendTransactionToMetaTransactionData } from '@zeal/domains/UserOperation/helpers/ethSendTransactionToMetaTransactionData'

type Params = {
    fromCurrency: CryptoCurrency
    serverPortfolio: ServerPortfolio2
    requests: EthSendTransaction[]

    fromAddress: Web3.address.Address
    network: Network
    networksToSponsor: NetworkHexId[]
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    keyStore: KeyStore
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}

const FEE_BUFFER = 1.5 // +50%

const calculateFeeAdjustedBalance = (
    feesResponse: Awaited<
        ReturnType<typeof fetchGasAbstractionTransactionFeesForMetaTransactions>
    >,
    balance: CryptoMoney,
    fromCurrency: CryptoCurrency,
    gasCurrencyPresetMap: GasCurrencyPresetMap,
    network: Network,
    serverPortfolio: ServerPortfolio2
): CryptoMoney => {
    switch (feesResponse.type) {
        case 'non_sponsored_transaction':
            const savedGasCurrencyId =
                gasCurrencyPresetMap[network.hexChainId] || null

            const predictedGasCurrency = calculateSelectedGasCurrency({
                fees: feesResponse.fees,
                outgoingAmounts: [
                    {
                        amount: balance.amount,
                        currencyId: balance.currency.id,
                    },
                ],
                serverPortfolio,
                savedGasCurrencyId,
            })

            if (predictedGasCurrency.id === fromCurrency.id) {
                const feeAmount = feesResponse.fees.find(
                    (fee) =>
                        fee.feeInTokenCurrency.currency.id ===
                        predictedGasCurrency.id
                )?.feeInTokenCurrency || {
                    amount: 0n,
                    currency: fromCurrency,
                }

                const bufferedFeeAmount = mulByNumber(feeAmount, FEE_BUFFER)

                return balance.amount > bufferedFeeAmount.amount
                    ? sub2(balance, bufferedFeeAmount)
                    : balance
            } else {
                return balance
            }

        case 'sponsored_transaction':
            return balance
        default:
            return notReachable(feesResponse)
    }
}

export const fetchFeeAdjustedBalance = async ({
    fromAddress,
    keyStore,
    network,
    networkRPCMap,
    networksToSponsor,
    serverPortfolio,
    gasCurrencyPresetMap,
    fromCurrency,
    defaultCurrencyConfig,
    networkMap,
    requests,
    signal,
}: Params): Promise<CryptoMoney> => {
    const balance = getBalanceByCryptoCurrency2({
        currency: fromCurrency,
        serverPortfolio,
    })

    if (!requests.length) {
        return balance
    }

    switch (keyStore.type) {
        case 'safe_4337': {
            switch (network.smartWalletSupport.type) {
                case 'not_supported':
                    return balance
                case 'supported':
                    const isSponsored =
                        network.smartWalletSupport.isSponsorshipSupported &&
                        networksToSponsor.includes(network.hexChainId)

                    if (isSponsored) {
                        return balance
                    }

                    const metaTransactionDatas = requests.map(
                        ethSendTransactionToMetaTransactionData
                    )

                    const feesResponse =
                        await fetchGasAbstractionTransactionFeesForMetaTransactions(
                            {
                                signal,
                                metaTransactionDatas,
                                callGasLimitBuffer: 0n,
                                keyStore,
                                network,
                                networkRPCMap,
                                serverPortfolio,
                                defaultCurrencyConfig,
                                networkMap,
                                sponsored: isSponsored,
                            }
                        )

                    return calculateFeeAdjustedBalance(
                        feesResponse,
                        balance,
                        fromCurrency,
                        gasCurrencyPresetMap,
                        network,
                        serverPortfolio
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(network.smartWalletSupport)
            }
        }

        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
        case 'track_only': {
            if (fromCurrency.id !== network.nativeCurrency.id) {
                return balance
            }

            const fees = await Promise.all(
                requests.map((request) =>
                    fetchGasEstimate({
                        network,
                        networkRPCMap,
                        rpcRequest: request,
                    }).then((gasEstimate) =>
                        fetchFeeForecast({
                            gasEstimate,
                            defaultCurrencyConfig,
                            networkMap,
                            network,
                            networkRPCMap,
                            gasLimit: getSuggestedGasLimit(gasEstimate),
                            address: fromAddress,
                            selectedPreset: { type: 'Fast' },
                            sendTransactionRequest: request,
                        })
                    )
                )
            )

            const totalFees = fees.reduce(
                (total, fee) =>
                    sumMoney(total, fee.fast.maxPriceInNativeCurrency),
                {
                    amount: 0n,
                    currency: fromCurrency,
                }
            )

            return balance.amount > totalFees.amount
                ? sub2(balance, totalFees)
                : balance
        }

        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}

export const fetchFeeAdjustedBalanceForGas = async ({
    fromAddress,
    keyStore,
    network,
    networkRPCMap,
    networksToSponsor,
    serverPortfolio,
    gasCurrencyPresetMap,
    fromCurrency,
    defaultCurrencyConfig,
    networkMap,
    gasEstimate,
    signal,
}: {
    gasEstimate: bigint
    fromCurrency: CryptoCurrency
    serverPortfolio: ServerPortfolio2
    fromAddress: Web3.address.Address
    network: Network
    networksToSponsor: NetworkHexId[]
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    keyStore: KeyStore
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<CryptoMoney> => {
    const balance = getBalanceByCryptoCurrency2({
        currency: fromCurrency,
        serverPortfolio,
    })

    switch (keyStore.type) {
        case 'safe_4337': {
            switch (network.smartWalletSupport.type) {
                case 'not_supported':
                    return balance
                case 'supported':
                    const isSponsored =
                        network.smartWalletSupport.isSponsorshipSupported &&
                        networksToSponsor.includes(network.hexChainId)

                    if (isSponsored) {
                        return balance
                    }

                    const feesResponse =
                        await fetchGasAbstractionTransactionFeesForMetaTransactions(
                            {
                                signal,
                                metaTransactionDatas: [
                                    {
                                        to: fromAddress,
                                        data: '0x0',
                                        value: '0x0',
                                        operation: OperationType.Call,
                                    },
                                ],
                                callGasLimitBuffer: gasEstimate,
                                keyStore,
                                network,
                                networkRPCMap,
                                serverPortfolio,
                                defaultCurrencyConfig,
                                networkMap,
                                sponsored: isSponsored,
                            }
                        )

                    return calculateFeeAdjustedBalance(
                        feesResponse,
                        balance,
                        fromCurrency,
                        gasCurrencyPresetMap,
                        network,
                        serverPortfolio
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(network.smartWalletSupport)
            }
        }

        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
        case 'track_only': {
            if (fromCurrency.id !== network.nativeCurrency.id) {
                return balance
            }

            const fee = await fetchFeeForecast({
                gasEstimate: fromBigInt(gasEstimate),
                defaultCurrencyConfig,
                networkMap,
                network,
                networkRPCMap,
                gasLimit: getSuggestedGasLimit(fromBigInt(gasEstimate)),
                address: fromAddress,
                selectedPreset: { type: 'Fast' },
                sendTransactionRequest: {
                    id: generateRandomNumber(),
                    jsonrpc: '2.0' as const,
                    method: 'eth_sendTransaction' as const,
                    params: [
                        {
                            from: fromAddress,
                            data: '0x0',
                            to: fromAddress,
                            value: '0x0',
                        },
                    ],
                },
            })

            return balance.amount > fee.fast.maxPriceInNativeCurrency.amount
                ? sub2(balance, fee.fast.maxPriceInNativeCurrency)
                : balance
        }

        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
