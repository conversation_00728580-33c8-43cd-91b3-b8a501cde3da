import { keys } from '@zeal/toolkit/Object'

import {
    CurrencyId,
    KnownCryptoCurrencies,
    ShortKnownCryptoCurrencies,
} from '@zeal/domains/Currency'
import {
    getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies,
    getShortKnownCryptoCurrenciesFromKnownCurrencies,
} from '@zeal/domains/Currency/helpers/getShortKnownCryptoCurrenciesfromKnownCryptoCurrencies'
import { NetworkRPCMap } from '@zeal/domains/Network'

import { fetchCryptoCurrencyFromRPC } from './fetchCryptoCurrencyFromRPC'
import {
    fetchShortStaticCurrencies,
    writeCurrenciesCache,
} from './fetchCurrenciesMatrix'

type Params = {
    currencies: CurrencyId[]
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}
export const fetchCryptoCurrency2 = async ({
    currencies,
    networkRPCMap,
    signal,
}: Params): Promise<KnownCryptoCurrencies> => {
    const currenciesForFetch: CurrencyId[] = []
    const knownCurrencies: KnownCryptoCurrencies = {}
    const staticShortCurrencies: ShortKnownCryptoCurrencies =
        await fetchShortStaticCurrencies()
    const staticCurrencies =
        getKnownCryptoCurrenciesFromShortKnownCryptoCurrencies(
            staticShortCurrencies
        )

    currencies.forEach((element: CurrencyId) => {
        if (staticCurrencies[element]) {
            knownCurrencies[element] = staticCurrencies[element]
        } else {
            currenciesForFetch.push(element)
        }
    })

    const currenciesFromRPC: KnownCryptoCurrencies =
        await fetchCryptoCurrencyFromRPC({
            currencies: currenciesForFetch,
            networkRPCMap,
            signal,
        })
    if (keys(currenciesFromRPC).length > 0) {
        await writeCurrenciesCache(
            getShortKnownCryptoCurrenciesFromKnownCurrencies(currenciesFromRPC)
        )
    }

    return { ...knownCurrencies, ...currenciesFromRPC }
}
