import * as aws from '@pulumi/aws'
import * as pulumi from '@pulumi/pulumi'
import { createRestAPIGateway } from './api-gateway'
import { indexerInstance } from './ec2/indexer-instance'
import { jumperInstance } from './ec2/jumper-instance'
import { nodeInstance } from './ec2/node-instance'
import { proxyInstance } from './ec2/proxy-instance'
import { magentaHeadBot } from './magenta-head-bot'
import { createDatabricksOnAws } from './databricks'

const AVAILABILITY_ZONE = 'eu-west-1b'

const VPC_NAME = 'zeal-be-vpc'
const PUBLIC_SUBNET_NAME = 'zeal-be-subnet-public'

const vpc = new aws.ec2.Vpc(VPC_NAME, {
    cidrBlock: '10.0.0.0/16',
    enableDnsSupport: true,
    enableDnsHostnames: true,
    tags: {
        Name: VPC_NAME,
    },
})

const publicSubnet = new aws.ec2.Subnet(PUBLIC_SUBNET_NAME, {
    vpcId: vpc.id,
    cidrBlock: '********/24',
    mapPublicIpOnLaunch: true,
    tags: {
        Name: PUBLIC_SUBNET_NAME,
    },
})

const internetGateway = new aws.ec2.InternetGateway('zeal-be-igw', {
    vpcId: vpc.id,
    tags: { Name: 'zeal-be-igw' },
})

const publicRouteTable = new aws.ec2.RouteTable('zeal-be-public-rt', {
    vpcId: vpc.id,
    routes: [{ cidrBlock: '0.0.0.0/0', gatewayId: internetGateway.id }],
    tags: { Name: 'zeal-be-public-rt' },
})

new aws.ec2.RouteTableAssociation('zeal-be-public-rt-assoc', {
    subnetId: publicSubnet.id,
    routeTableId: publicRouteTable.id,
})

// TODO @resetko-zeal Private subnet does not work figureout how to live with it :(

const jumper = jumperInstance({
    name: 'jumper',
    az: AVAILABILITY_ZONE,
    subnet: publicSubnet,
    allowSSM: false,
    vpc,
})

const nodePm2 = nodeInstance({
    az: AVAILABILITY_ZONE,
    jumperSecurityGroup: jumper.securityGroup,
    name: 'node-pm2',
    subnet: publicSubnet,
    vpc,
    allowSSM: false,
    nodeVersion: '20.8.0',
})

const proxy = proxyInstance({
    allowSSM: false,
    az: AVAILABILITY_ZONE,
    jumperSecurityGroup: jumper.securityGroup,
    name: 'proxy',
    subnet: publicSubnet,
    vpc,
})

const indexer = indexerInstance({
    allowSSM: true,
    az: AVAILABILITY_ZONE,
    jumperSecurityGroup: jumper.securityGroup,
    name: 'indexer-2',
    subnet: publicSubnet,
    vpc,
    volumeUuid: 'c09ba2d9-5aa2-43e8-8713-aa457486f21e',
})

/**
 * DNS
 */

const privateDNSZone = new aws.route53.Zone('zeal-be-dns-zone', {
    name: 'zeal-be.internal',
    vpcs: [{ vpcId: vpc.id }],
})

new aws.route53.Record('zeal-be-dns-proxy-record', {
    zoneId: privateDNSZone.zoneId,
    name: 'proxy.zeal-be.internal',
    type: 'A',
    ttl: 300,
    records: [proxy.instance.privateIp],
})

new aws.route53.Record('zeal-be-dns-indexer-record', {
    zoneId: privateDNSZone.zoneId,
    name: 'indexer.zeal-be.internal',
    type: 'A',
    ttl: 300,
    records: [indexer.instance.privateIp],
})

new aws.route53.Record('zeal-be-dns-node-pm2-record', {
    zoneId: privateDNSZone.zoneId,
    name: 'node-pm2.zeal-be.internal',
    type: 'A',
    ttl: 300,
    records: [nodePm2.instance.privateIp],
})

/**
 * EBS
 */

const dataVolume = new aws.ebs.Volume(`zeal-be-data-volume`, {
    availabilityZone: AVAILABILITY_ZONE,
    size: 50, // GB
    type: 'gp2',
    tags: {
        Name: `zeal-be-data-volume`,
    },
})

new aws.ec2.VolumeAttachment(`zeal-be-data-volume-attachment`, {
    deviceName: '/dev/sdf', // The device name exposed to the instance (e.g., /dev/sdh or xvdh)
    instanceId: nodePm2.instance.id,
    volumeId: dataVolume.id,
})

const indexerDataVolume = new aws.ebs.Volume(`zeal-be-indexer-data-volume-2`, {
    availabilityZone: AVAILABILITY_ZONE,
    size: 2048, // GB
    type: 'gp3',
})

// TODO @resetko-zeal move this into createIndexerInstance during next manual parallel deployment
new aws.ec2.VolumeAttachment(
    `zeal-be-indexer-data-volume-attachment-2`,
    {
        deviceName: '/dev/sdf', // The device name exposed to the instance (e.g., /dev/sdh or xvdh)
        instanceId: indexer.instance.id,
        volumeId: indexerDataVolume.id,
    },
    { deleteBeforeReplace: true, dependsOn: [indexer.instance] }
)

/**
 * GATEWAY
 */

const gatewayProxyApi = new aws.apigatewayv2.Api(`zeal-be-gw-proxy-http-api`, {
    name: `zeal-be-gw-proxy-http-api`,
    protocolType: 'HTTP',
})

const integrationProxy = new aws.apigatewayv2.Integration(
    `zeal-be-gw-http-api-integration-proxy`,
    {
        apiId: gatewayProxyApi.id,
        integrationType: 'HTTP_PROXY',
        integrationUri: pulumi.interpolate`http://${proxy.instance.publicDns}:${proxy.port}/{proxy}`,
        integrationMethod: 'ANY',
    }
)

const integrationNode = new aws.apigatewayv2.Integration(
    `zeal-be-gw-http-api-integration-node`,
    {
        apiId: gatewayProxyApi.id,
        integrationType: 'HTTP_PROXY',
        integrationUri: pulumi.interpolate`http://${nodePm2.instance.publicDns}:${nodePm2.port}/{proxy}`,
        integrationMethod: 'ANY',
    }
)

const tmp_integrationNode = new aws.apigatewayv2.Integration(
    `zeal-be-gw-http-api-tmp-integration-node`,
    {
        apiId: gatewayProxyApi.id,
        integrationType: 'HTTP_PROXY',
        integrationUri: pulumi.interpolate`http://${nodePm2.instance.publicDns}:${nodePm2.port}/notifications/subscribe`,
        integrationMethod: 'ANY',
    }
)

const integrationIndexer = new aws.apigatewayv2.Integration(
    `zeal-be-gw-http-api-integration-indexer`,
    {
        apiId: gatewayProxyApi.id,
        integrationType: 'HTTP_PROXY',
        integrationUri: pulumi.interpolate`http://${indexer.instance.publicDns}:${indexer.port}/{proxy}`,
        integrationMethod: 'ANY',
    }
)

const routeProxy = new aws.apigatewayv2.Route(
    `zeal-be-gw-http-api-route-proxy`,
    {
        apiId: gatewayProxyApi.id,
        routeKey: 'ANY /proxy/{proxy+}',
        target: pulumi.interpolate`integrations/${integrationProxy.id}`,
    }
)

const routeNode = new aws.apigatewayv2.Route(`zeal-be-gw-http-api-route-node`, {
    apiId: gatewayProxyApi.id,
    routeKey: 'ANY /api/{proxy+}',
    target: pulumi.interpolate`integrations/${integrationNode.id}`,
})

const tmp_routeNode = new aws.apigatewayv2.Route(
    `zeal-be-gw-http-api-tmp-route-node`,
    {
        apiId: gatewayProxyApi.id,
        routeKey: 'ANY /notifications/subscribe',
        target: pulumi.interpolate`integrations/${tmp_integrationNode.id}`,
    }
)

const routeIndexer = new aws.apigatewayv2.Route(
    `zeal-be-gw-http-api-route-indexer`,
    {
        apiId: gatewayProxyApi.id,
        routeKey: 'ANY /indexer/{proxy+}',
        target: pulumi.interpolate`integrations/${integrationIndexer.id}`,
    }
)

new aws.apigatewayv2.Deployment(
    `zeal-be-gw-http-api-deployment`,
    { apiId: gatewayProxyApi.id },
    {
        dependsOn: [
            routeProxy,
            routeNode,
            tmp_routeNode,
            routeIndexer,
            integrationProxy,
            integrationNode,
            integrationIndexer,
        ],
    }
)

const stage = new aws.apigatewayv2.Stage(`zeal-be-gw-http-api-stage`, {
    apiId: gatewayProxyApi.id,
    name: 'prod',
    autoDeploy: true,
})

const restApiGW = createRestAPIGateway({
    indexer: indexer,
    nodePm2,
    proxy,
})

/**
 * Discord bot
 */

const bot = magentaHeadBot()

/**
 * Databricks
 */

const databricks = createDatabricksOnAws()

export const databricksWorkspaceUrl: unknown = databricks.workspaceUrl
export const databricksEventBucketName: unknown = databricks.eventBucketName
export const databricksRoleArn: unknown = databricks.roleArn
export const databricksInstanceProfileArn: unknown =
    databricks.instanceProfileArn
export const databricksInstanceRoleArn: unknown = databricks.instanceRoleArn

export const botLambdaUrl: unknown = bot.url

export const restApiGWUrl: unknown = restApiGW.apiGatewayUrl

export const apiGatewayUrl: unknown = stage.invokeUrl
export const jumperPublicIp: unknown = jumper.eip.publicIp

export const nodePm2PrivateIp: unknown = nodePm2.instance.privateIp
export const nodePm2URN: unknown = nodePm2.instance.urn
export const nodePm2PublicDns: unknown = nodePm2.instance.publicDns

export const proxyPublicDns: unknown = proxy.instance.publicDns
export const proxyPrivateIp: unknown = proxy.instance.privateIp

export const indexerPublicDns: unknown = indexer.instance.publicDns
export const indexerPrivateIp: unknown = indexer.instance.privateIp
