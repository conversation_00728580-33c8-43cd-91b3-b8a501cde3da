import React, { useEffect, useRef, useState } from 'react'
import { Pressable, StyleSheet, View } from 'react-native'
import { Gesture, GestureDetector } from 'react-native-gesture-handler'
import { runOnJS } from 'react-native-reanimated'

import { LinearGradient } from 'expo-linear-gradient'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { colors } from '@zeal/uikit/colors'
import { Column } from '@zeal/uikit/Column'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'

import { ProgressBar } from './ProgressBar'

import { Artwork, ArtworkPlayer } from '../ArtworkPlayer'

type Props = {
    stories: StoryPage[]
    slide: number
    paused: boolean
    actions: {
        primary: {
            title: React.ReactNode
            onClick: () => void
        }
        secondary: {
            title: React.ReactNode
            onClick: () => void
        }
    }

    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'on_stories_completed' }
    | { type: 'on_next_slide_shown'; currentSlide: number }
    | { type: 'on_error'; error: Error }

export type StoryPage = {
    artwork: Artwork
    title: React.ReactNode
    subtitle?: React.ReactNode
}

const SHOW_SLIDE_FOR_MS = 6000

const styles = StyleSheet.create({
    contentWrapper: {
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 0,
    },
    container: {
        height: 260,
        padding: 16,
        backgroundColor: colors.surfaceDefault,
    },
    artwork: { flexGrow: 1, backgroundColor: colors.backgroundLight },
    storyNextPreviousButton: {
        position: 'absolute',
        height: '100%',
        width: '50%',
        zIndex: 1,
    },
    storyNextSlideButton: {
        right: 0,
    },
    storyPreviousSlideButton: {
        left: 0,
    },
    actionOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        top: 0,
    },
    shadow: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 52,
    },
})

type State = number

export const StoryWithPersistentActions = ({
    actions,
    stories,
    slide,
    paused = false,
    onMsg,
}: Props) => {
    const [currentSlide, setCurrentSlide] = useState<State>(slide)
    const shouldAutoProgress = useRef(slide === 0)

    useEffect(() => {
        setCurrentSlide(slide)
        shouldAutoProgress.current = slide === 0
    }, [slide])

    const page = stories[currentSlide]
    const isLastSlide = currentSlide === stories.length - 1
    const isFirstSlide = currentSlide === 0

    const storyLength = stories.length

    useEffect(() => {
        if (!isLastSlide && shouldAutoProgress.current && !paused) {
            const timeoutId = setTimeout(() => {
                setCurrentSlide(currentSlide + 1)
            }, SHOW_SLIDE_FOR_MS)
            return () => clearTimeout(timeoutId)
        }
    }, [currentSlide, isLastSlide, paused])

    const liveRefOnMsg = React.useRef(onMsg)
    useEffect(() => {
        if (isLastSlide) {
            liveRefOnMsg.current({ type: 'on_stories_completed' })
        }

        liveRefOnMsg.current({ type: 'on_next_slide_shown', currentSlide })
    }, [liveRefOnMsg, currentSlide, isLastSlide])

    useEffect(() => {
        if (!page) {
            liveRefOnMsg.current({
                type: 'on_error',
                error: new ImperativeError('no page for story', {
                    currentSlide,
                    storyLength,
                }),
            })
            setCurrentSlide(0) // try to recover
        }
    }, [currentSlide, page, storyLength])

    const handleSwipeRight = () => {
        if (currentSlide > 0) {
            setCurrentSlide(currentSlide - 1)
        }
    }

    const handleSwipeLeft = () => {
        if (currentSlide < stories.length - 1) {
            setCurrentSlide(currentSlide + 1)
        }
    }

    const handlePanGesture = Gesture.Pan().onEnd((event) => {
        if (event.translationX > 50) {
            runOnJS(handleSwipeRight)()
        } else if (event.translationX < -50) {
            runOnJS(handleSwipeLeft)()
        }
    })

    if (!page) {
        return <LoadingLayout actionBar={null} title={null} onClose={noop} />
    }

    return (
        <Screen background="default" padding="story" onNavigateBack={null}>
            <GestureDetector gesture={handlePanGesture}>
                <View style={styles.contentWrapper}>
                    <Column spacing={0} fill shrink>
                        <ArtworkPlayer art={page.artwork} />
                        <LinearGradient
                            style={styles.shadow}
                            colors={[
                                'rgba(11, 24, 33, 0)',
                                'rgba(11, 24, 33, 0.03)',
                            ]}
                        />
                        {!isFirstSlide && (
                            <Pressable
                                style={[
                                    styles.storyNextPreviousButton,
                                    styles.storyPreviousSlideButton,
                                ]}
                                onPress={() => {
                                    setCurrentSlide((state) => state - 1)
                                }}
                            >
                                {({ hovered }) => {
                                    return (
                                        hovered && (
                                            <LinearGradient
                                                start={{
                                                    x: 0,
                                                    y: 0.5,
                                                }}
                                                end={{
                                                    x: 1,
                                                    y: 0.5,
                                                }}
                                                style={[styles.actionOverlay]}
                                                colors={[
                                                    'rgba(11, 24, 33, 0.15)',
                                                    'rgba(11, 24, 33, 0)',
                                                ]}
                                            />
                                        )
                                    )
                                }}
                            </Pressable>
                        )}
                        {!isLastSlide && (
                            <Pressable
                                style={[
                                    styles.storyNextPreviousButton,
                                    styles.storyNextSlideButton,
                                ]}
                                onPress={() => {
                                    setCurrentSlide((state) => state + 1)
                                }}
                            >
                                {({ hovered }) => {
                                    return (
                                        hovered && (
                                            <LinearGradient
                                                start={{
                                                    x: 1,
                                                    y: 0.5,
                                                }}
                                                end={{
                                                    x: 0,
                                                    y: 0.5,
                                                }}
                                                style={[styles.actionOverlay]}
                                                colors={[
                                                    'rgba(11, 24, 33, 0.15)',
                                                    'rgba(11, 24, 33, 0)',
                                                ]}
                                            />
                                        )
                                    )
                                }}
                            </Pressable>
                        )}
                    </Column>

                    <View style={styles.container}>
                        <Column spacing={32} fill>
                            <Column spacing={8} fill alignX="center">
                                <Column spacing={8} fill alignY="center">
                                    {!isFirstSlide && (
                                        <Pressable
                                            style={[
                                                styles.storyNextPreviousButton,
                                                styles.storyPreviousSlideButton,
                                            ]}
                                            onPress={() => {
                                                setCurrentSlide(
                                                    (state) => state - 1
                                                )
                                            }}
                                        />
                                    )}
                                    {!isLastSlide && (
                                        <Pressable
                                            style={[
                                                styles.storyNextPreviousButton,
                                                styles.storyNextSlideButton,
                                            ]}
                                            onPress={() => {
                                                setCurrentSlide(
                                                    (state) => state + 1
                                                )
                                            }}
                                        />
                                    )}
                                    <Text
                                        align="center"
                                        variant="title2"
                                        weight="semi_bold"
                                        color="textPrimary"
                                    >
                                        {page.title}
                                    </Text>
                                    {page.subtitle && (
                                        <Text
                                            variant="footnote"
                                            color="textSecondary"
                                            align="center"
                                        >
                                            {page.subtitle}
                                        </Text>
                                    )}
                                </Column>
                                {stories.length > 1 && (
                                    <Row spacing={0} alignX="center">
                                        <ProgressBar
                                            stories={stories}
                                            currentSlide={currentSlide}
                                            animationTimeMs={SHOW_SLIDE_FOR_MS}
                                            onMsg={(msg) => {
                                                switch (msg.type) {
                                                    case 'on_progress_bar_clicked':
                                                        setCurrentSlide(
                                                            msg.step
                                                        )
                                                        break
                                                    default:
                                                        notReachable(msg.type)
                                                }
                                            }}
                                        ></ProgressBar>
                                    </Row>
                                )}
                            </Column>
                            <Actions direction="row" variant="default">
                                <Button
                                    variant="secondary"
                                    size="regular"
                                    onClick={() => {
                                        actions.secondary.onClick()
                                    }}
                                >
                                    {actions.secondary.title}
                                </Button>
                                <Button
                                    variant="primary"
                                    size="regular"
                                    onClick={() => {
                                        actions.primary.onClick()
                                    }}
                                >
                                    {actions.primary.title}
                                </Button>
                            </Actions>
                        </Column>
                    </View>
                </View>
            </GestureDetector>
        </Screen>
    )
}
