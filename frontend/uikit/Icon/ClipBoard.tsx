import React from 'react'
import { Path } from 'react-native-svg'

import { SvgIcon } from '@zeal/uikit/SvgIcon'

import { Color, colors } from '../colors'

type Props = {
    color?: Color
    size: number
}

export const ClipBoard = ({ size, color }: Props) => {
    return (
        <SvgIcon
            color={color && colors[color]}
            viewBox="0 0 32 32"
            width={size}
            height={size}
        >
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11 6.66667C11 4.64162 12.6416 3 14.6667 3H17.3333C19.3584 3 21 4.64162 21 6.66667V9.33333C21 9.88562 20.5523 10.3333 20 10.3333H12C11.4477 10.3333 11 9.88562 11 9.33333V6.66667ZM14.6667 5C13.7462 5 13 5.74619 13 6.66667V8.33333H19V6.66667C19 5.74619 18.2538 5 17.3333 5H14.6667Z"
                fill="currentColor"
            />
            <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.64924 6.57169C8.8178 6.49088 9.00495 6.6195 9.00461 6.80644L9 9.33334C9 10.9902 10.3431 12.3333 12 12.3333H20C21.6569 12.3333 23 10.9902 23 9.33334V6.80568C23 6.61895 23.187 6.49067 23.3554 6.57139C24.9355 7.32893 26 8.94238 26 10.7806V24.7665C26 27.0475 24.3009 28.9713 22.0374 29.2533C18.0279 29.7526 13.9721 29.7526 9.96263 29.2533C7.69908 28.9713 6 27.0475 6 24.7665V10.7806C6 8.94262 7.06881 7.32935 8.64924 6.57169ZM20 16C20.5523 16 21 16.4477 21 17C21 17.5523 20.5523 18 20 18H12C11.4477 18 11 17.5523 11 17C11 16.4477 11.4477 16 12 16H20ZM18.6667 20C19.219 20 19.6667 20.4477 19.6667 21C19.6667 21.5523 19.219 22 18.6667 22H12C11.4477 22 11 21.5523 11 21C11 20.4477 11.4477 20 12 20H18.6667Z"
                fill="currentColor"
            />
        </SvgIcon>
    )
}
