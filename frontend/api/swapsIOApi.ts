import { uuid } from '@zeal/toolkit/Crypto'
import { joinURL } from '@zeal/toolkit/URL/joinURL'
import * as Web3 from '@zeal/toolkit/Web3'

import { processFetchFailure, processFetchResponse } from './interceptors'

export const SWAPSIO_BASE_URL = 'https://api.prod.swaps.io/api/v0'

export type Paths = {
    get: Record<`/swaps/${string}`, { query: undefined }> &
        Record<`/swaps/${string}/data`, { query: undefined }> &
        Record<`/swaps/${string}/manual_receive`, { query: undefined }> &
        Record<
            `/quote`,
            {
                query: {
                    from_chain_id: string
                    to_chain_id: string
                    from_token_address: Web3.address.Address
                    to_token_address: Web3.address.Address
                    from_amount: string
                }
            }
        > &
        Record<
            `/users/${string}/swaps`,
            {
                query: {
                    limit?: number
                    cursor?: string | null
                    signed_only?: boolean
                }
            }
        >

    post: Record<
        `/swaps`,
        {
            body: {
                from_chain_id: string
                to_chain_id: string
                from_token_address: Web3.address.Address
                to_token_address: Web3.address.Address
                from_amount: string
                from_actor: Web3.address.Address
                from_actor_receiver: Web3.address.Address
            }
            query: undefined
        }
    > &
        Record<
            `/swaps/${string}/submit`,
            {
                query: undefined
                body: {
                    signature?: `0x${string}`
                }
            }
        >
}

export const get = <T extends keyof Paths['get']>(
    path: T,
    params: { query?: Paths['get'][T]['query'] }, // TODO @resetko-zeal fix query is not mandatory even if its there in Paths
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(SWAPSIO_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {
            'X-Request-Id': `zeal:${uuid()}`,
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'GET', response, url })
        )
        .then((response) => response.json())
}

export const post = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: Paths['post'][T]['query']
        body: Paths['post'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(SWAPSIO_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'POST',
        body: JSON.stringify(params.body),
        headers: {
            'Content-Type': 'application/json',
            'X-Request-Id': `zeal:${uuid()}`,
        },
        signal,
    })
        .catch((error) => processFetchFailure({ error }))
        .then((response) =>
            processFetchResponse({ params, method: 'POST', response, url })
        )
        .then((response) => response.json())
}
