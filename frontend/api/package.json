{"name": "@zeal/api", "version": "0.0.1", "license": "UNLICENSED", "private": true, "devDependencies": {"@types/node": "20.11.29", "@zeal/eslint-config": "workspace:*", "eslint": "8.3.0", "typescript": "5.5.3"}, "scripts": {"lint": "yarn eslint --cache --cache-location ../../.eslintcache/api.cache --max-warnings 0 ."}, "dependencies": {"@zeal/toolkit": "workspace:*", "axios": "0.27.2", "chrome-types": "0.1.254"}}