import { createHash, randomBytes } from 'crypto'

import {
    failure,
    object,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import { base64Encode, base64UrlEncode } from '@zeal/toolkit/String/base64'

import { post } from 'src/api/monerium'

import {
    MONERIUM_ZEAL_CLIENT_ID,
    MONERIUM_ZEAL_REDIRECT_URI,
} from '../constants'

export type MoneriumAuthInfo = {
    message: string
    signature: string
}

export type AuthCode = {
    code: string
    codeVerifier: string
}

export const fetchAuthCode = async ({
    authInfo,
}: {
    authInfo: MoneriumAuthInfo
}): Promise<AuthCode> => {
    const codeVerifier = base64UrlEncode(base64Encode(randomBytes(64)))
    const codeChallenge = base64UrlEncode(
        createHash('sha256').update(codeVerifier).digest().toString('base64')
    )

    return post('/auth', {
        body: {
            client_id: MONERIUM_ZEAL_CLIENT_ID,
            code_challenge: codeChallenge,
            code_challenge_method: 'S256',
            authentication_method: 'siwe',
            message: authInfo.message,
            signature: authInfo.signature,
            redirect_uri: MONERIUM_ZEAL_REDIRECT_URI,
        },
    }).then(async (res) => {
        const code = success(res.headers)
            .andThen((headers) => {
                const location = headers.get('Location')
                return location
                    ? success(location)
                    : failure({ type: 'cannot get Location' })
            })
            .andThen((location) => {
                const locationURL = new URL(location)

                const code = locationURL.searchParams.get('code')
                const error = locationURL.searchParams.get('error')
                const errorDescription =
                    locationURL.searchParams.get('error_description')

                return error
                    ? failure({
                          type: 'auth_code_fetch_error',
                          error,
                          errorDescription,
                      })
                    : code
                      ? string(code)
                      : failure({ type: 'missing code from auth response' })
            })
            .getSuccessResultOrThrow('Failed to parse monerium auth response')

        return {
            code,
            codeVerifier,
        }
    })
}

export const parseMoneriumAuthInfo = (
    input: unknown
): Result<unknown, MoneriumAuthInfo> =>
    object(input).andThen((obj) =>
        shape({
            message: string(obj.message),
            signature: string(obj.signature),
        })
    )
