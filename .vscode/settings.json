{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[solidity]": {"editor.defaultFormatter": "NomicFoundation.hardhat-solidity", "editor.formatOnSave": true}, "solidity.formatter": "forge", "git.detectSubmodules": false, "terminal.integrated.automationProfile.linux": {"path": "/bin/zsh", "icon": "play", "args": ["-l", "-i"]}, "terminal.integrated.profiles.linux": {"bash": null, "zsh": {"path": "/bin/zsh", "icon": "star"}}, "terminal.integrated.profiles.osx": {"bash": null, "zsh": {"path": "/bin/zsh", "icon": "star"}}, "terminal.integrated.automationProfile.osx": {"path": "/bin/zsh", "icon": "play", "args": ["-l", "-i"]}, "typescript.tsdk": "node_modules/typescript/lib", "mcp.server.id": "linear-zeal"}