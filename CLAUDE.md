# AI Agent Guide for Monorepo

**All coding standards and development practices are defined in `.cursor/rules/` directory:**

-   **`.cursor/rules/codestyle.mdc`** - Core coding standards (always applied)
-   **Additional `.mdc` files** in subdirectories provide domain-specific guidance

## AI Development Context

This monorepo uses advanced TypeScript patterns and functional programming principles. When working with AI tools:

1. **Always reference** `.cursor/rules/` directory first
2. **Follow** the established patterns in `.cursor/rules/codestyle.mdc`
