---
description: Guidelines for classifying, parsing, and reporting application errors. Use when implementing or refactoring error handling logic or creating new error types.
alwaysApply: false
---

## Error Domain Overview

The Error domain provides a structured approach to handling all application errors through classification, parsing, and reporting.
All errors flow through a centralized system that categorizes them into three main types:

1. **Unknown Errors** - Unrecognized errors that fall back to `UnknownError`
2. **Known Expected Errors** - Anticipated errors with specific handling (user-facing)
3. **Known Unexpected Errors** - Recognized errors that indicate system issues (reported to monitoring)

## Error Classification System

### Unknown Errors

-   **Type**: `unknown_error`
-   **Class**: `UnknownError`
-   **Purpose**: Catch-all for unrecognized errors
-   **Handling**: Always reported to monitoring with original error context
-   **Creation**: Automatically created when `parseAppError()` fails to match any known pattern

### Known Expected Errors

These are business logic errors that users might encounter during normal operation:

-   **Connectivity Errors**: `connectivity_error` - Network/connection issues
-   **User Input Errors**: Validation failures, invalid formats
-   **Authentication Errors**: Biometric failures, keychain issues
-   **Business Logic Errors**: Duplicate orders, insufficient funds, etc.
-   **User Cancellations**: Biometric prompt cancelled, operation aborted

**Characteristics**:

-   Have specific `type` fields for identification
-   Often have user-friendly error messages
-   May not be reported to monitoring (e.g., connectivity errors)
-   Include contextual information for user guidance

### Known Unexpected Errors

These indicate system problems that shouldn't occur during normal operation:

-   **RPC Errors**: Blockchain communication failures
-   **HTTP Errors**: API failures with status codes
-   **Bundler Errors**: Smart wallet operation failures
-   **Third-party Service Errors**: External service failures
-   **System Errors**: Unexpected application state

**Characteristics**:

-   Always reported to monitoring with full context
-   Include technical details for debugging
-   May have fallback user messages
-   Often include stack traces and request/response data

## Error Domain Structure

### Core Files

-   **`AppError.ts`** - Central error type definitions and base classes
-   **`parseAppError.ts`** - Main error parsing orchestrator
-   **`captureAppError.ts`** - Error reporting and monitoring logic
-   **`Domain-Specific Errors`** - Organized in `domains/` subdirectories

## Error Parsing Patterns

### Main Parser Orchestration

The `parseAppError()` function tries parsers in order of specificity:

1. **Signal/Abort Errors** - Highest priority
2. **Domain-Specific Errors** - Grouped by service/component
3. **Generic HTTP Errors** - Lower priority, more generic
4. **Connectivity Errors** - Lowest priority fallback
5. **Unknown Error** - Final fallback when all parsing fails

### Parser Organization Rules

-   **Group related parsers** using nested `oneOf()` calls
-   **Order by specificity** - most specific parsers first
-   **Keep generic parsers last** (HttpError, ConnectivityError)
-   **Follow Result type patterns** from `result-type.mdc`

## Error Handling Patterns

### Error Capture and Reporting

** Error Handling in useEffect**:

```typescript
const captureErrorOnce = useCaptureErrorOnce()

useEffect(() => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'loading':
            break
        case 'subsequent_failed':
            captureErrorOnce(pollable.error)
            break
        case 'error':
            // Usually handled in render with AppErrorPopup
            break
        default:
            return notReachable(pollable)
    }
}, [captureErrorOnce, pollable])
```

**Error Handling in Render**:

```typescript
switch (loadable.type) {
    case 'loading':
        return <LoadingComponent />

    case 'error':
        return (
            <>
                <LoadingComponent />
                <AppErrorPopup
                    error={parseAppError(loadable.error)}
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'try_again_clicked':
                                setLoadable({
                                    type: 'loading',
                                    params: loadable.params,
                                })
                                break
                            default:
                                notReachable(msg)
                        }
                    }}
                />
            </>
        )

    case 'loaded':
        return <LoadedComponent data={loadable.data} />
}
```

### Error Display Components

-   **`AppErrorPopup`** - Modal error display
-   **`AppErrorBanner`** - Inline error banner
-   **`AppErrorListItem`** - List item error display

### Error Boundary Integration

```typescript
componentDidCatch(error: unknown, info: { componentStack: string }) {
    captureAppError(parseAppError(error), {
        source: 'error_boundary',
        extra: { componentStack: info.componentStack }
    })
}
```

## Error Creation Rules

### Required Fields

Every error must have:

-   **`type`** field for identification and Sentry grouping
-   **Consistent naming** following `snake_case` convention

### Error Class Guidelines

-   **Extend Error class** if you need stack traces or messages
-   **Use plain objects** for simple discriminated union types

### Error Type Definitions

```typescript
// Simple discriminated union
export type SimpleError = { type: 'simple_error_type' }

// Error class with context
export class ComplexError extends Error {
    type: 'complex_error_type' = 'complex_error_type' as const
    name = 'ComplexError' as const

    constructor(public context: SomeContext) {
        super(`Complex error: ${context.message}`)
    }
}
```

## Best Practices

### Error Message Guidelines

-   **Provide actionable messages** for user-facing errors
-   **Include technical details** for debugging unexpected errors
-   **Maintain consistency** in error message formatting
-   **Avoid exposing sensitive information** in error messages
