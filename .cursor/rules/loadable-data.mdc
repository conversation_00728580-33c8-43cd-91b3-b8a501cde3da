---
description: Guidelines for managing asynchronous data operations using the LoadableData system. Covers hooks like useLoadableData and usePollableData. Use when implementing features that require fetching or reloading data.
alwaysApply: false
---

## Main LoadableData Types

-   **LoadableData<T, P, E>** - Basic async operation that starts immediately
-   **ReloadableData<T, P, E>** - Async operation that can be restarted and maintains previous data during reload
-   **LazyLoadableData<T, P, E>** - Async operation that starts on demand
-   **LazyReloadableData<T, P, E>** - Reloadable operation that starts on demand
-   **PollableData<T, P, E>** - Automatically repeating async operation
-   **LoadedReloadableData<T, P, E>** - Reloadable operation that starts with initial data

## Hook Usage Patterns

-   **Basic LoadableData Hook**:

    ```typescript
    const [loadable, setLoadable] = useLoadableData(fetchFunction, {
        type: 'loading',
        params: {
            /* initial params */
        },
    })
    ```

-   **ReloadableData with Accumulation**:

    ```typescript
    const [loadable, setLoadable] = useReloadableData(
        fetchFunction,
        initialState,
        {
            accumulate: (newData, prevData) => ({ ...prevData, ...newData }),
        }
    )
    ```

-   **PollableData with Stop Condition**:

    ```typescript
    const [loadable, setLoadable] = usePollableData(
        fetchFunction,
        initialState,
        {
            pollIntervalMilliseconds: 5000,
            stopIf: (state) => state.type === 'loaded' && state.data.isComplete,
        }
    )
    ```

-   **LazyLoadableData**:
    ```typescript
    const [loadable, setLoadable] = useLazyLoadableData(
        fetchFunction,
        { type: 'not_asked' } // default initial state
    )
    ```

## State Handling

-   **Exhaustive Switch Statements** - Always use exhaustive switch statements with `notReachable()` in the default case:

    ```typescript
    switch (loadable.type) {
        case 'loading':
            return <LoadingSpinner />
        case 'loaded':
            return <DataDisplay data={loadable.data} />
        case 'error':
            return <ErrorMessage error={loadable.error} />
        default:
            return notReachable(loadable)
    }
    ```

-   **Error Handling in useEffect** - Handle errors in useEffect for side effects, if there is no error handling in render:

    ```typescript
    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'loaded':
                break
            case 'error':
                captureError(loadable.error)
                break
            default:
                return notReachable(loadable)
        }
    }, [loadable])
    ```

-   **Conditional Initial States** - Initialize with cached data when available:
    ```typescript
    const [loadable] = useLoadableData(
        fetchPortfolio,
        portfolio
            ? {
                  type: 'loaded',
                  params,
                  data: portfolio,
              }
            : {
                  type: 'loading',
                  params,
              }
    )
    ```

## Common Patterns

-   **DataLoader Component Pattern** - Create dedicated DataLoader components that handle async state and delegate to presentation components:

    ```typescript
    export const DataLoader = ({ onMsg, foo, bar }) => {
        const [loadable, setLoadable] = useLoadableData(fetchData, {
            type: 'loading',
            params: { foo, bar },
        })

        switch (loadable.type) {
            case 'loading':
                return <Skeleton onMsg={onMsg} />
            case 'loaded':
                return <PresentationComponent data={loadable.data} onMsg={onMsg} />
            case 'error':
                return (
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: loadable.params,
                                    })
                                    break
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                )
            default:
                return notReachable(loadable)
        }
    }
    ```

-   **Fetch Function Signature** - All fetch functions should accept parameters with optional AbortSignal:
    ```typescript
    const fetchData = async (
        params: P & { signal?: AbortSignal }
    ): Promise<T> => {
        // Implementation with abort signal support
    }
    ```

## Type Selection Guidelines

-   **Choose LoadableData when:**

    -   Simple one-time async operation
    -   No need to maintain previous data during reload
    -   Operation starts immediately

-   **Choose ReloadableData when:**

    -   Need to reload/refresh data
    -   Want to maintain previous data during reload
    -   Need to handle subsequent failures gracefully
    -   Pagination

-   **Choose PollableData when:**

    -   Need automatic periodic updates
    -   Want to stop polling based on conditions
    -   Building real-time data displays

-   **Choose LoadedReloadableData when:**

    -   Same as ReloadableData but operation starts with initial data, so there is no loading state and data is always available

-   **Choose LazyLoadableData when:**

    -   Same as LoadableData but operation should start on user action

-   **Choose LazyReloadableData when:**
    -   Same as ReloadableData but operation should start on user action

## Best Practices

-   **State Management:**

    -   Use discriminated union pattern with exhaustive switch to handle all possible states explicitly

-   **Error Handling:**

    -   Handle errors in useEffect or in rendering when components from Error domain are used
    -   Consider retry mechanisms for failed states

-   **Performance:**
    -   Use accumulate option for data merging during reloads
    -   Implement proper abort signal handling in fetch functions
    -   Consider caching strategies for frequently accessed data
