---
alwaysApply: true
---

## Monorepo Package Isolation Rules

1. **UI Kit (`@zeal/uikit`)**

    - Cannot import from Domains
    - Can import from Toolkit
    - Pure UI components only
    - No business logic

2. **Toolkit (`@zeal/toolkit`)**

    - Cannot import from any other packages, except 3rd party packages
    - Pure utilities and framework code
    - No business logic

3. **Domains (`@zeal/domains`)**
    - Can import from toolkit
    - Can import from uikit
    - Can import from other domains

## Domain Internals Purposes

-   **`constants`** - Domain-level constants and configuration values
-   **`/domains`** - Sub-domains that are part of the larger domain context
-   **`/features`** - Complete user-facing features with their own internal structure
-   **`/api`** - External API calls and data fetching logic. Generally anything which is asynchronous.
-   **`/api/fixtures`** - Test fixtures and mock data for API functions
-   **`/components`** - Reusable React components specific to the domain
-   **`/helpers`** - Pure utility functions with no side effects
-   **`/hooks`** - React hooks specific to the domain
-   **`/parsers`** - Data validation and parsing functions using the Result type system

**Note**: Only these internal folders/files are allowed.

## Import Rules

-   Prefer `@zeal/toolkit` utilities over external libraries unless an exception is well-documented
-   Import specific items, not entire folders
-   `@zeal/domains/DomainName` format required when importing from other domains
-   Don't use `../` between packages or domains
-   Import features by name, not their internals

## Naming Conventions

-   **Domain Names**: PascalCase (e.g., `Currency`, `TransactionRequest`, `KeyStore`)
-   **File Names**: camelCase for TypeScript files, PascalCase for React components
