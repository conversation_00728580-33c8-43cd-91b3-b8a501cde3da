---
description: Guidelines for using the Result type for data validation and parsing. Covers functional patterns for handling operations that can succeed or fail. Use when writing data parsers or validation logic.
alwaysApply: false
---

## Result Type Overview

The Result type is a functional programming pattern for handling operations that can fail.
It represents either a `Success<T>` containing data or a `Failure<E>` containing an error reason.

## Core Result Methods

### Chaining Operations

-   **`andThen()`** - Chain operations that can fail, short-circuits on first failure
-   **`map()`** - Transform success values, preserves failures
-   **`tap()`** - Perform side effects on success values without changing the result
-   **`mapError()`** - Transform error values, preserves successes

### Extracting Values

-   **`getSuccessResult()`** - Returns data or undefined
-   **`getFailureReason()`** - Returns error reason or undefined
-   **`getSuccessResultOrThrow()`** - Returns data or throws UnexpectedResultFailureError

## Basic Validators

### Primitive Types

-   **`string(value)`** - Validates string type
-   **`number(value)`** - Validates number type (excludes NaN)
-   **`boolean(value)`** - Validates boolean type
-   **`array(value)`** - Validates array type
-   **`object(value)`** - Validates object type (excludes arrays and null)

### Nullability

-   **`nullable(value)`** - Accepts null or undefined, converts to null
-   **`required(value)`** - Rejects null or undefined

### String Validators

-   **`nonEmptyString(value)`** - String that is not empty
-   **`email(value)`** - Valid email format
-   **`stringLength(value, length)`** - Exact string length
-   **`minStringLength(value, min)`** - Minimum string length
-   **`maxStringLength(value, max)`** - Maximum string length
-   **`numberString(value)`** - String that represents a number

### URL Validators

-   **`parseHttpsUrl(value)`** - HTTPS URL validation
-   **`parseHttpOrHttpsUrl(value)`** - HTTP or HTTPS URL validation

### Array Validators

-   **`nonEmptyArray(value)`** - Array with at least one element
-   **`emptyArray(value)`** - Empty array validation
-   **`minArrayLength(value, min)`** - Minimum array length

### Number Validators

-   **`minAmount(value, min)`** - Minimum number value
-   **`maxAmount(value, max)`** - Maximum number value
-   **`rangeInt(value, {start, end})`** - Number within range
-   **`bigint(value)`** - BigInt conversion

### Binary Validators

-   **`arrayBuffer(value)`** - ArrayBuffer validation
-   **`uint8Array(value)`** - Uint8Array validation

## Pattern Matching

-   **`match(value, matchValue)`** - Exact value matching for literals
-   **`matchRegExp(value, regExp)`** - Regular expression matching

## Combinators

### Union Types

-   **`oneOf(value, [result1, result2, ...])`** - Try multiple parsers, return first success

### Object Validation

-   **`shape({key1: result1, key2: result2})`** - Validate object structure with typed keys
-   **`merge([result1, result2])`** - Merge multiple object results
-   **`recordStrict(record, {keyParser, valueParser})`** - Parse known record with typed keys/values

### Array Processing

-   **`arrayOf(value, parser)`** - Parse array where all elements must succeed
-   **`combine([result1, result2, ...])`** - All results must succeed, returns array
-   **`safeArrayOf(value, parser)`** - Parse array, keep only successful elements

## Common Usage Patterns

### Basic Object Parsing

```typescript
const parseUser = (input: unknown): Result<unknown, User> =>
    object(input).andThen((obj) =>
        shape({
            name: string(obj.name),
            email: email(obj.email),
            age: number(obj.age),
        })
    )
```

### Optional Fields

```typescript
shape({
    name: string(obj.name),
    avatar: nullableOf(obj.avatar, string), // string | null
})
```

### Array Processing

```typescript
// All elements must be valid
protocols: array(obj.protocols).andThen((arr) =>
    combine(arr.map((item) => parseProtocol(item)))
),

// Keep only valid elements
validItems: safeArrayOf(obj.items, parseItem),
```

### Union Types with oneOf

```typescript
// Simple enum-like union
status: oneOf(obj.status, [
    match(obj.status, 'pending' as const),
    match(obj.status, 'completed' as const),
    match(obj.status, 'failed' as const),
]),

// Transaction state union
state: oneOf(obj, [
    shape({ type: match(obj.type, 'pending') }),
    shape({
        type: match(obj.type, 'settled'),
        clearedAt: number(obj.clearedAt),
    }),
]),
```

### Chaining Validations

```typescript
address: string(obj.address).andThen(parseAddress),
timestamp: number(obj.timestamp).map((ts) => ts * 1000),
```

## Best Practices

### Parser Function Structure

-   Use `object().andThen()` and `shape()` for validation, following functional chaining pattern
-   Use `oneOf()` for union types rather than complex conditional logic
-   Use `nullableOf()` for optional fields that can fallback to null
-   Use `combine()` when all array elements must be valid
-   Use `safeArrayOf()` when you want to keep only valid array elements

### Type Safety

-   Always specify proper types for Result<E, T>
-   Use `match()` for literal value validation

### Parsing Logic Organization

-   Keep parsers synchronous and pure
-   Avoid throwing error in render
-   Keep parsing logic inline within `oneOf` arrays rather than splitting into separate helper functions
