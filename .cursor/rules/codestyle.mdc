---
alwaysApply: true
---

## Core principles

-   Do not use deprecated functions or types unless it's absolutely necessary
-   When dealing with validation or parsing follow `result-type.mdc` rules
-   When dealing with async operations follow `loadable-data.mdc` rules
-   When dealing with errors follow `error-handling.mdc` rules

## Linting & Formatting

-   Run `yarn lint --fix` to automatically fix minor issues and auto-format (prettier is used)
-   Manually address any remaining lint errors or warnings

## Discriminated Unions

-   Always use an exhaustive switch statement when branching on a discriminated union's `type`
-   Include a `default` case that calls `notReachable()` with the entire object
-   Do not perform partial checks on a union (e.g., `object.type === 'someType'`) as this risks missing unhandled variants
-   Do not create reusable helper functions that narrow unions to boolean using if logic

## Functional Composition

-   Favor method chaining (`map()`, `filter()`, `tap()`) over imperative loops and nested conditionals
-   Split complex operations into smaller functions for clarity

## Parameter Optionality Guidelines

-   Core dependencies should be required, not optional: if a parameter is necessary for proper functionality, avoid marking it as `?`
-   Keep parameter requirements consistent across call chains: do not arbitrarily make a parameter optional in one place and required in another
-   Avoid silent fallbacks for missing function parameters/props: do not default to minimal behavior when a critical dependency is absent

## Declarative IIFE Patterns

-   For transformations that require branching logic but produce a single result, use an Immediately Invoked Function Expression (IIFE)
-   IIFEs keep variable scope tight and clarify the transformation's purpose
-   Use IIFEs especially when multiple conditional branches feed into one final return value

## TypeScript

-   Use strict TypeScript with ESNext target
-   Use typesafe toolkit alternatives to Object.keys and Object.values

## Consistency & Codebase References

-   Before adding a new feature, locate similar references in the codebase
-   Adhere to established naming, file structure, and patterns
-   If uncertain, raise questions in code review or document your reasoning for any deviations

## Best Practices

-   Pick the most concise approach that clearly communicates intent
-   Keep chaining and transformations as simple and readable as possible
-   Use simple, clear expressions over complex nested conditionals
-   When handling optional values, use nullish coalescing with sensible defaults
