### ⚠️ TICKET TITLE INSTRUCTIONS ⚠️

**Format:** `[Type] Clear, user-observable outcome`
_Example:_ `[Feature] Add biometric unlock to iOS wallet`

**Note:** This section is for renaming the ticket itself and should NOT be included in the description below.

## 👤 Zealot Story

**As a** [persona], **I want** [action], **So that** [benefit].

---

## 1️⃣ Context / Why (2–3 sentences)

Current pain and who feels it.

## 2️⃣ Goal

Single-sentence end-state.

## 3️⃣ Scope

**In**

-   …

**Out**

-   …

---

## 4️⃣ Acceptance Criteria (bullet BDD)

-   [ ] **Given** wallet is locked, **When** user presents valid Face ID, **Then** wallet unlocks.
-   [ ] **Given** Face ID fails 3×, **Then** app falls back to PIN entry.

---

## 🔗 Dependencies

-   [ ] **Blocked by:** [ZEAL-3784](https://linear.app/zeal/issue/ZEAL-3784/investigate-failing-transactions) – …
-   [ ] **Related to:** [ZEAL-4068](https://linear.app/zeal/issue/ZEAL-4068/fix-wxdai-balance-after-unwrap) – …

---

## ❓ Outstanding Questions

-   [ ] …
-   [ ] …

---

## 5️⃣ Tech Notes

Implementation hints, feature flags, migrations, blockers.

## 6️⃣ Designs & References

-   Figma: <https://figma.com/file/...>
-   API spec: <https://api.example.com/openapi.yaml>
-   Related doc: <https://docs.google.com/document/d/…>

---

## 📊 Metrics & Success

-   KPI 1 target …
-   KPI 2 target …
